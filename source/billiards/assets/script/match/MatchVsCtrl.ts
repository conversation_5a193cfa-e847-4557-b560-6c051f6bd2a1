import { _decorator, Component, Node, Label, sp } from 'cc';
import Global from '../core/data/Global';
import Utils from '../core/tools/Utils';
import { MatchSeat } from './MatchSeat';
import { GameUtil } from '../core/tools/GameUtil';
const { ccclass, property, menu } = _decorator;

/**
 * 对战控制器组件
 * @class MatchVsCtrl
 * @description 负责处理玩家匹配过程中的对战界面显示、动画效果和状态管理
 */
@ccclass('MatchVsCtrl')
@menu('match/MatchVsCtrl')
export class MatchVsCtrl extends Component {
    // UI组件引用
    /** 玩家1（自己）的座位组件 */
    @property(MatchSeat)
    private MatchSeat1: MatchSeat = null;

    /** 玩家2（对手）的座位组件 */
    @property(MatchSeat)
    private MatchSeat2: MatchSeat = null;

    /** VS动画节点 */
    @property(Node)
    private matchVsAni: Node = null;

    /** 关闭按钮节点 */
    @property(Node)
    private btnClose: Node = null;

    /** 下注金额显示标签 */
    @property(Label)
    private lbBet: Label = null;

    /** 奖励金额显示标签 */
    @property(Label)
    private lbRank: Label = null;

    // 私有成员变量
    /** 是否正在匹配中 */
    private _isMatching: boolean = false;
    /** 当前下注金额 */
    private _currentBet: number = 0;
    /** 匹配超时计时器 */
    private _matchTimer = null;
    /** 是否已匹配成功 */
    private _isMatchSuccess: boolean = false;

    /**
     * 组件加载时的初始化
     * @description 注册金币变化事件监听
     */
    onLoad() {
        GameUtil.log("【MatchVsCtrl】组件加载");
        // EventMgr.addEventListener(EnumEvent.COIN_CHANGED, this._updateCoin, this);
    }

    /**
     * 初始化匹配界面
     * @param {number} currentBet - 当前下注金额
     * @description 初始化座位信息和下注信息
     */
    public initMatch(currentBet: number) {
        GameUtil.log(`【MatchVsCtrl】初始化匹配界面, 当前下注金额: ${currentBet}`);
        this._isMatchSuccess = false;
        this._initSeats();
        this._initBetInfo(currentBet);
    }

    /**
     * 初始化座位信息
     * @description 初始化双方座位并设置玩家信息
     */
    private _initSeats() {
        [this.MatchSeat1, this.MatchSeat2].forEach((seat, index) => {
            if (!seat) {
                GameUtil.log(`【MatchVsCtrl】警告: 座位${index+1}组件不存在`);
                return;
            }
            seat.init(index);
        });

        if (this.MatchSeat1) {
            GameUtil.log(`【MatchVsCtrl】设置玩家1信息: ${Global.userInfo.nickName}: ${Global.userId}`);
            this.MatchSeat1.setPlayer(Global.userId, Global.userInfo.faceUrl, Global.userInfo.nickName);
        }

        if (this.MatchSeat2) {
            this.MatchSeat2.reset();
        } else {
            GameUtil.log(`【MatchVsCtrl】警告: 玩家2座位组件不存在`);
        }

        this.startMatching(true);
    }

    /**
     * 初始化下注信息
     * @param {number} currentBet - 当前下注金额
     * @description 设置下注金额和奖励金额显示
     */
    private _initBetInfo(currentBet: number) {
        if (!currentBet || currentBet <= 0) {
            GameUtil.log(`【MatchVsCtrl】警告: 无效的下注金额 ${currentBet}, 尝试使用默认值`);
            // 如果下注金额无效，尝试使用有效下注列表中的第一个值
            const effectiveBetsList = Global.getEffectiveBetsList();
            if (effectiveBetsList && effectiveBetsList.length > 0) {
                currentBet = effectiveBetsList[0];
                GameUtil.log(`【MatchVsCtrl】使用默认下注金额: ${currentBet}`);
            } else {
                currentBet = 0;
            }
        }

        this._currentBet = currentBet;
        // 确保下注金额不会随着金币更新而变化
        this._updateBetAmount(currentBet);

        // 确保recycleBonus有值
        const recycleBonus = Global.recycleBonus || 1;
        const rewardAmount = currentBet * 2 * recycleBonus;
        GameUtil.log(`【MatchVsCtrl】设置奖励金额: ${rewardAmount}, 比例: ${recycleBonus}`);
        this._updateRewardAmount(rewardAmount);
    }

    /**
     * 更新下注金额显示
     * @param {number} amount - 下注金额
     */
    private _updateBetAmount(amount: number) {
        if (this.lbBet) {
            this.lbBet.string = Utils.formatNum(amount);
        } else {
            GameUtil.log(`【MatchVsCtrl】警告: 下注金额标签不存在`);
        }
    }

    /**
     * 更新奖励金额显示
     * @param {number} amount - 奖励金额
     */
    private _updateRewardAmount(amount: number) {
        if (this.lbRank) {
            this.lbRank.string = Utils.formatNum(amount);
        } else {
            GameUtil.log(`【MatchVsCtrl】警告: 奖励金额标签不存在`);
        }
    }

    /**
     * 更新金币显示
     * @param {number} num - 金币数量
     */
    private _updateCoin(num: number) {
        // 只更新下注金额，不改变当前存储的下注值
        this._updateBetAmount(this._currentBet);
    }

    /**
     * 开始/停止匹配
     * @param {boolean} isStart - 是否开始匹配
     * @description 控制匹配状态、动画和关闭按钮的显示
     */
    public startMatching(isStart: boolean) {
        // 如果已经匹配成功，不要重新开始匹配
        if (isStart && this._isMatchSuccess) {
            GameUtil.log(`【MatchVsCtrl】已匹配成功，忽略开始匹配请求`);
            return;
        }

        GameUtil.log(`【MatchVsCtrl】${isStart ? '开始' : '停止'}匹配`);
        this._isMatching = isStart;

        // 检查VS动画节点是否存在
        if (!this.matchVsAni) {
            GameUtil.log(`【MatchVsCtrl】警告: VS动画节点不存在`);
            return;
        }

        this.matchVsAni.active = isStart;
        this._clearMatchTimer();
        this._updateCloseButton(false);

        // 获取并检查骨骼动画组件
        const anim = this.matchVsAni.getComponent(sp.Skeleton);
        if (!anim) {
            GameUtil.log(`【MatchVsCtrl】警告: VS骨骼动画组件不存在`);
            return;
        }

        anim.clearTracks();
        if (isStart) {
            this._startMatchAnimation(anim);
            this._setCloseButtonTimer();
        }
    }

    /**
     * 清理匹配计时器
     * @description 清除匹配超时计时器
     */
    private _clearMatchTimer() {
        if (this._matchTimer) {
            clearTimeout(this._matchTimer);
            this._matchTimer = null;
            GameUtil.log(`【MatchVsCtrl】清理匹配超时计时器`);
        }
    }

    /**
     * 更新关闭按钮状态
     * @param {boolean} visible - 是否显示关闭按钮
     */
    private _updateCloseButton(visible: boolean) {
        if (this.btnClose) {
            this.btnClose.active = visible;
            if (visible) {
                GameUtil.log(`【MatchVsCtrl】显示匹配取消按钮`);
            }
        } else {
            GameUtil.log(`【MatchVsCtrl】警告: 关闭按钮节点不存在`);
        }
    }

    /**
     * 播放匹配动画
     * @param {sp.Skeleton} anim - 骨骼动画组件
     * @description 播放匹配过程中的VS动画
     */
    private _startMatchAnimation(anim: sp.Skeleton) {
        try {
            GameUtil.log(`【MatchVsCtrl】开始播放VS匹配动画`);
            setTimeout(() => {
                // 检查组件是否仍然有效
                if (!anim || !this.node || !this.node.isValid) {
                    GameUtil.log(`【MatchVsCtrl】动画播放已取消: 组件已销毁`);
                    return;
                }

                anim.setAnimation(0, 'idle_1', false);
                anim.setCompleteListener(() => {
                    // 再次检查组件有效性
                    if (!anim || !this.node || !this.node.isValid) {
                        return;
                    }
                    anim.setAnimation(0, 'idle_2', true);
                });
            }, 500);
        } catch (error) {
            GameUtil.log(`【MatchVsCtrl】播放匹配动画出错: ${error.message}`);
        }
    }

    /**
     * 设置关闭按钮显示计时器
     * @description 3秒后如果还在匹配中则显示关闭按钮
     */
    private _setCloseButtonTimer() {
        // 先清理之前的计时器
        this._clearMatchTimer();

        // 设置新的计时器
        this._matchTimer = setTimeout(() => {
            // 检查组件和匹配状态是否有效
            if (!this.node || !this.node.isValid) {
                return;
            }

            if (this._isMatching) {
                GameUtil.log(`【MatchVsCtrl】匹配超过3秒，显示取消按钮`);
                this._updateCloseButton(true);
            }
        }, 3000);

        GameUtil.log(`【MatchVsCtrl】设置3秒匹配超时计时器`);
    }

    /**
     * 匹配成功回调
     * @param {number} userId - 对手ID
     * @param {string} avatar - 对手头像
     * @param {string} nickname - 对手昵称
     * @description 设置对手信息并播放匹配成功动画
     */
    public onMatchSuccess(userId: number, avatar: string, nickname: string) {
        GameUtil.log(`【MatchVsCtrl】匹配成功，对手信息: ID=${userId}, 昵称=${nickname}`);
        // 清理计时器
        this._clearMatchTimer();
        // 更新匹配状态
        this._isMatching = false;
        this._isMatchSuccess = true;
        // 隐藏关闭按钮
        this._updateCloseButton(false);

        // 检查对手座位组件
        if (!this.MatchSeat2) {
            GameUtil.log(`【MatchVsCtrl】警告: 对手座位组件不存在`);
            return;
        }

        // 重置座位并设置对手信息
        this.MatchSeat2.reset();
        this.MatchSeat2.setPlayer(userId, avatar, nickname);
        // 播放匹配成功效果
        this.MatchSeat2._searchPlayerSuccessEffect();
    }

    /**
     * 取消匹配
     * @description 重置匹配状态和对手座位
     */
    public cancelMatching() {
        if (!this._isMatching) {
            GameUtil.log(`【MatchVsCtrl】当前不在匹配状态，忽略取消请求`);
            return;
        }

        GameUtil.log(`【MatchVsCtrl】取消匹配`);
        this._isMatching = false;
        this._isMatchSuccess = false;

        // 重置对手座位
        if (this.MatchSeat2) {
            this.MatchSeat2.reset();
        } else {
            GameUtil.log(`【MatchVsCtrl】警告: 取消匹配时对手座位组件不存在`);
        }
    }

    /**
     * 开始游戏
     * @param {Function} cb - 游戏开始后的回调函数
     * @description 延迟执行游戏开始回调
     */
    public gameStart(cb?: () => void) {
        GameUtil.log(`【MatchVsCtrl】开始游戏`);
        if (cb) {
            setTimeout(() => {
                if (this.node && this.node.isValid) {
                    GameUtil.log(`【MatchVsCtrl】执行游戏开始回调`);
                    cb();
                }
            }, 1000);
        }
    }

    /**
     * 组件销毁时的清理
     * @description 清理事件监听、计时器和动画资源
     */
    onDestroy() {
        GameUtil.log(`【MatchVsCtrl】组件销毁，清理资源`);
        // 取消事件监听
        // 更新状态
        this._isMatching = false;
        this._isMatchSuccess = false;
        // 清理计时器
        this._clearMatchTimer();

        // 清理动画资源
        try {
            [this.matchVsAni, this.MatchSeat1, this.MatchSeat2].forEach(node => {
                if (!node) return;
                const anim = node.getComponent(sp.Skeleton);
                if (anim) anim.clearTracks();
            });
        } catch (error) {
            GameUtil.log(`【MatchVsCtrl】清理动画资源出错: ${error.message}`);
        }

        // 清理座位组件
        [this.MatchSeat1, this.MatchSeat2].forEach(seat => {
            if (seat) seat.onDestroy();
        });
    }
}