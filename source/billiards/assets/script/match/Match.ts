import { _decorator, Node, Label, Prefab, v3, color, Widget, view, screen, SpriteFrame } from 'cc';
import Global from '../core/data/Global';
import i18n from '../core/tools/i18n';
import Utils from '../core/tools/Utils';
import { MatchSeat } from './MatchSeat';
import { MatchVsCtrl } from './MatchVsCtrl';
import { MatchBetCrtl } from './MatchBetCrtl';
import GameService from '../game/data/GameService';
import { UIView } from '../core/ui/UIView';
import { uiManager } from '../core/ui/UIManager';
import { UIID } from '../Main';
import { GameUtil } from '../core/tools/GameUtil';
import { EnumEvent, EventMgr } from '../core/event/EventManager';
import TipManager from '../game/manager/TipManager';
import { JsbBrid } from '../core/tools/JsbBridge';
import { GameNodePool } from '../game/manager/GameNodePool';
const { ccclass, property, menu } = _decorator;

/**
 * 匹配数据接口
 * @interface MatchData
 * @description 定义游戏匹配所需的所有参数
 */
interface MatchData {
    idx: number;           // 用户唯一标识ID
    gameId: number;        // 当前游戏的唯一标识
    gamePay: number;       // 本局游戏的下注金额
    playerNum: number;     // 本局游戏需要的玩家数量
    isPrivate: number;     // 是否为私人房间(0:否, 1:是)
    gameType: number;      // 游戏类型标识
    gameGroup: number;     // 游戏分组标识
    languageId: number;    // 游戏语言标识
    isCanRePlay: number;   // 是否允许重新开始(0:否, 1:是)
    msgID: number;         // 消息唯一标识
    version: string;       // 游戏客户端版本号
    pid: number;           // 平台标识ID
    source: number;        // 玩家来源渠道
    packageType: number;   // 游戏包类型
}

/**
 * 匹配界面主控制器
 * @class Match
 * @extends UIView
 * @description 负责处理游戏匹配流程的主要控制器，包括：
 * - 玩家匹配界面的显示和控制
 * - 下注界面的管理
 * - 倒计时处理
 * - 座位分配和管理
 * - 游戏开始流程控制
 */
@ccclass('Match')
export class Match extends UIView {
    // UI组件引用
    /** 座位预制体资源 */
    @property(Prefab)
    private pfSeat: Prefab = null;

    /** 对战界面控制器 */
    @property(MatchVsCtrl)
    private matchVsCtrl: MatchVsCtrl = null;

    /** 下注界面控制器 */
    @property(MatchBetCrtl)
    public matchBetCtrl: MatchBetCrtl = null;

    /** 关闭按钮节点 */
    @property(Node)
    private closeBtn: Node = null;

    @property(Label)
    private testLabel: Label = null;

    // 私有成员变量
    /** 座位容器节点 */
    private _ndBox: Node = null;
    /** 等待提示文本 */
    private _lblWaiting: Label = null;

    /** 座位实例数组 */
    private _seats: MatchSeat[] = [];
    /** 界面隐藏时的时间戳 */
    private _timeOnHide: number = 0;
    /** 当前倒计时剩余时间 */
    private _time: number = 0;
    /** 是否正在匹配中 */
    private _isMatching: boolean = false;
    /** 倒计时定时器引用 */
    private _countdownTimer: any = null;
    /** 上次点击开始按钮的时间戳 */
    private _lastClickTime: number = 0;

    /** 传入消息数据 */
    public openMsg: any;
    init(...arg) {
        this.UIid = UIID.Match;
        super.init(arg);

        if (Global.isArabic) {
            GameUtil.changAriboStyle(this.node);
            this.closeBtn.scale = v3(-1, 1, 1);
        }
    }
    /**
     * 界面打开时的初始化
     * @param {number} uid - 用户ID
     * @param {any} openMsg -传入消息数据  isMatch 是否直接匹配  coin 本局游戏下注金额
     * @description 初始化界面状态和游戏服务
     */
    public onOpen(uid: number, openMsg: any): void {
        let bView = uiManager.getUI(UIID.BilliardGame)
        bView && uiManager.close(bView);

        GameNodePool.instance.imgGameBg.node.active = false;
        GameUtil.loadResCb(GameNodePool.instance.imgBg, 'texture/match/match_bg', (v, spriteFrame: SpriteFrame) => {
            v && (GameNodePool.instance.imgBg.spriteFrame = spriteFrame);
        });

        this.openMsg = openMsg;
        GameUtil.log("【Match】界面打开" + (openMsg?.isMatch ? ", 直接匹配模式" : ""));
        GameService.instance.initMatchView(this);

        // 立即初始化界面，使用默认值或已有的服务端数据
        this.initInfo();

        // 在后台检查并更新下注信息
        if (Global.needsFetchBetsInfo()) {
            GameUtil.log("【Match】后台请求最新的下注金额配置");
            // 检查网络状态并获取下注金额配置
            if (!GameService.instance.socket || !Global.isconnect) {
                // 如果socket未连接，先连接socket
                GameService.instance.connectSocket(() => {
                    // socket连接成功后获取下注金额配置
                    GameService.instance.betsRequestWithCallback(() => {
                        this._checkDirectMatchAfterBetsUpdate();
                    });
                });
            } else {
                // socket已连接，直接获取下注金额配置
                GameService.instance.betsRequestWithCallback(() => {
                    this._checkDirectMatchAfterBetsUpdate();
                });
            }
        } else {
            // 已有下注金额配置，直接检查是否需要匹配
            this._checkDirectMatch();
        }
    }

    /**
     * 检查是否需要直接匹配
     * @private
     * @description 根据openMsg中的isMatch和coin参数决定是否直接开始匹配
     */
    private _checkDirectMatch(): void {
        // 如果没有传入openMsg或isMatch不为true，走正常逻辑
        if (!this.openMsg || !this.openMsg.isMatch) {
            this.initInfo();
            return;
        }

        GameUtil.log("【Match】进入直接匹配流程");

        // 确保MatchBetCtrl已初始化，以便_maxIndex等值正确设置
        if (this.matchBetCtrl) {
            this.matchBetCtrl._initInfo();
        }

        // 如果有指定coin参数，设置为下注金额，否则使用默认第一个金额
        let betAmount = this.openMsg.coin;
        const effectiveBetsList = Global.getEffectiveBetsList();
        if (!betAmount && effectiveBetsList && effectiveBetsList.length > 0) {
            betAmount = effectiveBetsList[0];
        }

        // 检查betAmount是否在有效下注列表中，如果存在则设置对应的_currentIndex
        let validBetAmount = betAmount;
        if (effectiveBetsList && effectiveBetsList.length > 0 && betAmount) {
            const betIndex = effectiveBetsList.indexOf(betAmount);
            if (betIndex !== -1 && this.matchBetCtrl) {
                // 设置MatchBetCrtl的_currentIndex并更新金额显示
                this.matchBetCtrl._setCurrentIndex(betIndex);
                validBetAmount = betAmount;
            } else if (betIndex === -1) {
                // 如果betAmount不在列表中，使用默认值
                validBetAmount = effectiveBetsList[0];
                if (this.matchBetCtrl) {
                    this.matchBetCtrl._setCurrentIndex(0);
                }
            }
        }

        // 初始化VS界面并传入下注金额
        GameUtil.log(`【Match】发起匹配请求, 下注金额: ${validBetAmount}`);
        GameService.instance.matchRequest(this._getMatchData(validBetAmount));
    }

    /**
     * 在获取到服务端下注信息更新后检查是否需要直接匹配
     * @private
     * @description 当服务端下注信息更新后，如果是直接匹配模式则继续匹配流程
     */
    private _checkDirectMatchAfterBetsUpdate(): void {
        // 如果是直接匹配模式，在获取到服务端数据后继续匹配流程
        if (this.openMsg && this.openMsg.isMatch) {
            GameUtil.log("【Match】服务端下注信息已更新，继续直接匹配流程");
            this._checkDirectMatch();
        }
    }

    /**
     * 组件加载时的事件注册
     * @description 注册界面显示/隐藏事件监听
     */
    protected onLoad(): void {
        TipManager.initTip(this.node);

        EventMgr.addEventListener(EnumEvent.HIDE, this._onHide, this);
        EventMgr.addEventListener(EnumEvent.SHOW, this._onShow, this);
        if (this.node?.isValid) {
            let topBar = this.node.getChildByPath('box/topBar');
            let topBarWidget = topBar.getComponent(Widget);
            let hairHeight = Global.systemInfo.hairHeight ? Utils.getHairHeight() : 40;
            let topValue = (Global.screen_top_height > 0 && hairHeight > 0) ? Global.screen_top_height / 2 : 0;
            topBarWidget.top = (-topValue + hairHeight);
            let widthValue = Global.screen_top_width;
            if (widthValue > 0) {
                topBarWidget.left -= widthValue / 2;
                topBarWidget.right -= widthValue / 2;
            }
        }
    }

    // protected start(): void {
    //     GameService.instance.imgBg.spriteFrame = this.bgSpf;
    // }

    /**
     * 初始化界面信息
     * @description 负责初始化整个匹配界面的显示状态
     * 调用_initViewMatch方法来设置初始的界面显示
     */
    public initInfo(): void {
        this._initViewMatchBet();
        let visibleSize = view.getVisibleSize();
        let winsize = screen.windowSize;
        let ratio = winsize.width / winsize.height;
        let drs = view.getDesignResolutionSize();
        let drsRatio = drs.width / drs.height;
        this.testLabel.string = "缩放比：" + ratio + "  " + drsRatio + "=屏幕宽高=" + winsize.width + ":" + winsize.height + " 设计 " + drs.width + ":" + drs.height + " 可见 " + visibleSize.width + ":" + visibleSize.height;
    }

    /**
     * 初始化匹配界面
     * @description 设置匹配界面的初始状态
     * 激活下注控制界面，隐藏VS对战界面
     * 如果下注控制器不存在则直接返回
     */
    private _initViewMatchBet(): void {
        if (!this.matchBetCtrl) {
            GameUtil.log("【Match】下注控制器不存在，无法初始化下注界面");
            return;
        }
        this.matchBetCtrl._initInfo();
        this._updateViewState(true, false);
        this.closeBtn.active = true;
    }

    /**
     * 初始化VS对战界面
     * @description 切换到VS对战界面的显示状态
     * 隐藏下注界面，显示VS对战界面
     * 获取当前选择的下注金额并初始化VS对战界面
     */
    public _initViewMatchVs(): void {
        if (!this.matchBetCtrl || !this.matchVsCtrl) {
            GameUtil.log("【Match】下注控制器或VS控制器不存在，无法初始化VS对战界面");
            return;
        }

        const currentBetAmount = this.matchBetCtrl.getCurrentBetAmount();
        GameUtil.log(`【Match】初始化VS对战界面, 当前下注金额: ${currentBetAmount}`);
        this._updateViewState(false, true);
        this.matchVsCtrl.initMatch(currentBetAmount);
    }

    /**
     * 初始化对手信息
     * @description 设置对战界面中对手的相关信息
     * @param userId 对手的用户ID
     * @param avatar 对手的头像URL
     * @param nickname 对手的游戏昵称
     */
    public initMatchVsPlayer(userId: number, avatar: string, nickname: string): void {
        GameUtil.log(`【Match】获取到对手信息, 用户ID: ${userId}, 昵称: ${nickname}`);
        this.matchVsCtrl?.onMatchSuccess(userId, avatar, nickname);
    }

    /**
     * 开始倒计时
     * @description 启动匹配倒计时功能
     * @param time 倒计时的初始时间（单位：秒）
     * @remarks
     * - 设置初始倒计时时间
     * - 更新倒计时显示
     * - 清除已存在的定时器
     * - 创建新的定时器，每秒更新一次
     */
    public startCountDown(time: number): void {
        GameUtil.log(`【Match】开始匹配倒计时: ${time}秒`);
        this._time = time;
        this._updateCountdownDisplay();
        this.stopCountDown();
        this._countdownTimer = this.schedule(this._updateCountdown.bind(this), 1);
    }

    /**
     * 更新倒计时
     * @description 每秒执行一次的倒计时更新函数
     * @remarks
     * - 递减倒计时时间
     * - 更新显示
     * - 当倒计时结束时停止定时器
     */
    private _updateCountdown(): void {
        if (--this._time <= 0) {
            this.stopCountDown();
        }
        this._updateCountdownDisplay();
    }

    /**
     * 更新倒计时显示
     * @description 更新界面上倒计时的显示内容和样式
     * @remarks
     * - 如果倒计时大于0，显示剩余秒数
     * - 如果倒计时结束，显示等待提示
     * - 根据不同状态设置不同的文本颜色
     */
    private _updateCountdownDisplay(): void {
        if (!this._lblWaiting) return;

        this._lblWaiting.string = this._time > 0 ? `${this._time}s` : i18n.t('waiting');
        this._lblWaiting.color = this._time > 0 ?
            color().fromHEX('#fef600') :
            color().fromHEX('#ffffff');
    }

    /**
     * 停止倒计时
     * @description 清理倒计时相关的所有状态
     * @remarks
     * - 重置倒计时时间
     * - 清除隐藏时间记录
     * - 取消定时器
     * - 更新显示状态
     */
    public stopCountDown(): void {
        this._time = 0;
        this._timeOnHide = 0;

        if (this._countdownTimer) {
            this.unschedule(this._countdownTimer);
            this._countdownTimer = null;
        }

        this._updateCountdownDisplay();
    }

    /**
     * 界面隐藏处理
     * @description 当界面被隐藏时的处理逻辑
     * @remarks 记录界面隐藏时的时间戳，用于恢复时计算时间差
     */
    private _onHide(): void {
        if (this._time > 0) {
            this._timeOnHide = Date.now();
        }
    }

    /**
     * 界面显示处理
     * @description 当界面重新显示时的处理逻辑
     * @remarks
     * - 计算界面隐藏期间经过的时间
     * - 更新倒计时剩余时间
     * - 更新显示状态
     */
    private _onShow(): void {
        if (this._time > 0 && this._timeOnHide > 0) {
            const delta = Math.floor((Date.now() - this._timeOnHide) * 0.001);
            this._time = Math.max(0, this._time - delta);
            this._updateCountdownDisplay();
            this._timeOnHide = 0;
        }
    }

    /**
     * 开始匹配
     * @description 触发游戏匹配流程
     * @remarks 调用VS控制器的匹配开始方法，并设置匹配成功后的回调
     */
    public startMatching(): void {
        if (this.matchVsCtrl) {
            GameUtil.log("【Match】开始匹配");
            this._isMatching = true;
            this.matchVsCtrl.startMatching(true);
        } else {
            GameUtil.log("【Match】VS控制器不存在，无法开始匹配");
        }
    }
    /**
     * 停止匹配
     * @description 停止游戏匹配流程
     * @remarks 调用VS控制器的匹配停止方法
     */
    public stopMatching(): void {
        if (this.matchVsCtrl) {
            GameUtil.log("【Match】停止匹配");
            this._isMatching = false;
            this.matchVsCtrl.startMatching(false);
        }
    }
    /**
     * 匹配成功
     * @description 匹配成功后，打开实际的游戏界面
     * @remarks 通过UI管理器打开台球游戏界面，并标记为从匹配界面进入
     */
    public matchSuccess(): void {
        GameUtil.log("【Match】匹配成功，准备开始游戏");
        // 设置本局游戏的下注金额
        if (this.matchBetCtrl) {
            Global.cost = this.matchBetCtrl.getCurrentBetAmount();
            GameUtil.log(`【Match】设置本局游戏下注金额: ${Global.cost}`);
        }
        if (this.matchVsCtrl) {
            this.matchVsCtrl.gameStart(() => this._openGameView());
        } else {
            this._openGameView();
        }
    }

    /**
     * 打开游戏界面
     * @description 匹配成功后，打开实际的游戏界面
     * @remarks 通过UI管理器打开台球游戏界面，并标记为从匹配界面进入
     */
    private _openGameView(): void {
        GameUtil.log("【Match】打开游戏界面");
        uiManager.open(UIID.BilliardGame, { isFromMatch: true });
    }

    /**
     * 获取匹配数据
     * @description 构建并返回用于游戏匹配的数据对象
     * @returns {MatchData} 包含所有必要匹配参数的数据对象
     * @remarks 包含玩家ID、游戏ID、下注金额等关键信息
     */
    private _getMatchData(betAmount: number = null): MatchData {
        const effectiveBetsList = Global.getEffectiveBetsList();
        const defaultBetAmount = effectiveBetsList && effectiveBetsList.length > 0 ? effectiveBetsList[0] : 0;

        const matchData = {
            idx: Global.userId,
            gameId: Global.gameId,
            gamePay: betAmount ? betAmount : (this.matchBetCtrl ? this.matchBetCtrl.getCurrentBetAmount() : defaultBetAmount),
            playerNum: 2,
            isPrivate: 0,
            gameType: 0,
            gameGroup: 0,
            languageId: 1,
            isCanRePlay: 1,
            msgID: Date.now(),
            version:String(Global.gameInfo.version),
            pid: 0,
            source: 0,
            packageType: Global.packageType
        };
        console.log(Global.gameInfo.version+"=发起匹配："+JSON.stringify(matchData));
        return matchData;
    }

    /**
     * 开始按钮点击处理
     * @description 处理开始匹配的逻辑：
     * - 检查玩家金币是否足够
     * - 不足则提示充值
     * - 足够则发起匹配请求
     */
    public onStartBtn(): void {
        if (!this.matchBetCtrl) {
            return;
        }

        // 防抖处理
        const now = Date.now();
        if (now - this._lastClickTime < 300) {
            return;
        }
        this._lastClickTime = now;

        const betAmount = this.matchBetCtrl.getCurrentBetAmount();
        GameUtil.log(`【Match】点击开始匹配, 下注金额: ${betAmount}, 玩家金币: ${Global.currentGoldNum}`);

        GameService.instance.matchRequest(this._getMatchData());
    }

    /**
     * 显示设置界面
     * @description 打开游戏设置界面
     * @remarks 通过UI管理器打开设置界面，并标记来源为匹配界面
     */
    public onShowSettings(): void {
        uiManager.open(UIID.Settings, {
            isFormMatch: true
        });
    }

    /**
     * 关闭界面处理
     * @description 处理界面关闭时的清理工作
     * @remarks 如果正在匹配中，发送取消匹配请求
     */
    public onCloseView(): void {
        GameUtil.log("【Match】关闭匹配界面");
        if (this.matchVsCtrl && this.matchVsCtrl.node.active) {
            GameService.instance.matchCancelRequest(this._getMatchData());
        } else if (this.matchBetCtrl && this.matchBetCtrl.node.active) {
            GameService.instance.backHall(true);
        } else {
            GameService.instance.backHall(true);
        }
    }

    /**
     * 显示规则界面
     * @description 打开规则界面
     * @remarks 通过UI管理器打开规则界面
     */
    public onShowRule(): void {
        JsbBrid.showGameRules(Global.gameId, 0);
    }

    /**
     * 组件销毁时的清理
     * @description 清理所有事件监听和计时器
     */
    public onDestroy(): void {
        // this.matchVsCtrl?.onDestroy();
        GameService.instance.resetGameData();
        this.stopCountDown();
    }

    /**
     * 更新界面显示状态
     * @param {boolean} showBet - 是否显示下注界面
     * @param {boolean} showVs - 是否显示对战界面
     * @description 控制下注界面和对战界面的显示状态
     */
    private _updateViewState(showBet: boolean, showVs: boolean): void {
        if (this.matchBetCtrl) this.matchBetCtrl.node.active = showBet;
        if (this.matchVsCtrl) {
            this.matchVsCtrl.node.active = showVs;
            if (!showVs) this.matchVsCtrl.startMatching(false);
        }
    }
}