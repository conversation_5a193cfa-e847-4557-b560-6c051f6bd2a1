import { AccountModel, GameModel, SystemInfo } from "../../game/data/GameInterface";
import { EnumEvent, EventManager } from "../event/EventManager";
import { GameUtil } from "../tools/GameUtil";

class Global {
    /**适配后的UILocation的y轴偏移值 */
    UILocationOffsetY = 0;
    /**地址栏参数 */
    urlSearch = null;//new URLSearchParams(location.search || '');
    /**是否是阿语 */
    isArabic = false;//this.urlSearch.get('lan') == '1';
    /**房间id */
    roomId = '';//+this.urlSearch.get('roomid');
    /**游戏id */
    gameId = 10024;
    /**网关token */
    token = '';
    /**用户id */
    userId = 0;
    /**连socket 请求头需要传的参数 key是固定的 */
    key = "asdf";
    /**昵称 */
    nickName = '';
    /**头像 */
    avatar = '';
    isconnect = false;
    /**是否是管理员 */
    isAdmin = false;
    /**客户端包类型 0：主包，2：DominoStar, 3: YallaStar*/
    packageType = 3
    isDebug: boolean = true;
    visibleWidth: number = 0;
    visibleHeight: number = 0;
    screen_scale: number = 0;
    screen_top_height: number = 0;
    screen_top_width: number = 0;
    /**获取系统信息*/
    systemInfo: SystemInfo = {

    };
    gameInfo: GameModel = {
        ip: '',
        url: '',
        version: '1040300',
        instanceId: '',
        token: 'asdf'
       
    };//游戏信息

    userInfo: AccountModel = {
        userId: null,
    };//角色信息

    /**获得焦点 true 失去焦点 false */
    public isResume: boolean = true;

    /**动效状态，开启1 关闭0 */
    public animationState = 1;
    public frameRate = 60;
    public dt = 1 / 60;//0.0166667;

    get currentGoldNum() {
        return this.userInfo.currentGoldNum;
    }
    set currentGoldNum(value) {
        this.userInfo.currentGoldNum = value;
        EventManager.getInstance().raiseEvent(EnumEvent.COIN_CHANGED, value);
    }

    /**游戏进度，0:未开启游戏 1:游戏开始 */
    gameState = 0;
    /**游戏加载后，玩家经历过的局数 */
    roundNum = 0;
    /**所有游戏模式 */
    gameModes = {
        ball8: 1081,
    };
    /**当前游戏模式，1081:8球模式 */
    gameMode = this.gameModes.ball8;
    /**一场游戏的花费 */
    cost = 0;
    /**自己是否在座位上 */
    isOnSeat = false;
    /**游戏是否开始 */
    isGaming = false;
    /**最大座位数目 */
    maxSeatNum = 2;
    /**座位信息 */
    private _seatsInfo: UserInfoOnSeat[] = [];
    public get seatsInfo(): UserInfoOnSeat[] {
        return this._seatsInfo;
    }
    public set seatsInfo(value: UserInfoOnSeat[]) {
        this._seatsInfo = value;
        this.isOnSeat = false;
        for (let i in value) {
            let info = value[i];
            if (info.idx == this.userId) this.isOnSeat = true;
        }
    }
    /** 匹配座位信息*/
    private _matchSeatsInfo:UserInfoOnSeat[] = []
    public get matchSeatsInfo(): UserInfoOnSeat[] {
        return this._matchSeatsInfo;
    }
    public set matchSeatsInfo(value: UserInfoOnSeat[]) {
        this._matchSeatsInfo = value;
    }

    /**游戏对局唯一id标识 */
    gameIdentity = 0;
    /**抽水获得比例 */
    recycleBonus = -1;
    /**游戏下注金额列表 */
    betsList:number[] = []


    /**游戏金层列表 */
    goldFloorsList: number[] = []

    /**默认下注金额列表 - 用于首次显示，避免界面闪烁 */
    private _defaultBetsList: number[] = [500, 1000, 2000]

    /**默认金层列表 */
    private _defaultGoldFloorsList: number[] = [500, 1000, 2000]

    /**本地存储的键名 */
    private readonly _STORAGE_KEY_BETS = 'billiards_bets_cache'
    private readonly _STORAGE_KEY_GOLD_FLOORS = 'billiards_gold_floors_cache'
    private readonly _STORAGE_KEY_RECYCLE_BONUS = 'billiards_recycle_bonus_cache'

    /**是否已经获取过服务端下注信息 */
    private _hasFetchedBetsInfo: boolean = false

    /**是否需要重新选服务 */
    public isReChooseServer: boolean = false;

    constructor() {
        this._loadCachedBetsInfo();
    }

    /**
     * 获取有效的下注金额列表
     * 如果服务端数据存在则使用服务端数据，否则使用默认数据
     * @returns {number[]} 下注金额列表
     */
    getEffectiveBetsList(): number[] {
        if (this.betsList && this.betsList.length > 0) {
            return this.betsList;
        }
        return this._defaultBetsList;
    }

    /**
     * 获取有效的金层列表
     * 如果服务端数据存在则使用服务端数据，否则使用默认数据
     * @returns {number[]} 金层列表
     */
    getEffectiveGoldFloorsList(): number[] {
        if (this.goldFloorsList && this.goldFloorsList.length > 0) {
            return this.goldFloorsList;
        }
        return this._defaultGoldFloorsList;
    }

    /**
     * 设置下注信息已获取标记
     */
    setBetsInfoFetched(): void {
        this._hasFetchedBetsInfo = true;
        // 同时缓存最新的下注信息
        this._cacheBetsInfo();
    }

    /**
     * 检查是否已获取过下注信息
     * @returns {boolean} 是否已获取
     */
    hasFetchedBetsInfo(): boolean {
        return this._hasFetchedBetsInfo;
    }

    /**
     * 检查是否需要获取下注信息
     * @returns {boolean} 是否需要获取
     */
    needsFetchBetsInfo(): boolean {
        return !this._hasFetchedBetsInfo || (!this.betsList || this.betsList.length === 0);
    }

    /**
     * 从本地存储加载缓存的下注信息
     * @private
     */
    private _loadCachedBetsInfo(): void {
        try {
            // 加载下注列表
            const cachedBets = localStorage.getItem(this._STORAGE_KEY_BETS);
            if (cachedBets) {
                const betsData = JSON.parse(cachedBets);
                if (Array.isArray(betsData) && betsData.length > 0) {
                    this.betsList = betsData;
                    GameUtil.log(`【Global】从缓存加载下注列表: ${JSON.stringify(betsData)}`);
                }
            }

            // 加载金层列表
            const cachedGoldFloors = localStorage.getItem(this._STORAGE_KEY_GOLD_FLOORS);
            if (cachedGoldFloors) {
                const goldFloorsData = JSON.parse(cachedGoldFloors);
                if (Array.isArray(goldFloorsData) && goldFloorsData.length > 0) {
                    this.goldFloorsList = goldFloorsData;
                    GameUtil.log(`【Global】从缓存加载金层列表: ${JSON.stringify(goldFloorsData)}`);
                }
            }

            // 加载回收奖励比例
            const cachedRecycleBonus = localStorage.getItem(this._STORAGE_KEY_RECYCLE_BONUS);
            if (cachedRecycleBonus) {
                const recycleBonus = Number(cachedRecycleBonus);
                if (!isNaN(recycleBonus) && recycleBonus > 0) {
                    this.recycleBonus = recycleBonus;
                    GameUtil.log(`【Global】从缓存加载回收奖励比例: ${recycleBonus}`);
                }
            }
        } catch (error) {
            GameUtil.log(`【Global】加载缓存下注信息失败: ${error}`);
        }
    }

    /**
     * 缓存下注信息到本地存储
     * @private
     */
    private _cacheBetsInfo(): void {
        try {
            if (this.betsList && this.betsList.length > 0) {
                localStorage.setItem(this._STORAGE_KEY_BETS, JSON.stringify(this.betsList));
                GameUtil.log(`【Global】缓存下注列表: ${JSON.stringify(this.betsList)}`);
            }

            if (this.goldFloorsList && this.goldFloorsList.length > 0) {
                localStorage.setItem(this._STORAGE_KEY_GOLD_FLOORS, JSON.stringify(this.goldFloorsList));
                GameUtil.log(`【Global】缓存金层列表: ${JSON.stringify(this.goldFloorsList)}`);
            }

            if (this.recycleBonus > 0) {
                localStorage.setItem(this._STORAGE_KEY_RECYCLE_BONUS, String(this.recycleBonus));
                GameUtil.log(`【Global】缓存回收奖励比例: ${this.recycleBonus}`);
            }
        } catch (error) {
            GameUtil.log(`【Global】缓存下注信息失败: ${error}`);
        }
    }

    /**获取在座位上的用户信息 观战用户*/
    getUserInfoOnSeat(idx) {
        let userData: UserInfoOnSeat = null;
        this.seatsInfo.forEach(element => {
            if (idx == element.idx) {
                userData = element;
                return true;
            }
        })
        return userData;
    }

    /**是否是观战 */
    get isWatcher() {
        // for (let i in this.seatsInfo) {
        //     if (this.seatsInfo[i].idx == this.userId) return false;
        // }
        // return true
        return false;
    }
}

export default new Global;