import { native } from "cc";
import { GameUtil } from "./GameUtil";
import { sys } from "cc";
import { DeviceType } from "../../game/data/Enum";
import Global from "../data/Global";
import { JsbWebSocket } from "../net/JsbWebSocket";
import { EventMgr } from "../event/EventManager";
import AudioMgr from "./AudioMgr";
import i18n from "./i18n";
import { FileUtil } from "./FileUtil";

export class JsbEnumEvent {
    static HASBACKHALL = 'HASBACKHALL';
    static PAUSE = 'onCocosPause'; //TODO::切后台的话，cocos直接休眠，不执行任何逻辑、监听，so onPause无效，需要cocos自己监听
    static RESUME = 'onCocosResume';
    static PRESSENTER = 'pressEnter';   //系统键盘按下回车键，原生通知游戏发送消息（观战）
    static SHOWKEYBOARD = "keyBoardUp";//弹起键盘
    static CALLKEYBOARD = "callKeyBoard";//弹起键盘
    static KICKEDOUT = "onKickedOut";//被顶号（各自的游戏中监听）
    static UPDATEMONEY = "updatemoney";//通知各个游戏更新钻石或者金币
    static SHARE = "shareResponse";//分享
    static ONBACKPRESSED = "onBackPressed";//android返回键功能
    static EXITGAMEWATCHFORLEAGUE = "exitGameWatchForLeague";///**其他游戏中收到联赛邀请，这时需要backHall to  leagueList */
    static QUITGAME = "quitGame";//观战中收到游戏邀请
    static CONNECTSTATECHANGE = "connectStateChange";   //大厅socket链接情况
    static SHARESHOT = "SHARESHOT";//分享截图
    static ONLOGIN = "onLogin";//原生触发进入游戏入口

    /**
     * json中的三个字段 {type:number,msg:string,isSend:boolean}
     * type:1表示消息,2表示小马动画(快捷表情),3新增表情系统;
     * msg:内容;
     * isSend:是否立即发送
     */
    static NATIVE_INPUT = "onNativeInput";
}

class JsbBridge {
    public deviceType: DeviceType;
    /**回掉函数hash  目前回掉后不置为 null*/
    private _cbHash = {};
    private methodId: number = 0;

    initNative(): void {
        // 获取设备类型
        switch (sys.os) {
            case sys.OS.ANDROID:
                this.deviceType = DeviceType.Android;
                break;
            case sys.OS.IOS:
                this.deviceType = DeviceType.IOS;
                break;
            default:
                this.deviceType = DeviceType.Browser;
                break;
        }
        if (native?.bridge) native.bridge.onNative = this.onCallNative_back.bind(this);
    }
    /**
     * @param msg {method,params}
     */
    onCallNative_back(msg: any): void {
        if (msg) {
            if (typeof msg == 'string') msg = JSON.parse(msg);
            // let para = (typeof msg.param == 'string' && msg.param) ? JSON.parse(msg.param) : msg.param;
            let para = msg.param;
            console.log((typeof msg == 'string') + ` sendToJavaScript 方法: `+ msg);
            let methodId = msg.methodId;
            // GameUtil.log("sendToJavaScript1" + methodId)
            // methodId && GameUtil.log("sendToJavaScript2" + Boolean(this._cbHash[methodId]))
            if (methodId && this._cbHash[methodId]) {
                // console.log("====方法回掉====");
                this._cbHash[methodId](para);
                delete this._cbHash[methodId];
                return;
            }
            let method = msg.method;
            switch (method) {
                case "onLogin":
                    GameUtil.log("=Main 进入游戏 0=");
                    para = (typeof para == 'string') ? JSON.parse(para) : para;
                    if (!!Global.gameInfo.token && !!para.gameInfo && Global.gameInfo.token == para.gameInfo.token) return;
                    GameUtil.log("=Main 进入游戏 1=");
                    AudioMgr.init();
                    EventMgr.raiseEvent(JsbEnumEvent.ONLOGIN, para);
                    break;
                // case "onKickedOut":
                //     GameCtrl[method](para);
                // break;
                case "currentLanguageType":
                    Global.isArabic = para == 2;
                    console.log("==获取语言=para=" + para);
                    i18n.init(Global.isArabic ? 'ar' : 'en');
                    break;
                case "currentGameSoundState":
                case "currentBackgroundMusicState":
                    AudioMgr[method](para);
                    break;
                case "loadProfileInfo":
                    console.log("==刘海==="+JSON.stringify(para));
                    Global.systemInfo = typeof para == 'string' ? JSON.parse(para) : para;
                    break;
                case "backHall":
                    GameUtil.log("返回大厅");
                    EventMgr.raiseEvent("clearGame");
                    break;
                case "onNetMsg":
                case "onNetResponseMsg":
                case "onNetClose":
                case "onNetError":
                case "onNetConnecting":
                    para = (typeof para == 'string') ? JSON.parse(para) : para;
                    JsbWebSocket.instance['toJs_' + method](para);
                    break;
                case "shareResponse":
                    // GameCtrl.backHall();
                    // console.log("shareResponse");
                    EventMgr.raiseEvent(method, para);
                    break;
                default:
                    //TODO::可以考虑让原生去掉onPause onResume
                    if (method != "onPause" && method != "onResume") {
                        EventMgr.raiseEvent(method, para);
                    }
                    break;
            }

        } else {
            GameUtil.log("等待原生响应...");
        }
    }

    sendToNative(method: string, param: any = null, cb: Function = null) {
        if (this.deviceType == DeviceType.Browser) return;
        if (native?.bridge) {
            let d = { method };
            if (param) d['param'] = param;
            if (cb) {
                this.methodId++;
                d['methodId'] = this.methodId;
                this._cbHash[this.methodId] = cb;
            }
            let str = JSON.stringify(d);
            native.bridge.sendToNative(str);
            // GameUtil.log("sendToNative 方法:" + method, str);
        } else {
            // GameUtil.log("无法通讯...");
        }
    }
    //===============================sendToNative=========================================
    getUserInfo(cb = null): void{
        this.sendToNative('getUserInfo', null, cb);
    }

    getGameInfo(cb = null): void{
        this.sendToNative('getGameInfo', null, cb);
    }

    getSystemInfo(cb = null): void{
        this.sendToNative('getSystemInfo', null, cb);
    }

    setSystemInfo(param: any = null): void {
        this.sendToNative('setSystemInfo', param);
    }
    /**游戏状态 1游戏中  0游戏结束*/
    setGameState(state = 0): void{
        this.sendToNative('setGameState',{state});
    }

    getSocketSign(cb = null): void{
        this.sendToNative('getSocketSign', cb);
    }

    /**
     * 埋点
     * @param eventName 
     * @param returnValue 
     * @param type  1点击时间  3曝光页面
     */
    clogDBAmsg(eventName: string, returnValue?: any, type: number = 1): void {
        this.sendToNative("clogDBAmsg", { eventName, mesg: returnValue ? { returnValue: JSON.stringify(returnValue) } : {}, type });
    }

    /**
     * 游戏加载完成200通知原生，可以准备发送onLogin
     */
    gameLoadingProgress(num = 200): void {
        this.sendToNative('gameLoadingProgress', 200);
    }

    removeMatchView() {
        this.sendToNative('removeMatchView');
    }
   
    /**
     * 打开规则页面，当成是系统弹框，需要把游戏内其他ui小弹框关闭
     * @param gameType 
     * @param mode 
     * @param ruleType 
     */
    showGameRules(gameType: number, mode: number, ruleType: number = 1) {
        this.sendToNative("showGameRules", { gameType, mode, ruleType });
        // EventMgr.raiseEvent('OpenDialog');
    }
   
    /**隐藏 规则webview*/
    removeGameRulesView() {
        this.sendToNative("removeGameRulesView");
    }

    removeAllJsbView() {
        this.removeGameRulesView();
    }

    /**
     * 返回游戏大厅
     * @param json {to:''}返回到指定的原生界面
     * @param delayTime 主要针对锦标赛退回列表界面，Android在backhall之后直接抛出事件，会导致转场时候出现蓝屏（ps：android因为某些原因不能回调）
     */
    public backHallTimeOut = null;
    backHall(json: any = null, cb: Function = null) {
        console.log("jsb backhall ");
        // if (this.deviceType != DeviceType.IOS) {//切回大厅效果，需要等ios返回，再移除界面,ios 监听了hasbackhall complete
        //     EventMgr.raiseEvent(JsbEnumEvent.HASBACKHALL);
        // }
        if (!json) this.sendToNative('backHall');
        else {
            this.sendToNative('backHall', json);
        }
    }

  
    /**
     * 屏蔽制定用户音频
     * data {
     *  "uId": number,用户ID
     *  "muted":boolean false取消 true屏蔽
     * }
     */
    muteRemoteAudioStream(uId: number, muted: boolean, cb: Function) {
        this.sendToNative("muteRemoteAudioStream", { uId, muted }, cb);
    }
    /**
    * 获取语音授权
    */
    currentAudioStatus(cb: Function) {
        if (this.deviceType == DeviceType.Browser) {
            cb({ "status": 0 });
            return;
        }
        this.sendToNative("currentAudioStatus", null, cb);
    }
   
    /**
     * 被T后，执行ios的kicketOut，避免游戏和原生都弹出被T的弹框
     * @param cb 
     */
    alertAccountLoggedView(cb) {
        this.sendToNative("alertAccountLoggedView", null, cb);
    }

    /**
     * 提示大厅显示toast
     * @param json {msg:'Please check your network connection'}
     */
    showToast(json: any) {
        this.sendToNative("showToast", json);
    }

    /**
     * 退出登录
     */
    loginOut() {
        GameUtil.log("退出的登录页面");
        if (this.deviceType == DeviceType.Browser) return;
        this.sendToNative("loginOut");
        Global.userInfo.userId = null;
        this.resetAccount();
    }

    clearTime() {
        if (this.backHallTimeOut) {
            clearTimeout(this.backHallTimeOut);
            EventMgr.raiseEvent(JsbEnumEvent.HASBACKHALL);
        }
        this.backHallTimeOut = null;
    }

    /**
     * 输入框禁止输入
     */
    limitInput() {
        this.sendToNative("limitInput");
    }

    /**
     * 获取备用域名方法
     */
    getDomainType(typeList, cb: Function = null) {
        GameUtil.log("备用域名列表：" + typeList);
        this.sendToNative("getDomainType", typeList, cb);
    }

    /**
        * 通过原生请求ajax  需要zues加密
        *resultCode =200 成功
        * @param json 
        */
    sendHttpRequest(url, json, cb?) {
        var sendData = { url: url, data: json };
        GameUtil.log(url + " :url==通过原生发起zues加密的ajax请求==");
        GameUtil.log(json);

        this.sendToNative("sendHttpRequest", sendData, cb);
    }
    /**
     * 获取代理信息
     */
    getProxyInfo(url, cb) {
        this.sendToNative("getProxyInfo", { url }, cb);
        GameUtil.log("====代理信息：");
        GameUtil.log(url);
    }

    /**通过原生下载资源 */
    /**
     * 初始化游戏完成时调用获取缓存资源路径
     * @param cb 
     */
    getRootPath(cb) {
        this.sendToNative("getRootPath", null, cb);
    }

    /**
   * 检查文件是否存在的方法
   * 
   * 通过调用底层的native接口，发送文件路径信息，以判断该文件是否存在于指定路径。
   * 此方法的核心在于与native层的通信，依赖于sendToNative方法实现跨层调用。
   * 
   * @param filePath 文件路径，用于检查文件是否存在
   * @param cb 回调函数，用于接收检查结果，结果为布尔值，表示文件是否存在
   */
    fileExist(filePath, cb) {
        this.sendToNative("fileExist", filePath, cb);
    }

    downloadImgByUrl(url): Promise<{ localPath: string, url: string }> {
        // console.log("加载 downloadImgByUrl", url);
        return new Promise((resolve, reject) => {
            let localPath = FileUtil.instance.getPathByUrl(url);
            this.fileExist(localPath, (data) => {//不重复下载
                if (data?.exist) {
                    resolve({ localPath, url });
                } else {
                    this.sendToNative("downloadImgByUrl", { url, path: 'cocos_img_temp' }, res => {
                        if (res && res.success) {
                            resolve({ localPath, url });
                        } else {
                            reject(null);
                        }
                    });
                }
            });

        })
    }

    /**md5加密 */
    encriptionMD5(str, cb) {
        this.sendToNative("encriptionMD5", str, cb);
    }

    /**
     * 重置account数据
     */
    resetAccount(): void {
        Global.userInfo = {
            encryptToken: '',
            roylevel: 0,
            vipLevel: 0,
            realRoyLevel: 0,
        }
        Global.gameInfo = {
            ip: '',
            token: '',
            banTalkData: "",
            version: "",
            roomId: 0,
        }
    }
}

export const JsbBrid = new JsbBridge();