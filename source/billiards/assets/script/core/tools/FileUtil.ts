import { assetManager, ImageAsset,sys } from "cc";
import { JsbBrid } from "./JsbBridge";

const COCOS_CACHE_PATH = '/CocosCache/appCache/';
/**
 * 文件工具类，用于处理游戏资源的加载和管理。
 */
export class FileUtil {
    success: boolean = false;
    folderPath: string = ""; // 系统资源FileUtil储存根目录热更资源(包含：皮肤基础包)
    folderCache: string = ""; // 系统资源FileUtil储存要求下载目录 单个皮肤加载的资源（包含皮肤预加载和局内单个皮肤加载，头像资源加载）
    // jsMd5: any = null;
    private static _instance: FileUtil = null;
    static get instance() {
        if (!this._instance) this._instance = new FileUtil();
        return this._instance;
    }
    /**
     * 初始化方法，用于获取可写路径并设置为资源存储目录
     */
    init() {
        if (this.folderCache === "") {
            JsbBrid.getRootPath((Path) => {
                console.log("==getRootPath===" + Path);
                this.folderCache = `${Path}${COCOS_CACHE_PATH}`;
                console.log("FileUtil folderCache init" + this.folderCache)
            });
        }
        // this.jsMd5 = new md5();
        // this.folder ='https://file.yallaludo.com/Skin/H5/dev/cc_skin_test2/'
    }

    /**
   * 异步加载单个纹理资源通过完整路径。
   * @param {string} pngPath PNG文件路径。
   * @param {number} [id] 资源ID。
   * @param {AssetType} [assetType] 资产类型。
   * @returns {Promise<ImageAsset>} 返回一个Promise，解析为图像资产。
   */
    async textureLoadByFullPath(pngPath: string): Promise<ImageAsset> {
        const pathUrl = '';

        // 尝试从本地路径加载资源
        let imageAsset = await this.tryLoadTexture(pngPath, pathUrl);

        if (!imageAsset) {
            throw new Error(`Failed to load texture from ${pngPath}`);
        }

        return imageAsset;
    }

    /**
     * 尝试从指定路径加载纹理资源。
     * @param {string} pngPath PNG文件路径。
     * @param {string} baseFolderPath 基础文件夹路径。
     * @returns {Promise<ImageAsset | undefined>} 返回一个Promise，解析为图像资产或undefined。
     */
    async tryLoadTexture(pngPath: string, baseFolderPath: string): Promise<ImageAsset | undefined> {

        const fullPath = baseFolderPath === '' ? pngPath : `${baseFolderPath}${pngPath}`;
        const exists = await this.checkPath([fullPath]);
        if (exists) {
            return new Promise((resolve, reject) => {
                assetManager.loadRemote(fullPath, (err, asset: ImageAsset) => {
                    if (err) {
                        console.log(`Texture load error for ${fullPath}: ${err}`);
                        reject(err);
                    } else {
                        console.log("textureLoad assets "+ JSON.stringify(asset));
                        resolve(asset);
                    }
                });
            });
        }
        return undefined;
    }

    /**
     * 检查多个路径是否存在。
     * @param {string[]} PatchUrlArr 路径URL数组。
     * @returns {Promise<boolean>} 返回一个Promise，解析为所有路径是否存在。
     */
    async checkPath(PatchUrlArr: string[]): Promise<boolean> {
        for (const filePath of PatchUrlArr) {
            const exists = await this.fileExist(filePath);
            if (!exists) {
                console.log("FileUtil checkPath  "+ PatchUrlArr+ "exists "+ exists);
                return false; // 如果找到一个不存在的文件，立即返回 false
            }
        }
        return true; // 所有文件都存在，返回 true
    }


    getPathByUrl(url: string) {
        let path = 'cocos_img_temp';
        let fileName = url ? url + ".png" : url;
        return `${this.folderCache}${path}/${fileName}`;
    }

    /**
     * 检查文件是否存在。
     * @param {string} filePath 文件路径。
     * @returns {Promise<any>} 返回一个Promise，解析为文件是否存在。
     */
    async fileExist(filePath): Promise<any> {

        return new Promise((resolve) => {
            JsbBrid.fileExist(filePath, (data) => {
                if (data) {

                    resolve(data?.exist || false);
                } else {
                    resolve(false);
                }
            });
        });

    }
}