
import { Texture2D, sp, SpriteA<PERSON><PERSON>, Sprite<PERSON>rame, ImageAsset } from "cc";
import { Sprite } from "cc";
import { GameUtil } from "../tools/GameUtil";
import { FileUtil } from "../tools/FileUtil";

/**
 * 定义CacheKey类型，用于资源的唯一标识。
 */
type CacheKey = string;

/**
 * 定义ResourceTypes类型，用于表示不同类型的资源。
 */
type ResourceTypes = Texture2D | sp.SkeletonData | SpriteAtlas | SpriteFrame | ImageAsset | any;

/**
 * 定义CacheValue类型，用于存储资源或null（表示资源已被移除）。
 */
type CacheValue<T> = T | null;


/**
 * CacheManager类，用于管理游戏资源的缓存。
 */
export class CacheManager {

    /**
     * 单例模式的静态实例属性。
     */
    private static _instance: CacheManager = null;

    /**
     * 存储资源的Map结构。
     */
    private resourceCache: Map<CacheKey, CacheValue<ResourceTypes>>;
    private resourceMapping: Map<string, string>;
    private loadingPromises: Map<CacheKey, Promise<any>>; // 用于跟踪正在加载中的资源
    /**
     * 获取CacheManager的单例实例。
     * @returns CacheManager的实例。
     */
    static get instance(): CacheManager {
        if (!this._instance) {
            this._instance = new CacheManager();
        }
        return this._instance;
    }

    /**
     * 私有构造函数，初始化资源缓存。
     */
    private constructor() {
        this.resourceCache = new Map<CacheKey, CacheValue<ResourceTypes>>();
        this.loadingPromises = new Map<CacheKey, Promise<any>>();
        this.resourceMapping = new Map<string, string>();

        window["CacheManager"] = this;
    }

    /**
     * 从缓存中移除资源。
     * @param key 资源的唯一标识。
     */
    removeResource(key: CacheKey): void {
        this.resourceCache.delete(key);
    }
    private addRef(res: SpriteFrame | sp.SkeletonData | SpriteAtlas) {
        if (res && res.uuid) {
            let resCache = this.resourceCache.get(res.uuid);
            if (resCache) {
                GameUtil.log("添加引用 前" + resCache.refCount + ' ' + resCache.uuid);
                resCache.addRef();
                GameUtil.log("添加引用 后" + resCache.refCount + ' ' + resCache.uuid);
            }
        }
    }
    private defRef(res: SpriteFrame | sp.SkeletonData | SpriteAtlas) {
        if (res && res.uuid) {
            let resCache = this.resourceCache.get(res.uuid);
            if (resCache) {
                GameUtil.log("移除引用 前" + resCache.refCount + ' ' + resCache.uuid);
                resCache.decRef(false);
                GameUtil.log("移除引用 后" + resCache.refCount + ' ' + resCache.uuid);
                this.releasesRes(resCache);
            }
        }
    }
    private releasesRes(res: SpriteFrame | sp.SkeletonData | SpriteAtlas) {
        if (res.refCount <= 0) {
            this.removeResource(res.uuid)
            if (res instanceof SpriteFrame) {
                this.destorySpriteFrame(res)
            } else if (res instanceof sp.SkeletonData) {
                let textures = res.textures as Texture2D[];
                if (textures) {
                    textures.forEach(texture => {
                        if (texture) {
                            texture.image?.decRef();
                            texture.destroy();
                        }
                    })
                }
                res.destroy();
            } else if (res instanceof SpriteAtlas) {
                let spriteFrames = res.spriteFrames;
                Object.keys(spriteFrames).forEach(key => {
                    let spriteFrame = spriteFrames[key];
                    spriteFrame && this.destorySpriteFrame(spriteFrame)
                })
                res.destroy();
            }
        }
    }
    private destorySpriteFrame(res: SpriteFrame) {
        let texture = res.texture as Texture2D;
        if (res.packable) {// 如果已加入动态合图，必须取原始的Texture2D
            texture = res.original?._texture as Texture2D;
        }
        if (texture) {
            texture.image?.decRef();
            texture.destroy();
        }
        res.destroy();
    }
    public setSpriteFrame(sp: Sprite, spf: SpriteFrame) {
        if (sp) {
            // console.trace(spf.uuid);
            let oldSF = sp.spriteFrame;
            if (oldSF) {
                if (spf?.uuid == oldSF.uuid) return;
                sp.spriteFrame = null;
                GameUtil.log("defSpriteFrameRef" + sp.uuid + ' ' + oldSF.uuid);
                this.defRef(oldSF);
            }
            if (spf) {
                if (!spf.texture) {//todo 需要容错处理
                    GameUtil.log("setSpriteFrame" + sp.uuid + ' ' + spf.uuid);
                }
                sp.spriteFrame = spf;
                this.resourceMapping.set(sp.uuid, spf.uuid);
                this.addRef(spf);
            }
        }
    }
    public destoryRes(node: any) {
        if (node) {
            let uuid = node.uuid
            if (uuid) {
                let resUuid = this.resourceMapping.get(uuid);
                if (resUuid) {
                    let sfCache = this.resourceCache.get(resUuid);
                    sfCache && this.defRef(sfCache)
                }
                this.resourceMapping.delete(uuid);
            }
        }
    }
    public setSkeletonData(sk: sp.Skeleton, skData: sp.SkeletonData, trackIndex: number = 0) {
        if (sk) {
            let oldSkData: sp.SkeletonData = sk.skeletonData
            if (oldSkData) {
                if (skData?.uuid == oldSkData.uuid) return;
                sk.clearAnimation(trackIndex)
                sk.skeletonData = null;
                this.defRef(oldSkData);
            }
            if (skData) {
                sk.skeletonData = skData;
                this.resourceMapping.set(sk.uuid, skData.uuid);
                this.addRef(skData);
            } else {
                this.resourceMapping.delete(sk.uuid);
            }
        }
    }
    public addAtlasRef(uuid: string, spriteAtlas: SpriteAtlas) {
        if (!this.resourceMapping.has(uuid)) {
            this.resourceMapping.set(uuid, spriteAtlas.uuid);
            this.addRef(spriteAtlas);
        }
    }
    /**
     * 异步获取资源
     * 
     * 此方法首先检查资源是否已缓存，如果已缓存则直接返回
     * 如果资源正在加载中，则等待加载完成并返回
     * 如果资源既未缓存也未加载，则使用提供的加载函数进行加载，并将加载的资源缓存起来
     * 
     * @param key 资源的唯一键，用于标识资源
     * @param loaderFunction 一个返回Promise的函数，用于加载资源
     * @returns 返回加载的资源
     * @throws 如果资源加载失败或资源被移除，抛出错误
     */
    async getResource<T extends ResourceTypes>(key: CacheKey, loaderFunction: () => Promise<T>, timeout: number = 10000): Promise<T> {
        // if (this.resourceCache.has(key)) {
        //     const value = this.resourceCache.get(key);
        //     if (value === null) {
        //         throw new Error(`Resource with key ${key} was removed.`);
        //     }
        //     return this.resourceCache.get(key) as T;
        // } else 
        if (this.loadingPromises.has(key)) {
            // 如果资源正在加载中，等待加载完成
            const promise = this.loadingPromises.get(key);
            try {
                const result = await promise;
                return result;
            } catch (error) {
                // 如果加载失败，从 loadingPromises 中移除 key
                this.loadingPromises.delete(key);
                throw error;
            }
        } else {
            // 启动加载操作
            const promise = this.withTimeout(loaderFunction(), timeout, key);
            this.loadingPromises.set(key, promise);
            try {
                const resource = await promise;
                // this.resourceCache.set(key, resource);
                this.loadingPromises.delete(key); // 加载完成后删除正在加载的标记
                return resource;
            } catch (error) {
                this.loadingPromises.delete(key); // 加载失败后删除正在加载的标记
                throw new Error(`Failed to load resource with key ${key}: ${error}`);
            }
        }
    }

    /**
     * 根据资源类型和路径构建缓存键
     * @param path 资源路径
     * @param type 资源类型字符串标识
     * @returns 构建的缓存键
     */
    buildCacheKey(path: string, type: string): CacheKey {
        return `${type}:${path}`;
    }
    getSpriteFrameInCache(fileName: string): SpriteFrame {
        let spriteFrame: SpriteFrame = this.resourceCache.get(this.buildCacheKey(fileName, 'SpriteFrame'));
        return spriteFrame?.texture ? spriteFrame : null;
    }
    
    /**
   * 包装一个Promise，添加超时功能
   * @param promise 原始Promise
   * @param timeout 超时时间，单位为毫秒
   * @param key 资源的唯一键，用于标识资源
   * @returns 包装后的Promise
   */
    private withTimeout<T>(promise: Promise<T>, timeout: number, key: CacheKey): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            const timer = setTimeout(() => {
                reject(new Error(`Resource loading timed out for key ${key}`));
            }, timeout);

            promise.then(
                (value) => {
                    clearTimeout(timer);
                    resolve(value);
                },
                (error) => {
                    clearTimeout(timer);
                    reject(error);
                }
            );
        });
    }


    /**
    * 异步加载并缓存SpriteFrame通过全连接URL。
    * 这样做的目的是为了优化后续对相同资源的请求，避免重复加载和创建，提高性能。
    * @param filePath 要加载的SpriteFrame的文件路径。
    * @returns 返回一个Promise，解析为加载并缓存后的SpriteFrame对象。
    */
    async loadAndCacheSpByFullPath(filePath: string, packable: boolean = false): Promise<{ spriteFrame: SpriteFrame, filePath: string }> {
        const spriteFrameKey = this.buildCacheKey(filePath, 'SpriteFrame');
        // GameUtil.log('loadAndCacheSpriteFrame', filePath);
        let spriteFrame = this.resourceCache.get(spriteFrameKey);
        if (spriteFrame) {
            return Promise.resolve({ spriteFrame, filePath });
        }
        // 修改为直接在回调内继续后续操作，确保按序执行
        const textureKey = this.buildCacheKey(filePath, 'Texture2D');
        const texturePromise = this.getResource(textureKey, async () => {
            try {
                const fileUtil = FileUtil.instance;
                const imageAsset = await fileUtil.textureLoadByFullPath(filePath);
                if (!imageAsset) throw new Error(`Failed to load texture: ${filePath}`);
                return this.createTexture(imageAsset); // 返回成功的Texture2D，以便在链式调用中使用
            } catch (error) {
                GameUtil.error(`Error loading loadAndCacheSpByURL: ${filePath}`, error);
                throw error; // 继续抛出异常
            }
        });

        // 等待Texture2D加载成功后，再创建SpriteFrame
        let texture: Texture2D;
        try {
            texture = await texturePromise;
        } catch (error) {
            // 如果在等待Texture2D时出错，直接抛出错误，无需尝试获取SpriteFrame
            throw error;
        }
        console.log("loadAndCacheSpByURL", texture);
        // 使用已缓存的Texture2D创建SpriteFrame
        spriteFrame = new SpriteFrame();
        spriteFrame.texture = texture;
        spriteFrame.packable = packable;
        spriteFrame._uuid = spriteFrameKey;
        this.resourceCache.set(spriteFrameKey, spriteFrame); // 添加SpriteFrame到缓存
        return { spriteFrame, filePath };
    }

    private createTexture(imageAsset: ImageAsset): Texture2D {
        const texture = new Texture2D();
        imageAsset.addRef();
        texture.image = imageAsset;
        texture.setWrapMode(2, 2, 2)
        return texture;
    }

    /**
     * 清理缓存，根据需要实现具体的清理策略
     */
    clearCache() {
        // this.resourceCache.forEach((value, key) => {
        //     console.log("releaseResource", key, value.refCount, value);
        //     this.releaseResource(value, key); // 释放每个资源
        //     // this.removeResource(key); // 从缓存中移除资源
        // });
        // this.resourceCache.clear(); // 清空缓存
        GameUtil.log("clearCache" + this.resourceCache);
        GameUtil.log("clearCache" + this.resourceMapping);
    }

}
//方法使用
// const cacheManager = CacheManager.instance;
// const spriteAtlas = await cacheManager.loadAndCacheSpriteAtlas(`chess_${skinId}`, skinId, AssetType.CHESS_PIECE);
// const spriteFrame = await cacheManager.loadAndCacheSpriteFrame(`image_domino_front_20001.png`, id, AssetType.DOMINO_TABLECLOTH);
// const skData = await cacheManager.loadAndCacheSpine(`faceframe_${id}`, id, AssetType.AVATAR_FRAME);

