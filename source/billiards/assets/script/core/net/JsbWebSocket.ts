import { ReconnectCtrl } from "../../common/reconnect/ReconnectCtrl";
import { DeviceType } from "../../game/data/Enum";
import GameData from "../../game/data/GameData";
import Global from "../data/Global";
import { EventMgr } from "../event/EventManager";
import { GameUtil } from "../tools/GameUtil";
import { JsbBrid } from "../tools/JsbBridge";

export class SocketEnumEvent {
    static Socket_Open = 'onNetOpen';                                 //原生通知socket已连上 
    static Socket_Message = 'Socket_Message';
    static Socket_Connecting = 'Socket_Connecting';                   //只展示小马动画，不发起重连
    static Socket_Message_Watch = 'Socket_Message_Watch';             //现在联赛观战 消息盒子
    static Socket_Message_Response = 'Socket_Message_Response';		  //联赛观战（第一版观战，可能已经不再用，这里继续保留）
    static Socket_Close = 'Socket_Close';
    static Socket_Error = 'Socket_Error';
    static Socket_Watch_Error = 'Socket_Watch_Error';				//联赛观战返回错误
}

export class JsbWebSocket {
    public static _instance: JsbWebSocket;

    static get instance(): JsbWebSocket {
        if (!this._instance)
            this._instance = new JsbWebSocket();
        return this._instance;
    }

    /**try网关、新联赛观战、联赛、老协议都是 onNetMsg*/
    public toJs_onNetMsg(msg) {
        if (typeof msg == "string") msg = eval(msg);
        EventMgr.raiseEvent(SocketEnumEvent.Socket_Message, msg);
        // }
    }
    /**第一版联赛观战 走onNetResponseMsg*/
    public toJs_onNetResponseMsg(msg) {
        // console.log("--NativeWebSocket.onNetResponseMsg--");
        // GameUtil.log(msg)
        EventMgr.raiseEvent(SocketEnumEvent.Socket_Message_Response, msg);
    }

    public toJs_onNetClose(msg) {
        GameUtil.log("--JsbWebSocket.onNetClose--")
        EventMgr.raiseEvent(SocketEnumEvent.Socket_Close, msg);
    }

    public toJs_onNetError(msg) {
        GameUtil.log("--JsbWebSocket.onNetError--")
        EventMgr.raiseEvent(SocketEnumEvent.Socket_Error, msg);
    }

    public toJs_onNetConnecting(msg) {
        GameUtil.logCatch("--JsbWebSocket.onNetConnecting--")
                //TODO：：有watch 专属加载数据动画 需要先把动画关掉，再抛事件；
        if (!ReconnectCtrl.ins.isReconnect && !GameData.isGameOver) {
            EventMgr.raiseEvent(SocketEnumEvent.Socket_Connecting);
        }
        Global.isconnect = false;
        ReconnectCtrl.ins.connect(true);
    }

    public toJs_onWatchError(msg) {
        GameUtil.log("--JsbWebSocket.onWatchError--");
        GameUtil.log(msg)
        EventMgr.raiseEvent(SocketEnumEvent.Socket_Watch_Error, msg);
    }

    init() {

    }

    /**
    * 通知原生连socket
    */
    connectSocket(): void {
        var url = `${Global.gameInfo.host}:${Global.gameInfo.port}`;
        if (Global.gameInfo.url && Global.gameInfo.url.length > 0) {

            url = Global.gameInfo.url;
            if (JsbBrid.deviceType == DeviceType.Android && Number(Global.gameInfo.androidVersion) < 23) {
                //TODO::端口0或1以下则不拼port    *******
                if (Number(Global.gameInfo.port) <= 0) url = Global.gameInfo.host;
                else url = `${Global.gameInfo.host}:${Global.gameInfo.port}`;
            }
        }

        // url = 'ws://*************:9091'
        // url = 'ws://fat-domino.yalla.games:9038';
        // YallaGlobal.Account.port = '9037';
        // YallaGlobal.Account.url = url;
        // url = 'ws://*************:9091';
        var data = JSON.stringify({
            url: url,
            gameId: Global.gameInfo.gameId,
            connectType: 2
        });
        GameUtil.log(url + '-**-' + Global.gameInfo.url + '---通知原生连socket--connectSocket--url:' + url + '--' + Number(Global.gameInfo.androidVersion));
        JsbBrid.sendToNative('connectSocket', data);
    }

    /**
     * 发送请求
     * @param jsonstring  bytearray
     */
    sendNetMsg(json: any) {
        JsbBrid.sendToNative('sendNetMsg', JSON.stringify(json))
    }

    /**
     * 发送请求(观战)
     * @param jsonstring  bytearray
     */
    sendNetMsg_watch(json: any) {//command 5聊天 6退出房间
        GameUtil.log('-----发送联赛观战消息sendNetMsg----' + JSON.stringify(json));
        GameUtil.log("sendNetMsg_watch:" + JSON.stringify(json))
        JsbBrid.sendToNative('sendNetMsg_watch', JSON.stringify(json))
    }
    /**
     * 断线重连
     * @param
     */
    sendNetReConnect() {
        GameUtil.log('-----sendNetReConnect----');
        JsbBrid.sendToNative('sendNetReConnect')
    }

    /**
     * close socket
     * @param
     */
    sendNetClose() {
        GameUtil.log('-----sendNetClose----');
        JsbBrid.sendToNative('sendNetClose')
    }
}