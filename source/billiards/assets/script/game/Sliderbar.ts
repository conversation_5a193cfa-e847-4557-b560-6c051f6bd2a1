import { _decorator, Component, Node, Input, EventTouch, UITransform, v3, UIOpacity } from 'cc';
import Utils from '../core/tools/Utils';
import BallCtrl from './ball/BallCtrl';
import { Cue } from './cue/Cue';
import GameData from './data/GameData';
import GameService from './data/GameService';
import BaseTipMgr from '../core/tools/BaseTipMgr';

const { ccclass } = _decorator;

@ccclass('Sliderbar')
export class Sliderbar extends Component {
    private _sliderPower: Node = null;
    private _tfPower: UITransform = null;
    private _powerOpacity: UIOpacity = null;
    private _sliderMaskOpacity: UIOpacity = null;
    private _ndSlideCue: Node = null;

    private _cue: Cue = null;

    private _percent = 0;
    private _isDrag: boolean = false;
    private _isMove: boolean = false;

    onLoad() {
        this.node.on(Input.EventType.TOUCH_START, this._touchStart, this);
        this.node.on(Input.EventType.TOUCH_MOVE, this._touchHandler, this);
        this.node.on(Input.EventType.TOUCH_END, this._touchEnd, this);
        this.node.on(Input.EventType.TOUCH_CANCEL, this._touchEnd, this);

        this._sliderPower = this.node.getChildByName('sliderPower');
        this._tfPower = this._sliderPower.getComponent(UITransform);
        this._powerOpacity = this._sliderPower.getComponent(UIOpacity);
        this._ndSlideCue = this.node.getChildByPath('mask/sliderCue');
        this._sliderMaskOpacity = this._ndSlideCue.parent.getComponent(UIOpacity);
        
        this._sliderPower.active = false;
        this.resetSlider();
    }

    setCue(cue: Cue) {
        this._cue = cue;
    }

    private _touchStart(e: EventTouch) {
        if (this._cue && !this._cue.canOperate || !this._isDrag) return;
        const location = Utils.getUILocation(e);
        const pos = this._tfPower.convertToNodeSpaceAR(v3(location.x, location.y, 0));
        let moveY = this._tfPower.height * 0.5 - pos.y;
        if (moveY < 0) moveY = 0;
        if (moveY > this._tfPower.height) moveY = this._tfPower.height;
        let percent = this._percent = moveY / this._tfPower.height;

        this.handleCuePower(percent, -moveY);
        GameService.instance.DragMoveRequest({
            cue: JSON.stringify({ powerPercent: percent, moveY: -moveY, event: "start" }),
            whiteBall: JSON.stringify({ x: this._cue.ballPos.x, y: this._cue.ballPos.y })
        });
    }

    private _frameCount = 0;
    private _touchHandler(e: EventTouch) {
        if (this._cue && !this._cue.canOperate || !this._isDrag) return;
        this._isMove = true;

        if (this._frameCount % 2 == 0) {
            const location = Utils.getUILocation(e);
            const pos = this._tfPower.convertToNodeSpaceAR(v3(location.x, location.y, 0));
            let moveY = this._tfPower.height * 0.5 - pos.y;
            if (moveY < 0) moveY = 0;
            if (moveY > this._tfPower.height) moveY = this._tfPower.height;
            let percent = this._percent = moveY / this._tfPower.height;
            this.handleCuePower(percent, -moveY);

            GameService.instance.DragMoveRequest({
                cue: JSON.stringify({ powerPercent: percent, moveY: -moveY }),
                whiteBall: JSON.stringify({ x: this._cue.ballPos.x, y: this._cue.ballPos.y })
            });
        }
        this._frameCount += 1;
    }

    private _touchEnd() {
        this._frameCount = 0;
        this._isMove = false;
        if (this._cue && !this._cue.canOperate || !this._isDrag) return;
        if (GameData.lineH > GameData.LineMaxLen || GameData.lineR > GameData.LineMaxLen) {
            BaseTipMgr.showTip(BaseTipMgr.codes.cueLine);
            this.resetSlider();
            return;
        }
        let power =GameData.getHitballPower(this._percent);
        let angle = this._cue.node.angle;
        let rad = this._cue._rad;
        if (power <= 0 || this._percent <= 0) return;

        let resultData = BallCtrl.syncBalls({ x: power, y: 0, percent: this._percent }, angle, rad, this._cue.dir, 'end');
        this._cue.shootCueBall(power, this._percent);
        this.resetSlider();
        GameService.instance.BallHitRequest(resultData, GameData.lineH, GameData.lineR);

        BallCtrl.onBallsSyncFinish();
        this.updateGray(true);
    }

    handleCuePower(percent: number, moveY: number) {
        if (this._sliderPower.active) {
            this._ndSlideCue.setPosition(v3(0, -7 + moveY, 0));
            this._powerOpacity.opacity = Math.max(80, percent * 255);
        }
        this._cue && (this._cue.changeCueAnchorY(percent * 0.22));
    }

    resetSlider() {
        if (this._sliderPower.active) {
            this._powerOpacity.opacity = 80;
            this._ndSlideCue.setPosition(v3(0, -7, 0));
            this._sliderMaskOpacity.opacity = 255;
        } else {
            this._sliderMaskOpacity.opacity = 80;
        }
    }

    updateGray(isGray: boolean) {
        this._isDrag = !isGray;
        this._sliderPower.active = !isGray;
        this.resetSlider();
    }

    updateActive(isShow: boolean) {
        this._sliderPower.active = isShow;
    }

    stopDrag() {
        this._isDrag = false;
        if (this._isMove) {
            this._isMove = false;
            this.handleCuePower(0, 0);
            this._frameCount = 0;
            GameService.instance.DragMoveRequest({
                cue: JSON.stringify({ powerPercent: 0, moveY: 0, event: 'end' }),
            });
        }
    }
}

