import { GameUtil } from '../../core/tools/GameUtil';
import TipManager from '../manager/TipManager';
import { Match } from '../../match/Match';
import Global from '../../core/data/Global';
import { ErrorCode, MatchErrorCode } from './Enum';
import GameService from './GameService';
import { dialogManager } from '../../common/dialog/DialogManager';
import { ConfirmParams } from './GameInterface';
import i18n from '../../core/tools/i18n';

/**
 * 匹配消息管理器
 * 负责处理匹配过程中的消息队列，确保消息按指定顺序处理
 * 支持消息重试机制和错误处理
 */
export class MatchMessageManager {
    /** 单例实例 */
    public static _instance: MatchMessageManager;
    /** 当前匹配实例 */
    private _match: Match | null = null;
    /** 消息队列，使用Map存储，key为消息类型，value为消息数据 */
    private _messageMap: Map<string, any> = new Map();
    /** 已处理的消息类型集合 */
    private _processedTypes: Set<string> = new Set();
    /** 是否正在处理消息 */
    private _isProcessing: boolean = false;
    /** 上次处理消息的时间戳 */
    private _lastProcessTime: number = 0;
    /** 消息处理定时器 */
    private _processingTimer: number | null = null;
    /** 消息处理顺序，固定为 match -> matchInfo -> matchStart */
    private readonly MESSAGE_ORDER = ['match', 'matchInfo', 'matchStart'] as const;
    /** 消息处理间隔时间（毫秒） */
    private readonly MESSAGE_INTERVALS = {
        match: 4000,    // match 和 matchInfo 之间间隔 4 秒
        matchInfo: 2000, // matchInfo 和 matchStart 之间间隔 2 秒
        matchStart: 0    // matchStart 后立即处理
    } as const;
    /** 最大重试次数 */
    private readonly MAX_RETRY_COUNT = 3;
    /** 当前重试次数 */
    private _retryCount: number = 0;
    
    /** 私有构造函数，确保单例模式 */
    private constructor() {}

    /** 获取单例实例 */
    static get instance(): MatchMessageManager {
        return this._instance || (this._instance = new MatchMessageManager());
    }

    /**
     * 设置当前匹配实例
     * @param match 匹配实例
     */
    setMatch(match: Match): void {
        GameUtil.log(`[MatchMessageManager] 设置Match实例: ${match ? '成功' : 'null'}`);
        this._match = match;
    }

    /**
     * 将消息加入队列
     * @param type 消息类型
     * @param data 消息数据
     */
    enqueueMessage(type: string, data: any): void {
        GameUtil.log(`[MatchMessageManager] enqueueMessage type=${type}, data=${JSON.stringify(data)}`);
        // 检查消息类型是否有效
        const isValidType = this.MESSAGE_ORDER.some(t => t === type);
        if (!isValidType) {
            GameUtil.log(`[MatchMessageManager] 收到未知类型的消息: ${type}`);
            return;
        }

        // 如果消息类型已经存在，则替换旧消息
        if (this._messageMap.has(type)) {
            GameUtil.log(`[MatchMessageManager] 替换已存在的消息: type=${type}`);
        } else {
            GameUtil.log(`[MatchMessageManager] 收到新消息: type=${type}`);
        }
        this._messageMap.set(type, data);
        
        // 如果当前没有在处理消息，则开始处理
        if (!this._isProcessing) {
            GameUtil.log(`[MatchMessageManager] 开始处理消息队列`);
            this.processNextMessage();
        }
    }

    /**
     * 处理下一条消息
     * 按照MESSAGE_ORDER定义的顺序处理消息
     * 确保消息处理间隔为MESSAGE_INTERVALS中定义的时间
     */
    private async processNextMessage(): Promise<void> {
        try {
            // 检查消息队列是否为空
            if (this._messageMap.size === 0) {
                GameUtil.log(`[MatchMessageManager] 消息队列已空，停止处理`);
                this.resetProcessingState();
                return;
            }

            this._isProcessing = true;
            const currentTime = Date.now();
            const timeSinceLastProcess = currentTime - this._lastProcessTime;
            
            // 获取下一个要处理的消息类型
            const nextMessageType = this.findNextMessageType();
            if (!nextMessageType) {
                GameUtil.log(`[MatchMessageManager] 没有找到符合顺序的消息，清空队列`);
                this.resetProcessingState();
                return;
            }

            // 获取当前消息类型的处理间隔
            const interval = this.MESSAGE_INTERVALS[nextMessageType];
            
            // 检查是否需要等待处理间隔
            if (timeSinceLastProcess < interval) {
                const waitTime = interval - timeSinceLastProcess;
                GameUtil.log(`[MatchMessageManager] 等待消息处理间隔: ${waitTime}ms`);
                this._processingTimer = window.setTimeout(() => this.processNextMessage(), waitTime);
                return;
            }

            // 处理消息
            const message = { type: nextMessageType, data: this._messageMap.get(nextMessageType) };
            this._messageMap.delete(nextMessageType);

            await this.processMessage(message);
            
            // 更新处理时间和重试计数
            this._lastProcessTime = Date.now();
            this._retryCount = 0;
            
            // 如果是 matchStart 消息，处理完成后清空队列
            if (nextMessageType === 'matchStart') {
                GameUtil.log(`[MatchMessageManager] matchStart消息处理完成，清空消息队列`);
                this.clear();
                return;
            }
            
            // 设置下一条消息的处理延迟
            const nextType = this.findNextMessageType();
            const nextInterval = nextType ? this.MESSAGE_INTERVALS[nextType] : 0;
            GameUtil.log(`[MatchMessageManager] 设置下一条消息处理延迟: ${nextInterval}ms`);
            this._processingTimer = window.setTimeout(() => this.processNextMessage(), nextInterval);
        } catch (error) {
            GameUtil.log(`[MatchMessageManager] 处理消息时发生错误: ${error}`);
            this.handleProcessingError();
        }
    }

    /**
     * 查找下一个要处理的消息类型
     * 按照MESSAGE_ORDER定义的顺序查找
     * 确保前一个类型的消息已处理完成
     * @returns 下一个要处理的消息类型，如果没有则返回null
     */
    private findNextMessageType(): string | null {
        for (const type of this.MESSAGE_ORDER) {
            // 如果前一个类型的消息还没处理，就不能处理当前类型的消息
            const prevTypeIndex = this.MESSAGE_ORDER.indexOf(type) - 1;
            if (prevTypeIndex >= 0 && !this._processedTypes.has(this.MESSAGE_ORDER[prevTypeIndex])) {
                GameUtil.log(`[MatchMessageManager] 等待前一个消息处理完成: ${this.MESSAGE_ORDER[prevTypeIndex]}`);
                continue;
            }
            if (this._messageMap.has(type)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 处理具体消息
     * @param message 消息对象，包含类型和数据
     */
    private async processMessage(message: { type: string, data: any }): Promise<void> {
        GameUtil.log(`[MatchMessageManager] 开始处理消息: type=${message.type}`);

        try {
            switch (message.type) {
                case 'match':
                    await this.processMatchMessage(message.data);
                    this._processedTypes.add('match');
                    break;
                case 'matchInfo':
                    await this.processMatchInfoMessage(message.data);
                    this._processedTypes.add('matchInfo');
                    break;
                case 'matchStart':
                    await this.processMatchStartMessage(message.data);
                    this._processedTypes.add('matchStart');
                    break;
            }
        } catch (error) {
            GameUtil.log(`[MatchMessageManager] 处理${message.type}消息时发生错误: ${error}`);
            throw error;
        }
    }

    /**
     * 处理匹配消息
     * @param data 匹配消息数据
     */
    private async processMatchMessage(data: any): Promise<void> {
        GameUtil.log(`[MatchMessageManager] 处理match消息: result=${data.result}, packageType=${data.packageType}`);
        if (this._match && data.result === 1 && data.packageType === 3) {
            await this._match._initViewMatchVs();
            GameUtil.log(`[MatchMessageManager] match消息处理完成`);
        } else if (data.result === 0 && data.code) {
            GameUtil.log(`[MatchMessageManager] match消息处理失败: code=${data.code}`);
            this.handleErrorCode(data.code);
        }
    }

    /**
     * 处理匹配信息消息
     * @param data 匹配信息消息数据
     */
    private async processMatchInfoMessage(data: any): Promise<void> {
        GameUtil.log(`[MatchMessageManager] 处理matchInfo消息: packageType=${data.packageType}, 是否有玩家数据=${!!data.player}`);
        if (this._match && data.packageType === 3 && data.player) {
            for (const { idx, faceUrl, nikeName } of data.player) {
                if (idx !== Global.userId) {
                    GameUtil.log(`[MatchMessageManager] 初始化玩家信息: idx=${idx}, nikeName=${nikeName}`);
                    await this._match.initMatchVsPlayer(idx, faceUrl, nikeName);
                }
            }
            GameUtil.log(`[MatchMessageManager] matchInfo消息处理完成`);
        } else if (data.result === 0 && data.code) {
            GameUtil.log(`[MatchMessageManager] matchInfo消息处理失败: code=${data.code}`);
            this.handleErrorCode(data.code);
        }
    }

    /**
     * 处理匹配开始消息
     * @param data 匹配开始消息数据
     */
    private async processMatchStartMessage(data: any): Promise<void> {
        GameUtil.log(`[MatchMessageManager] 处理matchStart消息: packageType=${data.packageType}, roomid=${data.roomid}`);
        if (data.packageType === 3 && data.roomid != null && data.token != null && data.connectType == 1) {
            Global.gameInfo.roomId = data.roomid;
            Global.gameInfo.token = data.token;
            Global.gameInfo.instanceId = data.instanceId;

            if (this._match) {
                Global.gameState = 1;
                GameUtil.log(`[MatchMessageManager] 准备开始匹配: roomId=${data.roomid}`);
                await this._match.matchSuccess();
                GameUtil.log(`[MatchMessageManager] matchStart消息处理完成`);
            }
        } else if (data.result === 0 && data.code) {
            GameUtil.log(`[MatchMessageManager] matchStart消息处理失败: code=${data.code}`);
            this.handleErrorCode(data.code);
        }
    }

    /**
     * 处理消息处理错误
     * 实现重试机制，超过最大重试次数则停止处理
     */
    private handleProcessingError(): void {
        this._retryCount++;
        if (this._retryCount >= this.MAX_RETRY_COUNT) {
            GameUtil.log(`[MatchMessageManager] 达到最大重试次数，停止处理`);
            this.resetProcessingState();
        } else {
            const retryDelay = this.MESSAGE_INTERVALS.match * this._retryCount;
            GameUtil.log(`[MatchMessageManager] 准备重试: 第${this._retryCount}次, 延迟${retryDelay}ms`);
            this._processingTimer = window.setTimeout(() => this.processNextMessage(), retryDelay);
        }
    }

    /**
     * 重置处理状态
     * 清除定时器，重置处理标志和重试计数
     */
    private resetProcessingState(): void {
        if (this._processingTimer) {
            clearTimeout(this._processingTimer);
            this._processingTimer = null;
        }
        this._isProcessing = false;
        this._retryCount = 0;
    }

    /**
     * 清理资源
     * 重置所有状态，清空消息队列
     */
    clear(): void {
        GameUtil.log(`[MatchMessageManager] 清理资源: 当前消息数量=${this._messageMap.size}`);
        this.resetProcessingState();
        this._messageMap.clear();
        this._processedTypes.clear();
        this._lastProcessTime = 0;
        this._match = null;
    }

    /**
     * 处理错误码
     * 根据不同的错误码显示相应的提示信息
     * @param errorCode 错误码
     */
    handleErrorCode(errorCode: number): void {
        GameUtil.log(`[MatchMessageManager] 处理错误码: ${errorCode}`);
        if (errorCode === ErrorCode.NO_TOAST) {
            return;
        }
        let params: ConfirmParams = { tip: '', single: true }, msg = '';
        switch (errorCode) {
            case 401:
            case 402:
            case ErrorCode.NO_MONEY:
            case ErrorCode.NO_DIAMOND:
            case MatchErrorCode.NO_ENOUGH_MONEY:
            case MatchErrorCode.NO_ENOUGH_DIAMOND:
                GameUtil.log(`[MatchMessageManager] 显示余额不足提示`);
                params = {
                    tip: i18n.t('dialog_recharge'),
                    single: false,
                    cancelTx: i18n.t('iknow'),
                    confirmTx: i18n.t('recharge'),
                    confirm: () => {
                        GameService.instance.backHall(false, { to: 'shop' });
                    },
                    cancel: () => {
                        //TODO::匹配失败，需要显示赌注界面
                        this._match?.initInfo();
                    }
                }
                break;
                
            case ErrorCode.MAX_LIMIT:
                GameUtil.log(`[MatchMessageManager] 显示每日限制提示`);
                msg = 'toast_match_failed_daily_limit';
                break;
                
            case 403:
            case ErrorCode.ACCOUNT_BE_FROZEN:
                GameUtil.log(`[MatchMessageManager] 显示账号冻结提示`);
                params = {
                    tip: i18n.t('error_AccountFrozen'),
                    single: true,
                    confirm: () => {
                        GameService.instance.backHall();
                    },
                }
                break;
                
            case MatchErrorCode.MATCH_GAME_FAILD:
            case MatchErrorCode.MATCH_GAME_FAILD_WRONGINFO:
            case MatchErrorCode.MATCH_GAME_FAILD_OUTTIME:
            case MatchErrorCode.MATCH_GAME_FAILED_CLOSED:
            case MatchErrorCode.MATCH_GAME_FAILD_NO_GAME:
                GameUtil.log(`[MatchMessageManager] 显示匹配失败提示`);
                msg = 'toast_match_failed';
                break;
                
            case MatchErrorCode.MATCH_GAME_FAILD_REPAIRS:
                GameUtil.log(`[MatchMessageManager] 显示维护中提示`);
                msg = 'toast_game_maintenance';
                break;
                
            case MatchErrorCode.MATCH_GAME_FAILD_INMATCH:
                GameUtil.log(`[MatchMessageManager] 显示已在匹配中提示`);
                msg = 'toast_already_matching';
                break;
                
            case MatchErrorCode.MATCH_GAME_FAILD_PLAYER_NUM:
                GameUtil.log(`[MatchMessageManager] 显示人数不足提示`);
                msg = 'toast_not_enough_players';
                break;
                
            case MatchErrorCode.MATCH_GAME_FAILD_DAILY_LIMIT:
                GameUtil.log(`[MatchMessageManager] 显示每日限制提示`);
                msg = 'toast_daily_limit_reached';
                break;
                
            case MatchErrorCode.MATCH_CANCEL_FAIL:
                GameUtil.log(`[MatchMessageManager] 显示取消匹配失败提示`);
                msg = 'toast_cancel_match_failed';
                break;
            case 1108: 
            case MatchErrorCode.MATCH_FAIL_ILLEGAL_PUNISH:
                GameUtil.log(`[MatchMessageManager] 显示非法游戏惩罚提示`);
                msg = 'toast_illegal_game_punishment';
                break;
                
            case MatchErrorCode.MATCH_GAME_SERVER_NOT_FOUND:
                GameUtil.log(`[MatchMessageManager] 显示无游戏服务器提示`);
                msg = 'toast_no_game_server';
                break;
                
            case MatchErrorCode.MATCH_GAME_NOT_EXISTS:
                GameUtil.log(`[MatchMessageManager] 显示房间不存在提示`);
                msg = 'error_room_not_exist';
                break;
                
            case MatchErrorCode.DB_NET_ERROE:
                GameUtil.log(`[MatchMessageManager] 显示数据库错误提示`);
                msg = 'toast_match_failed';
                break;
                
            case MatchErrorCode.LOGIN_FAILD_ID:
                GameUtil.log(`[MatchMessageManager] 显示登录ID错误提示`);
                msg = 'error_IDError';
                break;
                
            case MatchErrorCode.PACKAGE_NAME_ERROR:
            case MatchErrorCode.PACKAGE_INFO_EXPIRE:
                GameUtil.log(`[MatchMessageManager] 显示包信息错误提示`);
                msg = 'toast_match_failed';
                break;
                
            case MatchErrorCode.MATCH_GAME_FAILD_WRONG_ROOM:
                GameUtil.log(`[MatchMessageManager] 显示房间不存在提示`);
                msg = 'error_room_not_exist';
                break;
                
            default:
                GameUtil.log(`[MatchMessageManager] 显示默认匹配失败提示`);
                msg = 'toast_match_failed';
                break;
        }
        msg == '' ? this.showDialog(params) : this.showToast(msg);
    }

    private showToast(msg: string): void {
        TipManager.showToast(msg);
    }

    private showDialog(params: ConfirmParams): void {
        if (params.tip !== '') {
            dialogManager.showConfirm(params);
        }
    }
} 