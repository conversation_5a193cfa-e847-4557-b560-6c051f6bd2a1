import MD5 from '../../core/tools/MD5';
import { GameUtil } from '../../core/tools/GameUtil';
import Global from '../../core/data/Global';
import { DeviceType } from './Enum';
import { JsbBrid } from '../../core/tools/JsbBridge';

/**
 * MD5 使用示例
 * 演示如何在项目中使用 MD5 工具类进行加密
 */
class MD5Sign{
    /**
     * 演示 MD5 加密的几个常见用例
     */
    testMD5() {
        // 示例1：简单字符串加密
        const text = 'Hello, World!';
        const hash1 = MD5.hash(text);
        GameUtil.log(`MD5("${text}") = ${hash1}`);
        // 输出: MD5("Hello, World!") = 65a8e27d8879283831b664bd8b7f0ad4

        // 示例2：空字符串加密
        const emptyText = '';
        const hash2 = MD5.hash(emptyText);
        GameUtil.log(`MD5("${emptyText}") = ${hash2}`);
        // 输出: MD5("") = d41d8cd98f00b204e9800998ecf8427e

        // 示例3：中文字符串加密
        const chineseText = '你好，世界！';
        const hash3 = MD5.hash(chineseText);
        GameUtil.log(`MD5("${chineseText}") = ${hash3}`);
        // 输出: MD5("你好，世界！") = 7eca689f0d3389d9dea66ae112e5cfd7

        // 示例4：生成请求签名
        this.generateApiSignature();
    }
    /** socket由引擎发起，需要签名认证
     * 生成socket md签名
     * sign timestamp
     */
    generateSocketSign(cb) {
        console.log("====url:", Global.gameInfo)
        if (JsbBrid.deviceType == DeviceType.Browser) {
            cb && cb({ "url": Global.gameInfo.url });
        } else {
            let params:any = {
                AppId: 'ludo',//Global.playerAccount.appId,
                "User-Agent": Global.gameInfo.userAgent,
                UserId: Global.userId,
                Version: Global.gameInfo.version,
                AppVersion: Global.gameInfo.appVersion,
                ChannelId: Global.gameInfo.channelId,
                DevicedId: Global.gameInfo.devicedId,
                packageType: 3,
                shellVersion: Global.gameInfo.shellVersion
            }
    
            // let signParams = {
            //     idx: params.UserId,
            //     token: Global.playerAccount.token,
            //     time: time,
            //     key: Global.playerAccount.key
            // }
            JsbBrid.getSocketSign((msg) => {
                if (typeof msg == 'string') msg = JSON.parse(msg);
                params['Sign'] = msg.sign;
                params['Timestamp'] = msg.timestamp;
                let headers = '';
                // console.log("==参数信息===", params);
                for (let key in params) {
                    headers += `${key}=${params[key]}&`;
                }
                // console.log('最终请求参数:', params);
                // console.log(headers, Global.playerAccount.url);
    
                cb && cb({ url: `${Global.gameInfo.url}?${headers}` });
            })
        }
    }

    /**
     * 演示如何使用 MD5 生成API请求签名
     * 常见的签名方式是将参数按字母顺序排序后拼接，再加上密钥，最后进行MD5加密
     */
    generateApiSignature() {
        // 假设这是请求参数
        const params = {
            userId: '12345',
            timestamp: Date.now().toString(),
            action: 'getUserInfo'
        };
        
        // 假设这是应用的密钥
        const appSecret = 'mySecretKey';
        
        // 按字母顺序排序参数
        const sortedKeys = Object.keys(params).sort();
        
        // 构建签名字符串
        let signString = '';
        sortedKeys.forEach(key => {
            signString += `${key}=${params[key]}&`;
        });
        
        // 添加密钥
        signString += `key=${appSecret}`;
        
        // 计算签名
        const signature = MD5.hash(signString);
        
        GameUtil.log('签名字符串:', signString);
        GameUtil.log('生成的签名:', signature);
        
        // 现在可以将签名添加到请求中
        params['sign'] = signature;
        
        GameUtil.log('最终请求参数:', params);
    }

    /**
     * 实际开发中常见的用法：对密码进行 MD5 加密后再传输
     * @param username 用户名
     * @param password 密码
     */
    login(username: string, password: string) {
        // 对密码进行MD5加密
        const encryptedPassword = MD5.hash(password);
        
        // 模拟发送登录请求
        GameUtil.log(`用户 ${username} 尝试登录，加密后的密码: ${encryptedPassword}`);
        
        // 实际项目中会发送到服务器
        // Http.post('/api/login', { username, password: encryptedPassword });
    }
} 
export default new MD5Sign();