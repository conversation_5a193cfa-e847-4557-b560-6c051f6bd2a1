import { _decorator, Component, Mat4, RigidBody2D, Sprite, SpriteFrame, v3, v2, color, Material, Vec2, Vec3, Node, Animation } from 'cc';
import { BallType } from '../data/Enum';
import GameData from '../data/GameData';
import { EnumEvent, EventMgr } from '../../core/event/EventManager';
import Global from '../../core/data/Global';
const { ccclass, property } = _decorator;
@ccclass('Ball')
export class Ball extends Component {
    @property(Sprite)
    public ballSp: Sprite;
    
    @property
    public scrollSpeed = 0;

    @property
    public rotationSpeedMultiplier: number = 10.0; // 进一步增加旋转速度，让旋转节奏更协调 8.0

    @property(Animation)
    public ballAni: Animation = null; // 可操作球动画

    private stopSpeed = 0.8;//1;
    private matrix = new Mat4();
    private mat = [
        1, 0, 0,
        0, 1, 0,
        0, 0, 1]

    public scrollAxis = v3(1, 0, 0);
    public rb2D: RigidBody2D = null;
    private _isEnter: boolean = false;
    private _tweenMove: boolean = false; //入袋运动停止
    /**进入球袋的tag（1-6 为播放入袋动画位置做的记录） */
    public enterPacketTag: number = 0;
    public speed = 0;
    
    // 旋转状态缓存
    private _lastVelocity: Vec2 = v2(0, 0);
    private _rotationDirty: boolean = true;
    /**母球是否无效位置 */
    public isValidPos: boolean = false; 

    onLoad() {
        this.rb2D = this.getComponent(RigidBody2D);
        this.ballSp = this.node.getChildByName('ballSp').getComponent(Sprite);
    }

    init(num, texture: SpriteFrame, material:Material) {
        /**球，0：白球 */
        this.node.name = num + '';
        this.ballSp.customMaterial = material;
        this.ballSp.spriteFrame = texture;;
        this.ballSp.color = color().fromHEX(GameData.ballColor[num]);
    }

    updateBall(x, y) {
        this.isEnter = false;
        this.tweenMove = false;//避免上一回合，同步异常，球进洞动画未结束，twwenMove未重置，影响下一回合是否发球停消息
        this.updatePos(x, y);
    }

    resetBall() {
        this.tweenMove = false;
        this.node.setScale(1, 1, 1);
        this.resetVelocity()
        this.setPhysicalState(true);
        // 重置旋转矩阵
        this._resetRotationMatrix();
    }

    /**
     * 重置旋转矩阵为单位矩阵
     */
    public _resetRotationMatrix() {
        this.mat = [
            1, 0, 0,
            0, 1, 0,
            0, 0, 1
        ];
        this._updateMaterialMatrix();
    }

    /**
     * 检测并修正贴库边球的异常滑动
     */
    private _checkTableSideSliding(linearVelocityLen) {
        if (!this.rb2D || linearVelocityLen === 0 || this.isEnter) return;

        const velocity = this.rb2D.linearVelocity;
        const position = this.node.position;

        // 检测是否接近库边（基于球桌边界）
        const tableMargin = 18; // 库边检测边距
        const ballRadius = 16; // 球半径
        const minDistanceToWall = ballRadius + 2; // 最小距离库边的距离

        // 球桌边界（从GameData获取）
        const leftBound = -234 + tableMargin;
        const rightBound = 234 - tableMargin;
        const bottomBound = -467 + tableMargin;
        const topBound = 467 - tableMargin;

        let isNearWall = false;
        let wallNormal = v2(0, 0);

        // 检测各个方向的库边
        if (position.x <= leftBound + minDistanceToWall) {
            isNearWall = true;
            wallNormal = v2(1, 0); // 左墙法线向右
        } else if (position.x >= rightBound - minDistanceToWall) {
            isNearWall = true;
            wallNormal = v2(-1, 0); // 右墙法线向左
        } else if (position.y <= bottomBound + minDistanceToWall) {
            isNearWall = true;
            wallNormal = v2(0, 1); // 下墙法线向上
        } else if (position.y >= topBound - minDistanceToWall) {
            isNearWall = true;
            wallNormal = v2(0, -1); // 上墙法线向下
        }

        if (isNearWall && velocity.length() < 3.0 && velocity.length() > 0.1) {
            // 计算速度在墙面切线方向的分量
            const tangent = v2(-wallNormal.y, wallNormal.x);
            const tangentVelocity = velocity.dot(tangent);
            const normalVelocity = velocity.dot(wallNormal);

            // 如果主要是沿墙面滑动，减少这种运动
            if (Math.abs(tangentVelocity) > Math.abs(normalVelocity) * 2) {
                // 减少切线方向的速度，保持法线方向的速度
                const correctedVelocity = v2(
                    wallNormal.x * normalVelocity + tangent.x * tangentVelocity * 0.2,
                    wallNormal.y * normalVelocity + tangent.y * tangentVelocity * 0.2
                );
                this.rb2D.linearVelocity = correctedVelocity;
            }
        }
    }

    /**
     * 根据物理速度计算旋转轴和角速度
     */
    private _calculateRotationFromVelocity(velocity: Vec2): { axis: Vec3, angularSpeed: number } {
        const speed = velocity.length();
        if (speed < 0.01) {
            return { axis: v3(1, 0, 0), angularSpeed: 0 };
        }

        // 计算旋转轴：垂直于移动方向
        // 对于2D平面运动，旋转轴在3D空间中为 (vy, vx, 0)
        // 修复Y轴旋转方向：调整旋转轴计算
        const rawAxis = v3(velocity.y, velocity.x, 0); // 修正旋转方向
        
        // 必须归一化！否则会导致旋转矩阵异常，进而导致shader材质丢失
        const axis = rawAxis.normalize();
        
        // 安全检查：确保归一化后的轴向量有效
        if (!axis || isNaN(axis.x) || isNaN(axis.y) || isNaN(axis.z)) {
            // console.warn(`[Ball ${this.ballId}] 旋转轴计算异常，使用默认轴`);
            return { axis: v3(1, 0, 0), angularSpeed: 0 };
        }
        
        // 优化角速度计算：让旋转与运动速度更协调
        // 使用更符合视觉感受的速度映射关系
        const baseAngularSpeed = speed / GameData.ballRadius; // 基础物理角速度 ω = v/r
        // console.log(`[Ball ${this.ballId}] _calculateRotationFromVelocity, 速度:`, speed, '基础角速度:', baseAngularSpeed);
        // 根据速度范围调整旋转倍数，让高速和低速都有合适的旋转效果
        let speedMultiplier;
        if (speed > 50) {
            // 高速时：旋转稍微慢一点，避免过于眩晕
            speedMultiplier = this.rotationSpeedMultiplier;//0.8
        } else if (speed > 20) {
            // 中速时：正常旋转速度
            speedMultiplier = this.rotationSpeedMultiplier * 2;//1.0
        } else {
            // 低速时：旋转稍微快一点，确保能看到效果
            speedMultiplier = this.rotationSpeedMultiplier * 4;//1.5
        }
        
        const angularSpeed = baseAngularSpeed * speedMultiplier;
        
        // 安全检查：确保角速度有效
        if (isNaN(angularSpeed) || !isFinite(angularSpeed)) {
            // console.warn(`[Ball ${this.ballId}] 角速度计算异常: ${angularSpeed}`);
            return { axis: v3(1, 0, 0), angularSpeed: 0 };
        }
        
        return { axis, angularSpeed };
    }

    /**
     * 矩阵旋转计算
     */
    scroll(rad, x, y, z) {
        // 参数安全检查：防止异常值导致矩阵计算错误
        if (isNaN(rad) || !isFinite(rad) || 
            isNaN(x) || !isFinite(x) || 
            isNaN(y) || !isFinite(y) || 
            isNaN(z) || !isFinite(z)) {
            // console.warn(`[Ball ${this.ballId}] scroll参数异常: rad=${rad}, axis=(${x},${y},${z})`);
            return; // 跳过异常的旋转计算
        }
        
        // 限制旋转角度范围，防止过大的旋转导致计算溢出
        const maxRad = Math.PI * 2; // 限制在2π范围内
        if (Math.abs(rad) > maxRad) {
            rad = rad > 0 ? maxRad : -maxRad;
        }
        
        // 矩阵旋转
        // 传入的是逆矩阵，所以计算反向旋转
        // 因为只需要计算3d旋转，所以用3x3矩阵表示
        const s = Math.sin(-rad);
        const c = Math.cos(-rad);
        const t = 1 - c;

        let a = this.mat;
        const a00 = a[0]; const a01 = a[1]; const a02 = a[2];
        const a10 = a[3]; const a11 = a[4]; const a12 = a[5];
        const a20 = a[6]; const a21 = a[7]; const a22 = a[8];

        const b00 = x * x * t + c; const b01 = y * x * t + z * s; const b02 = z * x * t - y * s;
        const b10 = x * y * t - z * s; const b11 = y * y * t + c; const b12 = z * y * t + x * s;
        const b20 = x * z * t + y * s; const b21 = y * z * t - x * s; const b22 = z * z * t + c;

        // 计算新的矩阵值
        const newMat = [
            a00 * b00 + a10 * b01 + a20 * b02,
            a01 * b00 + a11 * b01 + a21 * b02,
            a02 * b00 + a12 * b01 + a22 * b02,
            a00 * b10 + a10 * b11 + a20 * b12,
            a01 * b10 + a11 * b11 + a21 * b12,
            a02 * b10 + a12 * b11 + a22 * b12,
            a00 * b20 + a10 * b21 + a20 * b22,
            a01 * b20 + a11 * b21 + a21 * b22,
            a02 * b20 + a12 * b21 + a22 * b22
        ];
        
        // 验证计算结果的有效性
        let hasInvalidResult = false;
        for (let i = 0; i < 9; i++) {
            if (isNaN(newMat[i]) || !isFinite(newMat[i])) {
                hasInvalidResult = true;
                // console.error(`[Ball ${this.ballId}] 旋转计算产生异常值: newMat[${i}] = ${newMat[i]}`);
                break;
            }
        }
        
        // 只有在结果有效时才更新矩阵
        if (!hasInvalidResult) {
            for (let i = 0; i < 9; i++) {
                a[i] = newMat[i];
            }
        } else {
            // console.warn(`[Ball ${this.ballId}] 跳过异常的旋转计算，保持当前矩阵`);
        }
    }

    /**
     * 更新材质矩阵
     */
     _updateMaterialMatrix() {
        // 检查材质和Sprite是否有效
        if (!this.ballSp || !this.ballSp.material) {
            return;
        }

        // 矩阵有效性检查：防止异常值导致shader材质丢失
        let a = this.mat;
        let hasInvalidValue = false;
        
        for (let i = 0; i < 9; i++) {
            if (isNaN(a[i]) || !isFinite(a[i])) {
                hasInvalidValue = true;
                // console.error(`[Ball ${this.ballId}] 旋转矩阵包含异常值: mat[${i}] = ${a[i]}`);
                break;
            }
        }
        
        // 如果矩阵包含异常值，重置为单位矩阵
        if (hasInvalidValue) {
            // console.warn(`[Ball ${this.ballId}] 重置异常旋转矩阵为单位矩阵`);
            this._resetRotationMatrix();
            a = this.mat; // 使用重置后的矩阵
        }
        
        // 构建4x4矩阵，确保第4行第4列为1（齐次坐标）
        this.matrix.set(
            a[0], a[1], a[2], 0,
            a[3], a[4], a[5], 0,
            a[6], a[7], a[8], 0,
            0, 0, 0, 1) // 确保齐次坐标正确
        
        // 最终矩阵有效性检查 - 简化版本
        // 由于无法直接访问Mat4内部数据，我们通过设置操作来验证矩阵有效性
        try {
            // 尝试创建一个测试矩阵来验证当前矩阵是否有效
            const testMatrix = new Mat4();
            testMatrix.set(
                a[0], a[1], a[2], 0,
                a[3], a[4], a[5], 0,
                a[6], a[7], a[8], 0,
                0, 0, 0, 1
            );
            // 如果能成功创建，说明矩阵数据有效
            this.matrix = testMatrix;
        } catch (e) {
            // console.error(`[Ball ${this.ballId}] 矩阵创建失败，重置为单位矩阵:`, e);
            this.matrix.identity();
        }
        
        // 尝试多个可能的属性名称
        const matrixPropertyNames = ['b_matrix', 'uMatrix', 'u_matrix', 'rotationMatrix', 'transformMatrix', 'ballMatrix'];
        for (const propName of matrixPropertyNames) {
            try {
                this.ballSp.material.setProperty(propName, this.matrix);
                break;
            } catch (e) {
                // 尝试下一个属性名
            }
        }
    }

    update(dt) {
        let vlen = this.speed = this.linearVelocityLen;
        if (vlen == 0 && !this.tweenMove) return;
        
        // 检测贴库边异常移动并修正
        // this._checkTableSideSliding(this.speed);

        // 降低速度阈值，让球在更低速度时也能旋转
        if (vlen <= this.stopSpeed) {
            if (this.speed > 0) {
                this.resetVelocity();
                if (!this.tweenMove && !this.isEnter && this.speed == 0) {
                    //TODO::球停了检测是否有在洞口的球  
                    EventMgr.raiseEvent(EnumEvent.CUSTOM_BALL_MOVE_STOP, this.ballId);
                }
                // console.log(this.tweenMove + ":"+this.isEnter + ":" +this.speed+"=tweenMove=球停了=ballId=" + this.ballId +":"+ this.linearVelocityLen);
            }
        } else {
            // 获取当前刚体速度
            const currentVelocity = this.rb2D.linearVelocity;
            
            // 强制更新旋转轴（确保旋转始终基于当前速度方向）
            const rotationInfo = this._calculateRotationFromVelocity(currentVelocity);
            this.scrollAxis = rotationInfo.axis;
            this._lastVelocity.set(currentVelocity);
            
            // 优化旋转角度计算：考虑帧率变化和时间同步
            let rad = dt * rotationInfo.angularSpeed;
            
            // 帧率补偿：确保在不同帧率下旋转速度一致
            const targetFPS = 60;
            const currentFPS = 1 / dt;
            const fpsRatio = Math.min(currentFPS / targetFPS, 2.0); // 限制最大补偿倍数
            
            // 根据帧率调整旋转角度
            if (fpsRatio < 0.8) {
                // 低帧率时稍微加快旋转，保持视觉连贯性
                rad *= 1.2;
            } else if (fpsRatio > 1.5) {
                // 高帧率时稍微减慢旋转，避免过于快速
                rad *= 0.9;
            }
            
            // 大幅降低旋转角度阈值，让更多旋转能被应用
            if (Math.abs(rad) > 0.0001) {
                // 应用旋转
                this.scroll(rad, this.scrollAxis.x, this.scrollAxis.y, this.scrollAxis.z);
                
                // 更新材质矩阵
                this._updateMaterialMatrix();
            }
        }
    }

    /**
     * 设置旋转轴（用于特殊效果，如侧旋）
     * @param x 旋转轴X分量
     * @param y 旋转轴Y分量  
     * @param z 旋转轴Z分量
     * @param smooth 是否使用平滑过渡
     * @param transitionSpeed 过渡速度倍数
     */
    rotateSphere(x = 0, y = 0, z = 0, smooth: boolean = false, transitionSpeed: number = 5.0) {
        const newAxis = v3(x, y, z);
        
        if (smooth && newAxis.length() > 0) {
            // 平滑过渡到新的旋转轴
            this._smoothTransitionToNewAxis(newAxis, transitionSpeed);
        } else {
            // 立即设置新的旋转轴
            this.scrollAxis.set(newAxis);
            this._rotationDirty = true;
        }
    }

    /**
     * 平滑过渡到新的旋转轴
     * @param targetAxis 目标旋转轴
     * @param speed 过渡速度
     */
    private _smoothTransitionToNewAxis(targetAxis: Vec3, speed: number) {
        // 归一化目标轴
        const normalizedTarget = targetAxis.normalize();
        
        // 计算当前轴到目标轴的差异
        const currentAxis = this.scrollAxis.normalize();
        const axisDifference = Vec3.subtract(new Vec3(), normalizedTarget, currentAxis);
        
        // 如果差异很小，直接设置
        if (axisDifference.length() < 0.01) {
            this.scrollAxis.set(normalizedTarget);
            this._rotationDirty = true;
            return;
        }
        
        // 计算过渡步长
        const transitionStep = axisDifference.clone().multiplyScalar(speed * 0.016); // 假设60fps
        
        // 应用过渡
        this.scrollAxis.add(transitionStep);
        this.scrollAxis.normalize();
        this._rotationDirty = true;
    }

    /**
     * 设置旋转预设效果
     * @param preset 预设类型
     * @param intensity 强度倍数
     */
    setRotationPreset(preset: 'normal' | 'topspin' | 'backspin' | 'sidespin-left' | 'sidespin-right', intensity: number = 1.0) {
        switch (preset) {
            case 'normal':
                // 正常滚动：根据移动方向自动计算
                this._rotationDirty = true;
                break;
                
            case 'topspin':
                // 上旋：增加Y轴旋转分量
                this.rotateSphere(0, intensity, 0, true);
                break;
                
            case 'backspin':
                // 下旋/回旋：减少Y轴旋转分量
                this.rotateSphere(0, -intensity, 0, true);
                break;
                
            case 'sidespin-left':
                // 左侧旋：增加Z轴旋转分量
                this.rotateSphere(0, 0, intensity, true);
                break;
                
            case 'sidespin-right':
                // 右侧旋：减少Z轴旋转分量
                this.rotateSphere(0, 0, -intensity, true);
                break;
        }
    }

    /**
     * 获取当前旋转状态信息
     */
    getRotationInfo(): { axis: Vec3, speed: number, isRotating: boolean } {
        return {
            axis: this.scrollAxis.clone(),
            speed: this.linearVelocityLen,
            isRotating: this.linearVelocityLen > 0.01
        };
    }

    /**
     * 根据击球方式设置旋转效果
     * @param cueAngle 球杆角度
     * @param hitPoint 击球点（相对于球心，范围-1到1）
     * @param force 击球力度
     */
    applyStrokeRotation(cueAngle: number, hitPoint: { x: number, y: number }, force: number) {
        // 根据击球点计算旋转效果
        const spinFactorX = hitPoint.x * 2.0; // 左右侧旋
        const spinFactorY = hitPoint.y * 1.5; // 上下旋
        
        // 结合球杆角度计算最终旋转轴
        const angleRad = cueAngle * Math.PI / 180;
        const rotationX = Math.cos(angleRad) * spinFactorX;
        const rotationY = spinFactorY;
        const rotationZ = Math.sin(angleRad) * spinFactorX;
        
        // 根据力度调整旋转强度
        const forceMultiplier = Math.min(force / 100, 2.0); // 力度范围0-100转换为0-2倍数
        
        this.rotateSphere(
            rotationX * forceMultiplier,
            rotationY * forceMultiplier,
            rotationZ * forceMultiplier,
            true, // 使用平滑过渡
            3.0   // 中等过渡速度
        );
    }

    // /**
    //  * 模拟台球杆法的旋转效果
    //  * @param technique 杆法类型
    //  * @param strength 杆法强度 (0-1)
    //  */
    // applyBilliardTechnique(technique: 'follow' | 'draw' | 'stop' | 'stun' | 'english-left' | 'english-right', strength: number = 0.5) {
    //     const clampedStrength = Math.max(0, Math.min(1, strength));
        
    //     switch (technique) {
    //         case 'follow':
    //             // 跟进杆：球会在击中目标球后继续向前滚动
    //             this.setRotationPreset('topspin', clampedStrength * 1.5);
    //             break;
                
    //         case 'draw':
    //             // 拉杆：球会在击中目标球后向后滚动
    //             this.setRotationPreset('backspin', clampedStrength * 1.5);
    //             break;
                
    //         case 'stop':
    //             // 停杆：球击中目标球后停止
    //             this.setRotationPreset('normal');
    //             break;
                
    //         case 'stun':
    //             // 定杆：球以滑动方式前进，减少旋转
    //             this.rotateSphere(1, 0, 0, true, 2.0);
    //             this.rotationSpeedMultiplier *= 0.3; // 减少旋转速度
    //             break;
                
    //         case 'english-left':
    //             // 左塞：左侧旋
    //             this.setRotationPreset('sidespin-left', clampedStrength);
    //             break;
                
    //         case 'english-right':
    //             // 右塞：右侧旋
    //             this.setRotationPreset('sidespin-right', clampedStrength);
    //             break;
    //     }
    // }

    /**
     * 根据碰撞信息调整旋转
     * @param collisionVelocity 碰撞后的速度
     * @param collisionNormal 碰撞法线（可选）
     */
    applyCollisionRotation(collisionVelocity: Vec2, collisionNormal?: Vec2) {
        // 立即计算并应用新的旋转
        const rotationInfo = this._calculateRotationFromVelocity(collisionVelocity);
        this.scrollAxis = rotationInfo.axis;
        this._lastVelocity.set(collisionVelocity);
        this._rotationDirty = false;
        
        // 如果提供了碰撞法线，可以计算更复杂的旋转效果
        if (collisionNormal) {
            // 计算碰撞角度调整旋转轴
            const impactAngle = Math.atan2(collisionVelocity.y, collisionVelocity.x);
            const normalAngle = Math.atan2(collisionNormal.y, collisionNormal.x);
            const relativeAngle = impactAngle - normalAngle;
            
            // 根据相对角度微调旋转轴（增强效果）
            const adjustmentFactor = Math.sin(relativeAngle) * 0.3; // 增加调整因子
            this.scrollAxis.z += adjustmentFactor;
            //TODO:: 必须归一化！否则会导致旋转矩阵异常，进而导致shader材质丢失
            this.scrollAxis.normalize();
            
            //TODO::碰撞后的速度变量会翻倍增长，导致角速度增加（需要在每一次球停后重置rotationSpeedMultiplier 为默认值8）
            // 根据碰撞强度调整旋转速度
            const collisionStrength = collisionVelocity.length();
            if (collisionStrength > 5) { // 降低阈值，让更多碰撞触发旋转增强
                // const speedMultiplier = 1 + (collisionStrength / 50); // 动态调整倍数
                const speedMultiplier = 1 + (collisionStrength / 100); // 动态调整倍数，转速倍数太大，修改公式
                this.rotationSpeedMultiplier *= speedMultiplier;
                // console.log(`[Ball ${this.ballId}] 碰撞强度:`+ collisionStrength+ '旋转倍数:'+ speedMultiplier);
            }
        }
        
        // 立即应用一次旋转效果，确保碰撞时有明显的旋转变化
        const immediateRotation = 0.1 * rotationInfo.angularSpeed;
        if (Math.abs(immediateRotation) > 0.001) {
            this.scroll(immediateRotation, this.scrollAxis.x, this.scrollAxis.y, this.scrollAxis.z);
            this._updateMaterialMatrix();
        }
    }

    /**球入袋，状态更新 */
    enterPacket(tag: number) {
        if (this.ballId != 0) {
            this.isEnter = true;
        }
        this.enterPacketTag = tag;
        this.tweenMove = true;
    }
    
    /**入袋动画结束 */
    finishMove() {
        this.tweenMove = false;
        this.setPhysicalState(false);
    }

    /**设置刚体状态；若不可用则休眠 */
    setPhysicalState(v: boolean = false) {
        if (this.rb2D && !v) {
            this.rb2D.sleep();
        }
    }
    
    setScale(v = 1) {
        this.node.setScale(v, v, v);
    }

    /**目标球可击打提示动效 */
    playHintAni(isShow: boolean) {
        if (!Global.animationState) return;
        if (!this.ballAni) return;
        this.ballAni.node.active = isShow;
        if (isShow) {
            this.ballAni.play();
        } else {
            this.ballAni.stop();
        }
    }

    /**更新位置&重置向量速度 */
    updatePos(x, y, z = 0) {
        this.resetVelocity();
        this.node.setPosition(x, y, z);
    }

    /**重置线性速度 & 角速度 */
    resetVelocity() {
        if (this.rb2D) {
            this.rb2D.linearVelocity = v2(0, 0);
            this.rb2D.angularVelocity = 0;
        }
        // 重置旋转状态
        this._lastVelocity.set(0, 0);
        this._rotationDirty = true;
        this.rotationSpeedMultiplier = 8;
        this.speed = this.linearVelocityLen;
    }

    setWakeUp() {
        if (this.isValid && this.rb2D) {
            if (!this.rb2D.enabled || !this.rb2D.isAwake()) {
                this.rb2D.enabled = true;
                this.rb2D.wakeUp();
            }
        }
    }

    get ballType(): number {
        let num = Number(this.node.name);
        if (num == 0) return BallType.White;
        if (num == 8) return BallType.Black;
        if (num > 0 && num < 8) return BallType.Solid;
        return BallType.Stripe;
    }

    get ballId(): number {
        return Number(this.node.name);
    }

    set isEnter(v: boolean) {
        this._isEnter = v;
    }

    get isEnter(): boolean {
        return this._isEnter;
    }

    get x(): number {
        return this.node.position.x;
    }

    get y(): number {
        return this.node.position.y;
    }

    get z(): number {
        return this.node.position.z;
    }

    get linearVelocityLen() {
        if (this.rb2D) return this.rb2D.linearVelocity.length();
        return 0;
    }

    get tweenMove() {
        return this._tweenMove;
    }
    set tweenMove(v) {
        this._tweenMove = v;
    }

    resetWhiteBall() {
        this.resetVelocity();
        this.setPhysicalState(true);
    }

    checkIsValidPos(isValidPos: boolean) {
        this.isValidPos = isValidPos;
        this.node.active = !this.isValidPos;
    }

    clear() {
        this.tweenMove = false; 
        this.rb2D?.sleep();
        this.ballAni?.stop();
        // this._resetRotationMatrix();// 清理旋转状态
        this._lastVelocity?.set(0, 0);
        this._rotationDirty = true;
    }
}