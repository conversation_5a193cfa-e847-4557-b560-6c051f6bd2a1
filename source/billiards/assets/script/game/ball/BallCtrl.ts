import { BoxCollider2D, Collider2D, Component, Contact2DType, IPhysics2DContact, PhysicsGroup, PhysicsSystem2D, PolygonCollider2D, Quat, Tween, tween, v2, v3, Vec2, Vec3 } from "cc";
import { BilliardsStatus, BallType, AllBallStopEvent } from '../data/Enum';
import { Ball } from "./Ball";
import GameService from "../data/GameService";
import { Cue } from "../cue/Cue";
import GameData, { TweenTags } from "../data/GameData";
import { Table } from "../Table";
import Global from "../../core/data/Global";
import { Sliderbar } from "../Sliderbar";
// import PhysicalManagerCustom from "../../core/tools/PhysicalManagerCustom";
import LocalStorage from "../../core/tools/LocalStorage";
import { FineTurning } from "../FineTurning";
import AudioMgr from "../../core/tools/AudioMgr";
import { PlayerItem } from "../player/PlayerItem";
import { EnumEvent, EventMgr } from "../../core/event/EventManager";
import { uiManager } from "../../core/ui/UIManager";
import { UIID } from "../../Main";
import { GameUtil } from "../../core/tools/GameUtil";
import HitBallRuleMgr from "./HitBallRuleMgr";

class BallCtrl extends Component {
    /**球Ball列表（除母球） */
    public ballDic: { [key: string]: Ball } = null;
    private _whiteBall: Ball = null;
    private _cue: Cue = null;
    private _table: Table = null;
    private _sliderbar: Sliderbar = null;
    private _fineTurning: FineTurning = null;
    private _winnerId: number = 0;
    /** 单回合内白球发生碰撞列表*/
    private _hitWhiteList: Array<any> = null;
    /** 单回合内球袋发生碰撞列表*/
    private _hitPacketList: Array<any> = null;
    /** 单回合内库边发生碰撞列表*/
    private _hitTableSideList: Array<any> = null;
    /** 每回合进球 */
    private _roundEnterBallList: Array<number> = null;
    /** 累计进球数 */
    private _enterBallNum = 0;
    private _endPos: Vec3 = v3(230, -560, 0);
    private _curTargetPos: Vec3 = null;
    /**是否初始化事件 */
    private isInitEvent = false;

    // ========== 性能优化相关属性 ==========
    /** 性能优化：球数组缓存，避免每次遍历字典 */
    private _ballArray: Ball[] = [];
    private _ballArrayDirty: boolean = true;

    /** 性能优化：碰撞检测Set缓存，提高查找效率 */
    private _hitPacketSet: Set<string> = new Set();
    private _hitTableSideSet: Set<string> = new Set();
    private _hitWhiteSet: Set<string> = new Set();

    /** 旋转效果控制 */
    public enableRotationEffects: boolean = true; // 全局旋转效果开关
    public rotationIntensity: number = 1.0; // 旋转强度倍数

    /** 碰撞事件防重复处理 */
    private _collisionPairs = new Set<string>(); // 存储已处理的碰撞对
    private _collisionClearTimer: any = null;

    get roundEnterBallList() {
        return this._roundEnterBallList || [];
    }
    get hitWhiteList() {
        return this._hitWhiteList || [];
    }
    get hitPacketList() {
        return this._hitPacketList || [];
    }
    get hitTableSideList() {
        return this._hitTableSideList || [];
    }

    init(whiteBall: Ball, cue: Cue, table: Table, sliderbar: Sliderbar, fineTurning: FineTurning) {
        this._whiteBall = whiteBall;
        this._cue = cue;
        this._table = table;
        this._sliderbar = sliderbar;
        this._fineTurning = fineTurning;

        this._winnerId = 0;
        this._enterBallNum = 0;
        this._curTargetPos = null;
        this._roundEnterBallList = [];
        this._roundBallList = [];
        sliderbar.setCue(this._cue);
        fineTurning.setCue(this._cue);

        this._initEvent();
        this.updateSlider_fineTurning();
        if (GameData.playBall) this.updateBall(GameData.playBall);
        GameData.enterBallList && (this._enterBallNum = GameData.enterBallList.length);
        
        // 暴露调试方法到全局作用域
        this.exposeDebugMethods();
    }

    /**
     * 性能优化：更新球数组缓存
     */
    private _updateBallArrayCache() {
        if (!this._ballArrayDirty || !this.ballDic) return;

        this._ballArray.length = 0;
        for (let key in this.ballDic) {
            this._ballArray.push(this.ballDic[key]);
        }
        this._ballArrayDirty = false;
    }

    /**
     * 性能优化：快速检查球是否运动
     */
    public fastCheckBallMovement(): boolean {
        // 先检查白球
        if (this._whiteBall.speed || this._whiteBall.tweenMove) {
            return true;
        }

        // 使用缓存的球数组检查
        this._updateBallArrayCache();
        for (let i = 0; i < this._ballArray.length; i++) {
            const ball = this._ballArray[i];
            if (ball.speed || ball.tweenMove) {
                return true;
            }
        }
        return false;
    }

    /**
     * 生成碰撞对的唯一标识
     */
    private _getCollisionPairKey(ballId1: number, ballId2: number): string {
        // 确保较小的ID在前，避免重复
        const minId = Math.min(ballId1, ballId2);
        const maxId = Math.max(ballId1, ballId2);
        return `${minId}-${maxId}`;
    }

    /**
     * 检查碰撞是否已经处理过
     */
    private _isCollisionAlreadyProcessed(ballId1: number, ballId2: number): boolean {
        const key = this._getCollisionPairKey(ballId1, ballId2);
        return this._collisionPairs.has(key);
    }

    /**
     * 标记碰撞已处理
     */
    private _markCollisionProcessed(ballId1: number, ballId2: number): void {
        const key = this._getCollisionPairKey(ballId1, ballId2);
        this._collisionPairs.add(key);

        // 设置定时器清理碰撞记录
        if (this._collisionClearTimer) {
            clearTimeout(this._collisionClearTimer);
        }

        this._collisionClearTimer = setTimeout(() => {
            this._collisionPairs.clear();
            this._collisionClearTimer = null;
        }, 500); // 500ms后清理碰撞记录
    }

    public onEndContactBalls(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact = null) {
        if (selfCollider.group == PhysicsGroup['balls'] && otherCollider.group == PhysicsGroup['balls']) {
            let selfBall = selfCollider.node.getComponent(Ball);
            let otherBall = otherCollider.node.getComponent(Ball);

            // 检查是否已经处理过这对球的碰撞
            if (this._isCollisionAlreadyProcessed(selfBall.ballId, otherBall.ballId)) {
                return;
            }

            // 检查是否有球在运动（降低速度阈值）
            const hasMovement = selfBall.linearVelocityLen > 0.1 || otherBall.linearVelocityLen > 0.1;

            if (hasMovement) {
                // 标记这对球的碰撞已处理
                this._markCollisionProcessed(selfBall.ballId, otherBall.ballId);

                // 播放碰撞音效
                let vol = GameData.getBallHitBallVol(selfBall.rb2D.linearVelocity);
                AudioMgr.playAudioEffect('ballHitBall', vol);

                // 设置碰撞标志
                // selfBall.isHit = otherBall.isHit = true;

                // console.log(`[BallCtrl] 应用碰撞旋转效果: ${selfBall.ballId} vs ${otherBall.ballId}`);
                // 计算碰撞旋转效果
                // this._applyCollisionRotationEffects(selfBall, otherBall, contact);

                // // 延迟重置碰撞标志
                // setTimeout(() => {
                //     if (selfBall && selfBall.isValid) selfBall.isHit = false;
                //     if (otherBall && otherBall.isValid) otherBall.isHit = false;
                // }, 100);
            }
        }
    }

    /**
     * 为碰撞的球应用旋转效果
     * @param ball1 第一个球
     * @param ball2 第二个球
     * @param contact 碰撞接触信息
     */
    private _applyCollisionRotationEffects(ball1: Ball, ball2: Ball, contact: IPhysics2DContact) {
        // 检查全局旋转开关
        if (!this.enableRotationEffects || !contact) return;
        
        // 获取碰撞点和法线
        const worldManifold = contact.getWorldManifold();
        if (!worldManifold || worldManifold.points.length === 0) return;

        const normal = worldManifold.normal;
        const normalVec2 = v2(normal.x, normal.y);

        // 为球1应用碰撞旋转（考虑强度倍数）
        if (ball1.rb2D && ball1.rb2D.linearVelocity.length() > 0) {
            ball1.applyCollisionRotation(ball1.rb2D.linearVelocity, normalVec2);
            
            // 根据强度倍数调整旋转
            if (this.rotationIntensity !== 1.0 && typeof ball1.rotateSphere === 'function') {
                const rotInfo = ball1.getRotationInfo();
                ball1.rotateSphere(
                    rotInfo.axis.x * this.rotationIntensity,
                    rotInfo.axis.y * this.rotationIntensity,
                    rotInfo.axis.z * this.rotationIntensity
                );
            }
        }

        // 为球2应用碰撞旋转（考虑强度倍数）
        if (ball2.rb2D && ball2.rb2D.linearVelocity.length() > 0) {
            // 对于球2，法线方向相反
            const reversedNormal = v2(-normal.x, -normal.y);
            ball2.applyCollisionRotation(ball2.rb2D.linearVelocity, reversedNormal);
            
            // 根据强度倍数调整旋转
            if (this.rotationIntensity !== 1.0 && typeof ball2.rotateSphere === 'function') {
                const rotInfo = ball2.getRotationInfo();
                ball2.rotateSphere(
                    rotInfo.axis.x * this.rotationIntensity,
                    rotInfo.axis.y * this.rotationIntensity,
                    rotInfo.axis.z * this.rotationIntensity
                );
            }
        }
    }

    private _initEvent() {
        if (this.isInitEvent) return;

        EventMgr.addEventListener(EnumEvent.CUSTOM_BALL_MOVE_STOP, this.checkBallMoveInPacket, this);
        PhysicsSystem2D.instance.on(Contact2DType.BEGIN_CONTACT, this.onEndContactBalls, this);
        //母球
        let whiteBallCollider = this._whiteBall?.node.getComponent(Collider2D);
        if (whiteBallCollider) {
            whiteBallCollider.on(Contact2DType.BEGIN_CONTACT, this.onEndContactWhite, this);
        }

        //球袋&库边
        //球袋
        let packetColliders = this._table.getComponents(PolygonCollider2D);
        if (packetColliders) {
            packetColliders.forEach(element => {
                if (element.group == PhysicsGroup['ballPacket']) {
                    element.on(Contact2DType.END_CONTACT, this.onEndContactPacket, this);
                }
            });
        }
        //库边
        let tableSideColliders = this._table.getComponents(BoxCollider2D);
        if (tableSideColliders) {
            tableSideColliders.forEach(element => {
                if (element.group == PhysicsGroup['tableSide']) {
                    element.on(Contact2DType.BEGIN_CONTACT, this.onEndContactTableSide, this);
                }
            });
        }

        EventMgr.addEventListener(EnumEvent.SHOW, this.onShow, this);
        this.isInitEvent = true;
    }
    onShow() {
        if (GameData.isGameOver) {
            return;
        }
        GameService.instance.CdTimeRequest();
    }

    private _ballStopTimeOut;
    update(dt: number = Global.dt): boolean {
        PhysicsSystem2D.instance.postUpdate(PhysicsSystem2D.instance.fixedTimeStep)

        // // 性能优化：减少update频率，每N帧检查一次球停状态
        // this._updateCounter++;
        // if (this._updateCounter < this._updateInterval) {
        //     return this._lastAllBallStopState;
        // }
        // this._updateCounter = 0;

        // if (!GameData.isMyOperate || GameData.noHit || !this.ballDic) return;
        if (GameData.noHit || !this.ballDic) return false;

        // 性能优化：使用快速检查方法
        const hasMovingBalls = this.fastCheckBallMovement();
        let allballStop = !hasMovingBalls;

        // this._testLabel.string = allballStop + '==' + (!GameData.isSendBallStop)+"==" + this._whiteBall.speed + '=' + !this._whiteBall.tweenMove;
        //todo::球停检测球是否有效位置
        // if (allballStop && !GameData.isSendBallStop) {
        //     // 性能优化：使用缓存的球数组进行检查
        //     this._updateBallArrayCache();
        //     let packetTag = 0;

        //     for (let i = 0; i < this._ballArray.length; i++) {
        //         const ball = this._ballArray[i];
        //         // console.log(ball.ballId, '=Ballctrl.update=' + ball.tweenMove);
        //         packetTag = GameData.checkInPacket(ball.node.position);
        //         if (packetTag) {
        //             allballStop = false;
        //             this.checkInPacket(ball.ballId, packetTag);
        //             break; // 性能优化：找到一个就退出
        //         }
        //         // GameUtil.log(ball.ballId + " " + allballStop + "==Ballctrl 普通球是否靠近球袋===" + packetTag);
        //     }

        //     if (allballStop && this._whiteBall?.isValid) {
        //         packetTag = GameData.checkInPacket(this._whiteBall.node.position);
        //         if (packetTag) {
        //             allballStop = false;
        //             this.checkInPacket(this._whiteBall.ballId, packetTag);
        //         }
        //         // GameUtil.log(this._whiteBall.ballId +" "+ allballStop + "==Ballctrl 母球是否靠近球袋===" + packetTag + "  " + this._whiteBall.x + ":" + this._whiteBall.y);
        //     }
        // }

        if (allballStop && !GameData.isSendBallStop && GameData.isMyOperate) {
            // this._whiteBall.updatePos(221.7, -467.3)
            if (this._ballStopTimeOut) clearTimeout(this._ballStopTimeOut);
            GameData.isSendBallStop = true;
            if (Global.isResume) {
                this._ballStopTimeOut = setTimeout(() => {
                    this._ballMoveStop();
                }, 1000);
            } else {
                this._ballMoveStop();
            }
        }

        // 性能优化：缓存球停状态
        return allballStop;
    }

    /**
     * 初始化球
     * @param ball
     */
    addBallToTable(ball: Ball) {
        if (!this.ballDic) this.ballDic = {};
        this.ballDic[ball.ballId] = ball;
        // 性能优化：标记球数组需要更新
        this._ballArrayDirty = true;
    }
    getBallById(ballId): Ball {
        if (this.ballDic) return this.ballDic[ballId];
    }

    /**
     * 击球
     * 本回合击球状态置为true
     * 回合cd倒计时停止
     */
    shootCue() {
        this.unschedule(this.invoke);
        this._table && this._table.stopCdTime();
        GameData.noHit = false;
    }

    private _count = 0;
    private _playerItem: PlayerItem = null;
    beginTimer(playerItem: PlayerItem) {
        GameData.noHit = true;
        this.unschedule(this.invoke);
        this._playerItem = playerItem;
        this._count = GameData.cdTime;
        if (this._count != undefined) {
            playerItem.beginTimer(this._count);
            this.schedule(this.invoke, 1, this._count);
        }
    }

    invoke() {
        this._count -= 1;
        this._playerItem?.invoke(this._count);
        if (this._count == 0) {//停止拖动操作
            this._cue && this._cue.stopDrag();
            this._sliderbar && this._sliderbar.stopDrag();
        }
        if (this._count >= 0 && this._count <= 9) {
            if (GameData.isMyOperate) this._table.playCdTime(this._count);
            if (this._count <= 0) this._table.stopCdTime();
        }
        if (this._count <= 0) {
            this.stopTimer();
        }
    }

    stopTimer() {
        this._playerItem?.stopTimer();
        this._table?.stopCdTime();
        this.unschedule(this.invoke);
    }

    onEndContactWhite(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact = null) {
        if (!this._hitWhiteList) this._hitWhiteList = [];
        if (otherCollider.node && otherCollider.node.name) {
            if (otherCollider.tag == 1) {
                let ballName = otherCollider.node.name;
                // 性能优化：使用Set进行快速查找，避免重复添加
                if (!this._hitWhiteSet.has(ballName)) {
                    this._hitWhiteList.push(ballName);
                    this._hitWhiteSet.add(ballName);
                }

                // // 为白球和被撞球应用碰撞旋转效果
                // if (this.enableRotationEffects && contact && this._whiteBall && otherCollider.node) {
                //     const hitBall = otherCollider.node.getComponent(Ball);
                //     if (hitBall) {
                //         // console.log(`[BallCtrl] 应用白球碰撞旋转效果`);
                //         this._applyWhiteBallCollisionRotation(this._whiteBall, hitBall, contact);
                //     }
                // }

                // 白球碰撞音效已在 onEndContactBalls 中处理，这里不再重复播放
                // let vol = GameData.getBallHitBallVol(this._whiteBall.rb2D.linearVelocity);
                // this._playAudioEffect('ballHitBall', vol);
            }
        }
    }

    /**
     * 为白球碰撞应用旋转效果
     * @param whiteBall 白球
     * @param hitBall 被撞球
     * @param contact 碰撞接触信息
     */
    private _applyWhiteBallCollisionRotation(whiteBall: Ball, hitBall: Ball, contact: IPhysics2DContact) {
        if (!contact) return;

        const worldManifold = contact.getWorldManifold();
        if (!worldManifold || worldManifold.points.length === 0) return;

        const normal = worldManifold.normal;
        const normalVec2 = v2(normal.x, normal.y);

        // console.log(`[BallCtrl] 白球碰撞旋转: 法线=${normalVec2.x}, ${normalVec2.y}`);

        // 为白球应用碰撞旋转（白球作为Ball类的实例，也有applyCollisionRotation方法）
        if (whiteBall.rb2D && whiteBall.rb2D.linearVelocity.length() > 0.1) {
            if (typeof whiteBall.applyCollisionRotation === 'function') {
                // console.log(`[BallCtrl] 白球应用碰撞旋转, 速度:`, whiteBall.rb2D.linearVelocity);
                whiteBall.applyCollisionRotation(whiteBall.rb2D.linearVelocity, normalVec2);
            }
            
            // 根据强度倍数调整白球旋转
            if (this.rotationIntensity !== 1.0 && typeof whiteBall.rotateSphere === 'function') {
                const rotInfo = whiteBall.getRotationInfo();
                if (rotInfo) {
                    whiteBall.rotateSphere(
                        rotInfo.axis.x * this.rotationIntensity,
                        rotInfo.axis.y * this.rotationIntensity,
                        rotInfo.axis.z * this.rotationIntensity
                    );
                }
            }
        }

        // 为被撞球应用旋转
        if (hitBall.rb2D && hitBall.rb2D.linearVelocity.length() > 0.1) {
            const reversedNormal = v2(-normal.x, -normal.y);
            // console.log(`[BallCtrl] 被撞球${hitBall.ballId}应用碰撞旋转, 速度:`, hitBall.rb2D.linearVelocity);
            hitBall.applyCollisionRotation(hitBall.rb2D.linearVelocity, reversedNormal);
            
            // 根据强度倍数调整被撞球旋转
            if (this.rotationIntensity !== 1.0 && typeof hitBall.rotateSphere === 'function') {
                const rotInfo = hitBall.getRotationInfo();
                hitBall.rotateSphere(
                    rotInfo.axis.x * this.rotationIntensity,
                    rotInfo.axis.y * this.rotationIntensity,
                    rotInfo.axis.z * this.rotationIntensity
                );
            }
        }
    }

    onEndContactPacket(selfCollider: PolygonCollider2D, otherCollider: Collider2D, contact: IPhysics2DContact = null) {
        if (!this._hitPacketList) this._hitPacketList = [];
        //TODO::这里tag可能是0
        if (otherCollider.node && otherCollider.node.name && otherCollider.tag != 0) {
           let ballName = otherCollider.node.name;
            // 性能优化：使用Set进行快速查找，避免indexOf的O(n)复杂度
            if (!this._hitPacketSet.has(ballName)) {
                this._hitPacketList.push(ballName);
                this._hitPacketSet.add(ballName);
                this._checkEnterPacket(selfCollider.tag, otherCollider.getComponent(Ball));
            }
         }
    }
    onEndContactTableSide(selfCollider: BoxCollider2D, otherCollider: Collider2D, contact: IPhysics2DContact = null) {
        if (!this._hitTableSideList) this._hitTableSideList = [];
        if (otherCollider.node && otherCollider.node.name && otherCollider.tag != 0) {
            let ballName = otherCollider.node.name;
            // 性能优化：使用Set进行快速查找，避免indexOf的O(n)复杂度
            if (!this._hitTableSideSet.has(ballName)) {
                this._hitTableSideList.push(ballName);
                this._hitTableSideSet.add(ballName);
                
                // 为撞击库边的球应用反弹效果，特别是倾斜击球时
                const ball = otherCollider.node.getComponent(Ball);
                if (ball && ball.rb2D) {
                    this._applyTableSideCollisionRotation(ball, contact);
                }
            }
        }
        AudioMgr.playAudioEffect('ballHitCushion');
    }

    /**
     * 为撞击库边的球应用旋转效果
     * @param ball 撞击库边的球
     * @param contact 碰撞接触信息
     */
    private _applyTableSideCollisionRotation(ball: Ball, contact: IPhysics2DContact) {
        if (!contact || !ball.rb2D) return;

        const worldManifold = contact.getWorldManifold();
        if (!worldManifold || worldManifold.points.length === 0) return;

        const normal = worldManifold.normal;
        const normalVec2 = v2(normal.x, normal.y);
        const velocity = ball.rb2D.linearVelocity;
        const velocityLen = velocity.length();

        // 处理所有速度的球，特别是倾斜击球的反弹
        if (velocityLen > 0.1) {
            const ballPos = ball.node.position;
            const tangent = v2(-normal.y, normal.x); // 库边切线方向
            const tangentVelocity = velocity.dot(tangent);
            const normalVelocity = velocity.dot(normalVec2);

            // 检测是否主要沿库边移动（这是问题所在）
            const isSliding = Math.abs(tangentVelocity) > Math.abs(normalVelocity) * 1.2;

            if (isSliding || velocityLen < 2.0) {
                // 增强反弹效果，减少沿库边滑动
                let restitution = 0.7; // 反弹系数
                let frictionReduction = 0.4; // 摩擦减少系数

                // 对于高速球，增强反弹
                if (velocityLen > 5.0) {
                    restitution = 0.8;
                    frictionReduction = 0.5;
                }

                // 计算反弹后的速度
                const newNormalVelocity = -normalVelocity * restitution;
                const newTangentVelocity = tangentVelocity * frictionReduction;

                const newVelocity = v2(
                    normal.x * newNormalVelocity + tangent.x * newTangentVelocity,
                    normal.y * newNormalVelocity + tangent.y * newTangentVelocity
                );

                ball.rb2D.linearVelocity = newVelocity;

                // 分离球和库边，避免持续碰撞
                const separationDistance = velocityLen > 5.0 ? 1.5 : 0.8;
                const separationVector = v2(normal.x * separationDistance, normal.y * separationDistance);
                ball.node.setPosition(ballPos.x + separationVector.x, ballPos.y + separationVector.y, ballPos.z);
            }
        }

        // // 库边碰撞通常会产生更复杂的旋转效果
        // if (velocityLen > 0) {
        //     ball.applyCollisionRotation(velocity, normalVec2);

        //     // 额外的库边反弹效果：可以增加一些侧旋
        //     const impactAngle = Math.atan2(velocity.y, velocity.x);
        //     const normalAngle = Math.atan2(normal.y, normal.x);
        //     const incidenceAngle = Math.abs(impactAngle - normalAngle);

        //     // 根据入射角度调整旋转，模拟真实的库边反弹效果
        //     if (incidenceAngle > Math.PI / 6) { // 30度以上的斜角撞击
        //         const spinAdjustment = Math.sin(incidenceAngle) * 0.15;
        //         ball.rotateSphere(
        //             ball.scrollAxis.x,
        //             ball.scrollAxis.y,
        //             ball.scrollAxis.z + spinAdjustment
        //         );
        //     }
        // }
    }

    checkBallMoveInPacket(eventName, ballId) {
        let ball = this.ballDic[ballId];
        if (ballId == 0) ball = this._whiteBall;

        let packetTag = GameData.checkInPacket(ball.node.position);
        console.log("[BallCtrl:收到球停事件 ballId=]" + ballId + " 球袋：" + packetTag);
        if (packetTag) {
            this.checkInPacket(ball, packetTag);
        }
    }

    /**
     * 检测是否靠近球袋
     */
    checkInPacket(ball: Ball, packetTag) {
        if (!this._hitPacketList) this._hitPacketList = [];
        let key = String(ball.ballId);
        if (this._hitPacketList.indexOf(key) < 0) {
            this._hitPacketList.push(key);
            this._checkEnterPacket(packetTag, ball);
        }
    }

    checkNoHit(): boolean {
        let isWhiteHit = this._hitWhiteList && this._hitWhiteList.length > 0;
        let isHitPacket = this._hitPacketList && this._hitPacketList.length > 0;
        let isHitTableSide = this._hitTableSideList && this._hitTableSideList.length > 0;
        if (isWhiteHit || isHitPacket || isHitTableSide) return false;
        return true;
    }

    /**
     * 球运动停止
     * 1.检测是否犯规
     * 2.更新球杆位置
     */
    private _isFoul = false;
    _ballMoveStop() {
        this.syncTableBall(GameData.currentOperate, GameData.noHit);
    }
    /**同步桌面球，异常结束，由正常方同步 */
    syncTableBall(idx = Global.userId, isHit = false) {
        if(GameData.isGameOver) return;
        this._isFoul = HitBallRuleMgr.checkFoul();
        let ballInfo = {}, playBall = {};
        for (let key in this.ballDic) {
            let ball = this.ballDic[key];
            let newPos = this.clampBallPos(ball.node.position);
            // ball.updatePos(newPos.x, newPos.y);
            playBall[ball.ballId] = { x: newPos.x, y: newPos.y };
        }

        let wBallPos = { x: this._whiteBall.x, y: this._whiteBall.y };
        if (GameData.isWhiteBallInPacket || this._whiteBall.x == this._endPos.x) {//TODO::现在等切换用户，才更改球位置，这里提交数据会导致母球位置错乱，，这里母球初始位置还需要重新计算
            let resetPos = GameData.whiteBallRestPos();
            wBallPos = { x: resetPos.x, y: resetPos.y };
        }
        // else {
            // wBallPos = this.clampBallPos(wBallPos);
            // this._whiteBall.updatePos(wBallPos.x, wBallPos.y);
        // }

        ballInfo['noHit'] = isHit;
        ballInfo['ballType'] = GameData.myBallType;
        ballInfo['currentOperate'] = idx;
        ballInfo['playBall'] = JSON.stringify(playBall);
        ballInfo['whiteBall'] = JSON.stringify(wBallPos);
        ballInfo['enterBallList'] = JSON.stringify(GameData.enterBallList);
        ballInfo['cue'] = JSON.stringify({ rad: this._cue.rad, angle: this._cue.angle });
        GameService.instance.AllBallStopRequest(ballInfo);
    }

    /**球停事件 */
    ballStopEventRequest() {
        let ballInfo = {};
        let combox = GameData.updateCombox(this._roundEnterBallList, this._isFoul);
        ballInfo['combox'] = combox;
        if (GameData.isRestart || GameData.isGameOver) {
            ballInfo = this.checkGameOver();
        } else {
            if (this._isFoul) {
                ballInfo['event'] = AllBallStopEvent.FOUL;
            }
            else if (combox >= 1) ballInfo['event'] = AllBallStopEvent.COMBOX;
            else ballInfo['event'] = AllBallStopEvent.DEFAULT;

            let isFirstOpen = LocalStorage.getFirstOpenStaopBall();
            GameUtil.log(combox + " " + ballInfo['event'] + " " + this._isFoul + ' BallCtrl.ballStopEventRequest 是否第一次球停' + isFirstOpen);
            if ((!isFirstOpen && GameData.billiardsStatus != BilliardsStatus.Object) || GameData.roundSetObjectBall) {
                let firstHitballId = HitBallRuleMgr.getHitBallIdByWhite(this.hitWhiteList);
                if (this._roundEnterBallList && this._roundEnterBallList.length > 0 && firstHitballId != 8) {
                    if (this._roundEnterBallList.indexOf(0) < 0) {
                        ballInfo['combox'] = 0;
                        ballInfo['event'] = AllBallStopEvent.COMBOX;
                    }
                }
            } else {
                LocalStorage.saveFirstOpenStopBall('1');
            }
        }
        GameData.combox = combox;

        if (ballInfo['event'] == AllBallStopEvent.RESTART) {
            //TODO::如果是重开，对手也要重置球桌信息
            this.restartGame();
        }
        GameUtil.log("===球停事件：" + !GameData.noHit);
        GameUtil.log(ballInfo);
        if(!GameData.noHit) GameService.instance.AllBallStopEventRequest(ballInfo);
        // console.log(GameData.billiardsStatus, ballInfo['event'], ':event==ballStopEvent==combox:', combox, this._roundEnterBallList);
        this.clearHitList();
    }
    public checkGameOver() {
        let ballInfo = {};
        if (GameData.isRestart) {
            ballInfo['event'] = AllBallStopEvent.RESTART;
            ballInfo['restartPlayerId'] = GameData.currentOperate;
        }
        if (GameData.isGameOver) {
            ballInfo['event'] = AllBallStopEvent.GAME_OVER;
            ballInfo['winner'] = this._winnerId;
        }
        return ballInfo;
    }

    /**
     * 检测是否有球入袋
     *  入袋的球设置刚体球面，碰撞体不可用
     * selfColliderTag 球袋tag
     */
    private _checkEnterPacket(selfColliderTag, otherColliderBall: Ball) {
        let ballId = otherColliderBall.ballId;
        if (GameData.enterBallList.indexOf(ballId) < 0) {
            if (!this._roundEnterBallList) this._roundEnterBallList = [];
            if (!this._roundBallList) this._roundBallList = [];
            GameData.addEnterBall(ballId);
            this._roundEnterBallList.push(ballId);
            // GameUtil.log("=====进袋碰撞检测==ballId==" + ballId);
            if (otherColliderBall) {
                otherColliderBall.enterPacket(selfColliderTag);
                this._roundBallList.push(otherColliderBall);
                this._playBallMoveTween();
            }
        }
    }

    private _settimeOutV;
    private _roundBallList: Array<Ball> = [];
    private _playBallMoveTween(): void {
        if (this._roundBallList && this._roundBallList.length <= 1) this._ballMoveTween(this._roundBallList[0]);
    }
    private _playBallMoveTweenNext(): void {
        if (this._roundBallList && this._roundBallList.length > 0) this._roundBallList.shift();
        if (this._roundBallList && this._roundBallList[0]) this._ballMoveTween(this._roundBallList[0]);
    }

    /**
     * 球落袋运动；播放入袋动画
     * @param ball
     */
    private _ballMoveTween(ball: Ball) {
        // ball.node.setScale(GameData.ballEnterScale, GameData.ballEnterScale, GameData.ballEnterScale);
        this._enterBallNum += 1;
        this._curTargetPos = v3(this._endPos.x - (this._enterBallNum - 1) * GameData.ballRadius * 2, this._endPos.y, 0);
        if (Global.isResume) {
            // let skPos = GameData.packetPosList[ball.enterPacketTag - 1];
            // if (!skPos) skPos = [ball.node.position.x, ball.node.position.y];
            // this._table.udpateEnterBallSk(true, skPos);
            ball.resetVelocity();
            ball.setPhysicalState(false);
            ball.node.rotation = Quat.IDENTITY;

            let self = this;
            let duration = 0.01 * 140 * (this._curTargetPos.x + 475) / 475;

            //TODO::在左下角洞口进球的才会显示在左下角位置。其他都是显示在进球轨道入口
            // ball.updatePos(skPos[0], skPos[1]);
            tween(ball.node).tag(TweenTags.game)
                // .to(0.1, { position: v3(skPos[0], skPos[1], 0) }, {})
                .to(0.1, { position: v3(-244, -543, 0) }, {
                    onStart(target?: any) {
                        let ball = target.getComponent(Ball);
                        ball.setScale(0);
                    },
                    onComplete(target?: any) {
                        self._table.udpateEnterBallSk(false);
                        let ball = target.getComponent(Ball);
                        ball.setScale(1);
                    }
                })
                .to(0.1, { position: v3(-228, -559, 0) }, {
                    onStart(target?: any) {
                        let ball = target.getComponent(Ball);
                        ball.setScale(1);
                    }
                })
                .to(duration, { position: self._curTargetPos }, {
                    onUpdate(target, ratio) {
                        let ball = target.getComponent(Ball);
                        self.updateRotation(ball, 1.5);
                    },
                    onComplete(target?: any) {
                        let ball = target.getComponent(Ball);
                        ball.finishMove();
                    }
                })
                .start();

            // clearTimeout(this._settimeOutV);
            if (this._roundBallList && this._roundBallList.length > 0) {
                this._settimeOutV = setTimeout(() => {
                    self._playBallMoveTweenNext();
                }, 300);
            }

            AudioMgr.playAudioEffect('pockedOne');

        } else {
            ball.finishMove();
            ball.node.position = this._curTargetPos;

            this._roundBallList && this._roundBallList.shift();
            this._playBallMoveTween();
        }
    }

    updateRotation(ball: Ball, vx = 0, vy = 0) {
        ball.applyCollisionRotation(v2(vx, vy), v2(2, 0));
        const rotInfo = ball.getRotationInfo();
        ball.rotateSphere(
            rotInfo.axis.x * this.rotationIntensity,
            rotInfo.axis.y * this.rotationIntensity,
            rotInfo.axis.z * this.rotationIntensity
        );
    }

    /**
     * 入袋动画结束，检测是否白球需要回到球桌
     * @param node
     * @returns
     */
    checkWhiteReset() {
        GameData.removeEnterBall(0);
        this._whiteBall.finishMove();
    }
    checkEnterBall() {
        /**如果母球是在中间进球位置，那还原母球位置后，需要更新后续进球的位置 */
        let enterList = GameData.enterBallList || [];
        for (let i = 0; i < enterList.length; i++) {
            let ball = this.ballDic[enterList[i]];
            if (ball) {
                let x = this._endPos.x - i * GameData.ballRadius * 2;
                //TODO::切后台，偶现有球异常进入袋中，但是未显示
                ball.node.active = true;
                ball.updatePos(x, this._endPos.y);
            }
        }
    }

    /**
     * 双方确定目标球
     */
    setObjectBall(ballName: string) {
        if (GameData.hasObjectBall(GameData.currentOperate)) return;
        let ballType = GameData.getBallType(Number(ballName));
        GameData.setObjectBall(GameData.currentOperate, ballType);
        GameData.billiardsStatus = BilliardsStatus.Object;
        GameData.roundSetObjectBall = true;
    }


    /**球停，设置球杆角度 */
    ballStop_updateCue() {
        let ballDic = {};
        for (let key in this.ballDic) {
            if (Object.prototype.hasOwnProperty.call(this.ballDic, key)) {
                let element: Ball = this.ballDic[key];
                if (!element.isEnter && !GameData.isInPacket(element.ballId)) {
                    ballDic[element.ballId] = element;
                }
            }
        }
        let cueBallType;
        if (GameData.billiardsStatus == BilliardsStatus.Object) {
            let objectBallNum = GameData.objectBallList(GameData.currentOperate).length;
            if (objectBallNum == 1) {//黑球
                cueBallType = BallType.Black;
            } else {
                cueBallType = GameData.getPlayerData(GameData.currentOperate).ballType;
            }
        }
        GameUtil.log(this._cue?.node?.active + "====ballStop_updateCue=====" + GameData.billiardsStatus + ' ' + cueBallType);
        if (this._cue && this._cue.isValid && this._cue.node?.active) {
            this._cue.ballStop_updateCue(GameData.billiardsStatus, ballDic, cueBallType);
        }
    }

    restartGame(isResetAll = true) {
        GameUtil.log("=重新开局 BallCtrl.restartGame=" + isResetAll);
        GameData.isRestart = true;
        if (isResetAll) {
            this.clearPhysicalStepCount();
            this.reset();
            GameService.instance.restartGame();
        }
    }

    gameOver(winnerId: number = 0) {
        this._winnerId = winnerId;
        GameData.isGameOver = true;

        this.clearHitList();
        Tween.stopAllByTag(TweenTags.game);
        this.stopTimer();
        for (let key in this.ballDic) {
            let ball = this.ballDic[key];
            ball.clear();
        }
        this._whiteBall?.clear();
        PhysicsSystem2D.instance.enable = false;
    }
    /** 结算界面是否存在 */
    isShowGameOver(): boolean{
        let bView = uiManager.getUI(UIID.Result);
        return !!bView;
    }

    /** 结算界面是否存在 */
    isOtherView(): boolean {
        let cView = uiManager.getUI(UIID.Confirm);
        let sView = uiManager.getUI(UIID.Settings);
        let isOther = (!!cView || !!sView);
        GameUtil.log(isOther+"===是否其他面板==");
        return isOther;
    }

    stopDragMove() {
        this._cue && this._cue.stopDrag();
        this._table && this._table.stopDrag();
    }
    /**
     * 开始计时器
     * 检测是否犯规
     * 避免切后台回来，倒计时cd还在继续
     * 母球如果进袋，需要重置为开球区； 再次检测母球是否非法位置
     * @param head
     */
    playerChangeResponse(head: PlayerItem) {
        if (!this._whiteBall?.isValid) return;
        
        let isMyOperate = GameData.isMyOperate;
        let whiteBallPos = this._whiteBall.node.position;
        this._table.stopCdTime();
        this.clearHitList();
        if (GameData.isWhiteBallInPacket || this._whiteBall.x == this._endPos.x) {
            this.checkWhiteReset();
            whiteBallPos = GameData.whiteBallRestPos();
            //开球局母球直接进球袋，犯规后，拖动母球到球袋，非法位置； 断线重连，等切换玩家母球不显示
            // this._table.forbidset(false);
        }
        this.checkEnterBall();

        let result = GameData.checkValidPos(whiteBallPos, 29);
        let isValidPos = result[0];
        GameUtil.log('=BallCtrl.playerChangeResponse=' + GameData.isWhiteBallInPacket + ":" + isValidPos);
        if (isValidPos) {
            if (result[1] == 'packet') {
                whiteBallPos = GameData.whiteBallRestPos();
                isValidPos = GameData.checkValidPos(whiteBallPos)[0];
                if (!isValidPos) {
                    this._whiteBall.updatePos(whiteBallPos.x, whiteBallPos.y);
                    this._table.forbidset(false);
                }
            }
        } else {
            this._whiteBall.updatePos(whiteBallPos.x, whiteBallPos.y);
            this._table.forbidset(false);
        }

        let savePosIndex = 0;
        let len = GameData.whiteSavePosList.length;
        while (isValidPos && savePosIndex < len) {
            let nextPos = { x: GameData.whiteSavePosList[savePosIndex][0], y: GameData.whiteSavePosList[savePosIndex][1] };
            isValidPos = GameData.checkValidPos(nextPos)[0];
            // console.log(nextPos, '===BallCtrl.playerChangeResponse=1===', isValidPos,savePosIndex);
            if (!isValidPos) {
                this._whiteBall.updatePos(nextPos.x, nextPos.y);
                this._table.forbidset(false);
            }
            savePosIndex += 1;
        }

        //需要再检测母球回到初始位置后，再次计算进球数 checkWhiteReset之后再计算
        this.beginTimer(head);
        this._enterBallNum = GameData.enterBallList ? GameData.enterBallList.length : 0;
        this._roundEnterBallList = [];
        this._roundBallList = []
        this._whiteBall.resetWhiteBall();
        this.updateSlider_fineTurning();

        if (!GameData.isWatch && isMyOperate) this.playHintAni(GameData.getPlayerData(GameData.currentOperate));

        /**获得自由球，或者开球阶段 */
        let isMyFreeBall = (GameData.freeBallIdx == Global.userId) || (GameData.billiardsStatus == BilliardsStatus.FirstOpen && !GameData.isMyFreeBall);
        this._table.playerChange(isMyFreeBall, isMyOperate);

        // //切换用户，更新当前球杆角度,需要延迟一帧
        // if (Global.isResume) {
        //     this.scheduleOnce(() => {
        //         this._cue.playerChange(isMyOperate);//切换用户，球杆都显示  用户是否可以操杆
        //         this.ballStop_updateCue();
        //     }, 0);
        // } else {
            this._cue.playerChange(isMyOperate);//切换用户，球杆都显示  用户是否可以操杆
            this.ballStop_updateCue();
        // }
    }

    /**击球方切后台或者断线重连，未发送球停消息；切换玩家后需要重新更新桌台信息 */
    playerChange_goBack(msg: any) {
        if (msg.whiteBall) {
            this._whiteBall.updatePos(GameData.whiteBall.x, GameData.whiteBall.y);
        }
        // if (msg.playBall) {
            // this.updateBall(GameData.playBall);
        // }
        if (msg.cue) {
            this._cue.handleCue(GameData.cue.rad, GameData.cue.angle);
        }
    }

    cdTimeResponse() {
        this._count = GameData.cdTime;
    }

    playHintAni(playerData) {
        if (!GameData.hasObjectBall(playerData.playerId)) return;
        if (playerData) {
            for (let key in this.ballDic) {
                if (Object.prototype.hasOwnProperty.call(this.ballDic, key)) {
                    let ball: Ball = this.ballDic[key];
                    if (ball.ballType == playerData.ballType && !ball.isEnter) {
                        ball.playHintAni(true);
                    }
                }
            }
        }
    }

    updateBall(ballDic: any) {
        Tween.stopAllByTag(TweenTags.game);
        for (let key in ballDic) {
            let ball: Ball = this.ballDic[key];
            if (ball) {
                let ballId = Number(key);
                let ballData = ballDic[key];
                let isInPacket = GameData.isInPacket(ballId);
                ball.updateBall(ballData.x, ballData.y);
                if (isInPacket) {
                    ball.setPhysicalState();
                    if (ballId != 0) {
                        ball.isEnter = true;
                    }
                } else {
                    ball.setWakeUp();
                }
                this.ballDic[key] = ball;
            }
        }
        //切换玩家，如果母球犯规，会导致母球位置先从上一局的位置更新到1/4或者中心点
        //TODO::这里一定要在球停消息时，处理母球停止，避免不同手机，运动效率不一致，球位置不同，导致下一轮偏差
        if (GameData.whiteBall) {
            this._whiteBall.node.setScale(1, 1, 1);
            GameUtil.log("==ballCtrl.updateBall=white=");
            GameUtil.log(GameData.whiteBall);
            this._whiteBall.updatePos(GameData.whiteBall.x, GameData.whiteBall.y);
        }
    }

    /**观战者 | 游戏中关于 力度杆 方向杆 更新 */
    updateSlider_fineTurning() {
        if (GameData.isWatch) {
            this._sliderbar.updateActive(false);
            this._fineTurning.updateActive(false);
            return;
        }
        let isGray = !GameData.isMyOperate;
        this._sliderbar.updateGray(isGray);
        this._fineTurning.updateGray(isGray);
    }
    /**击球后同步所有球信息 */
    syncBalls(impulse, cueAngle, cueRad, cueDir, eventState) {
        let beHitBallsPos = {};
        // this._whiteBall.resetVelocity();
        let newWhitePos = this.clampBallPos(this._whiteBall.node.position);
        this._whiteBall.updatePos(newWhitePos.x, newWhitePos.y);

        for (let key in this.ballDic) {
            if (Object.prototype.hasOwnProperty.call(this.ballDic, key)) {
                let ball: Ball = this.ballDic[key];
                ball.playHintAni(false);
                if (!ball.isEnter) {
                    // ball.resetVelocity();
                    let newPos = this.clampBallPos(ball.node.position);
                    ball.updatePos(newPos.x, newPos.y);
                    beHitBallsPos[ball.ballId] = { x: newPos.x, y: newPos.y };
                }
            }
        }

        // // 为白球设置击球旋转效果
        // if (this.enableRotationEffects && this._whiteBall && typeof this._whiteBall.applyStrokeRotation === 'function') {
        //     // 根据击球力度和角度设置旋转
        //     const force = impulse.percent || 50; // 默认力度50%
        //     const hitPoint = { x: 0, y: 0 }; // 默认击球点为球心，可以根据实际需求调整
            
        //     this._whiteBall.applyStrokeRotation(cueAngle, hitPoint, force * this.rotationIntensity);
        //     // console.log(`白球击球旋转设置: 角度=${cueAngle}, 力度=${force}, 强度=${this.rotationIntensity}`);
        // }

        // 旋转效果已在球的update方法中自动处理
        PhysicsSystem2D.instance.physicsWorld.syncSceneToPhysics();
        let syncBallData = {
            "hitImpulse": { x: impulse.x, y: impulse.y, percent: impulse.percent },
            "cue": { angle: cueAngle, rad: cueRad, dir: {x: cueDir.x, y: cueDir.y} },
            "event": eventState,
            "currentOperate": GameData.currentOperate,
            "ballPos": { x: newWhitePos.x, y: newWhitePos.y },
            "beHitBallsPos": beHitBallsPos,
        }
        return syncBallData;
    }

    //#region 共享方法，方便其它脚本执行球的同步
    doBallsSync(data) {
        if (!this) return;
        this._cue?.updateActive(false);
        this._whiteBall?.updatePos(data.ballPos.x, data.ballPos.y);
        for (const key in data.beHitBallsPos) {
            const ball: Ball = this.ballDic[key];
            const pos = data.beHitBallsPos[key];
            ball?.updatePos(pos.x, pos.y);
        }
        PhysicsSystem2D.instance.physicsWorld.syncSceneToPhysics();
    }

    // 碰撞后修正位置精度（保留3位小数）
    clampBallPos(pos) {
        const precision = 1000000;
        return {
            x: Math.round(pos.x * precision) / precision,
            y: Math.round(pos.y * precision) / precision
        };
    }

    onBallsSyncFinish() {
        // this.clearPhysicalStepCount();
    }
    // /**异常球停，切换到其他玩家，强制停球 */
    // stopAllBall() {
    //     // Tween.stopAllByTag(TweenTags.game);
    //     GameUtil.log("==BallCtrl.stopAllBll 强制停球===");
    //     for (let key in this.ballDic) {
    //         let ball = this.ballDic[key];
    //         ball.resetBall();
    //         this.ballDic[key] = ball;
    //     }
    // }

    clearHitList() {
        this._hitWhiteList = [];
        this._hitPacketList = [];
        this._hitTableSideList = [];
        // 性能优化：清理Set缓存
        this._hitWhiteSet.clear();
        this._hitPacketSet.clear();
        this._hitTableSideSet.clear();
    }

    reset() {
        for (let key in this.ballDic) {
            let ball = this.ballDic[key];
            ball.clear();
        }
        if (GameData.whiteBall) this._whiteBall.clear();

        clearTimeout(this._settimeOutV);
        this._settimeOutV = null;
        this._isFoul = false;
        this._winnerId = 0;
        this._enterBallNum = 0;
        this._curTargetPos = null;
        this._hitWhiteList = null;
        this._hitPacketList = null;
        this._hitTableSideList = null;

        this._roundEnterBallList = null;
        this._roundBallList = null;

        // 性能优化：清理所有缓存
        this._ballArrayDirty = true;
        this._ballArray.length = 0;
        this._hitWhiteSet.clear();
        this._hitPacketSet.clear();
        this._hitTableSideSet.clear();
    }

    clearPhysicalStepCount() {
        // PhysicalManagerCustom._postUpdateStepCount = 0;
    }

    removeEvent() {
        EventMgr.removeEventListener(EnumEvent.SHOW, this.onShow, this);
        EventMgr.removeEventListener(EnumEvent.CUSTOM_BALL_MOVE_STOP, this.checkBallMoveInPacket, this);

        PhysicsSystem2D.instance.off(Contact2DType.BEGIN_CONTACT, this.onEndContactBalls, this);
        //母球
        if (this._whiteBall && this._whiteBall.isValid) {
            let whiteBallCollider = this._whiteBall.node.getComponent(Collider2D);
            if (whiteBallCollider) {
                whiteBallCollider.off(Contact2DType.BEGIN_CONTACT, this.onEndContactWhite, this);
            }
        }
        if (this._table && this._table.isValid) {
            //球袋
            let packetColliders = this._table.getComponents(PolygonCollider2D);
            if (packetColliders) {
                packetColliders.forEach(element => {
                    if (element.group == PhysicsGroup['ballPacket']) {
                        element.off(Contact2DType.END_CONTACT, this.onEndContactPacket, this);
                    }
                });
            }
            //库边
            let tableSideColliders = this._table.getComponents(BoxCollider2D);
            if (tableSideColliders) {
                tableSideColliders.forEach(element => {
                    if (element.group == PhysicsGroup['tableSide']) {
                        element.off(Contact2DType.BEGIN_CONTACT, this.onEndContactTableSide, this);
                    }
                });
            }
        }

        this.clearPhysicalStepCount();
    }
    clear() {
        this.reset();
        this.removeEvent();
        this.unscheduleAllCallbacks();
        this.ballDic = null;
        this.isInitEvent = false;
        PhysicsSystem2D.instance.enable = false;
    }

    /**
     * 为特定球手动应用旋转效果（用于特殊情况）
     * @param ballId 球的ID
     * @param rotationAxis 旋转轴
     * @param rotationSpeed 旋转速度倍数
     */
    public applyCustomRotation(ballId: number, rotationAxis: Vec3, rotationSpeed: number = 1.0) {
        let ball: Ball = null;
        
        if (ballId === 0) {
            ball = this._whiteBall;
        } else {
            ball = this.ballDic[ballId];
        }
        
        if (ball && typeof ball.rotateSphere === 'function') {
            ball.rotateSphere(
                rotationAxis.x * rotationSpeed,
                rotationAxis.y * rotationSpeed,
                rotationAxis.z * rotationSpeed
            );
        }
    }

    /**
     * 根据击球参数为白球设置旋转
     * @param cueAngle 球杆角度
     * @param hitPoint 击球点坐标
     * @param force 击球力度
     */
    public setWhiteBallStrokeRotation(cueAngle: number, hitPoint: { x: number, y: number }, force: number) {
        if (this._whiteBall && typeof this._whiteBall.applyStrokeRotation === 'function') {
            this._whiteBall.applyStrokeRotation(cueAngle, hitPoint, force);
        }
    }

    /**
     * 高级碰撞旋转调整 - 考虑能量传递和角动量守恒
     * @param ball1 第一个球
     * @param ball2 第二个球  
     * @param contact 碰撞信息
     */
    private _applyAdvancedCollisionRotation(ball1: Ball, ball2: Ball, contact: IPhysics2DContact) {
        if (!contact) return;

        const worldManifold = contact.getWorldManifold();
        if (!worldManifold || worldManifold.points.length === 0) return;

        // 获取碰撞前的速度和旋转信息
        const ball1RotInfo = ball1.getRotationInfo();
        const ball2RotInfo = ball2.getRotationInfo();
        
        const v1 = ball1.rb2D.linearVelocity;
        const v2 = ball2.rb2D.linearVelocity;
        
        // 计算质量（假设所有球质量相等）
        const m1 = 1.0, m2 = 1.0;
        
        // 计算碰撞法线
        const normal = new Vec2(worldManifold.normal.x, worldManifold.normal.y);
        const tangent = new Vec2(-normal.y, normal.x);
        
        // 计算法线方向的速度分量
        const v1n = v1.x * normal.x + v1.y * normal.y;
        const v2n = v2.x * normal.x + v2.y * normal.y;
        
        // 计算切线方向的速度分量
        const v1t = v1.x * tangent.x + v1.y * tangent.y;
        const v2t = v2.x * tangent.x + v2.y * tangent.y;
        
        // 应用弹性碰撞公式计算碰撞后的法线速度
        const v1n_new = ((m1 - m2) * v1n + 2 * m2 * v2n) / (m1 + m2);
        const v2n_new = ((m2 - m1) * v2n + 2 * m1 * v1n) / (m1 + m2);
        
        // 计算碰撞后的速度向量
        const v1_new = new Vec2(
            normal.x * v1n_new + tangent.x * v1t,
            normal.y * v1n_new + tangent.y * v1t
        );
        const v2_new = new Vec2(
            normal.x * v2n_new + tangent.x * v2t,
            normal.y * v2n_new + tangent.y * v2t
        );
        
        // 根据能量传递调整旋转
        const energyTransfer1 = v1_new.length() / (v1.length() || 1);
        const energyTransfer2 = v2_new.length() / (v2.length() || 1);
        
        // 为球1应用旋转调整
        if (energyTransfer1 > 0.1) {
            const rotationAdjustment1 = this._calculateRotationFromEnergyTransfer(
                ball1RotInfo.axis, energyTransfer1, normal
            );
            ball1.rotateSphere(
                rotationAdjustment1.x,
                rotationAdjustment1.y, 
                rotationAdjustment1.z,
                true, // 平滑过渡
                2.0   // 较快的过渡速度
            );
        }
        
        // 为球2应用旋转调整
        if (energyTransfer2 > 0.1) {
            const rotationAdjustment2 = this._calculateRotationFromEnergyTransfer(
                ball2RotInfo.axis, energyTransfer2, new Vec2(-normal.x, -normal.y)
            );
            ball2.rotateSphere(
                rotationAdjustment2.x,
                rotationAdjustment2.y,
                rotationAdjustment2.z,
                true, // 平滑过渡
                2.0   // 较快的过渡速度
            );
        }
    }

    /**
     * 根据能量传递计算旋转调整
     * @param originalAxis 原始旋转轴
     * @param energyRatio 能量传递比例
     * @param collisionNormal 碰撞法线
     */
    private _calculateRotationFromEnergyTransfer(originalAxis: Vec3, energyRatio: number, collisionNormal: Vec2): Vec3 {
        // 将2D法线转换为3D
        const normal3D = v3(collisionNormal.x, collisionNormal.y, 0);
        
        // 计算旋转轴与碰撞法线的夹角
        const dotProduct = Vec3.dot(originalAxis.normalize(), normal3D.normalize());
        const angle = Math.acos(Math.abs(dotProduct));
        
        // 根据夹角和能量传递调整旋转
        const adjustmentFactor = Math.sin(angle) * energyRatio * 0.5;
        
        // 计算调整后的旋转轴
        const adjustedAxis = Vec3.lerp(
            new Vec3(),
            originalAxis,
            v3(-collisionNormal.y, collisionNormal.x, originalAxis.z),
            adjustmentFactor
        );
        
        return adjustedAxis.normalize().multiplyScalar(energyRatio);
    }

    // /**
    //  * 模拟台球中的"传塞"效果
    //  * @param sourceBall 传递旋转的球
    //  * @param targetBall 接受旋转的球
    //  * @param transferRatio 传递比例 (0-1)
    //  */
    // public applySpinTransfer(sourceBall: Ball, targetBall: Ball, transferRatio: number = 0.3) {
    //     if (!sourceBall || !targetBall) return;
        
    //     const sourceRotInfo = sourceBall.getRotationInfo();
    //     if (!sourceRotInfo.isRotating) return;
        
    //     // 计算传递的旋转量
    //     const transferredRotation = sourceRotInfo.axis.clone().multiplyScalar(transferRatio);
        
    //     // 应用到目标球
    //     targetBall.rotateSphere(
    //         transferredRotation.x,
    //         transferredRotation.y,
    //         transferredRotation.z,
    //         true, // 平滑过渡
    //         1.5   // 较慢的过渡，模拟旋转传递过程
    //     );
        
    //     // 减少源球的旋转
    //     const remainingRotation = sourceRotInfo.axis.clone().multiplyScalar(1 - transferRatio * 0.5);
    //     sourceBall.rotateSphere(
    //         remainingRotation.x,
    //         remainingRotation.y,
    //         remainingRotation.z,
    //         true,
    //         2.0
    //     );
    // }

    // /**
    //  * 检测并应用旋转衰减效果
    //  * @param dt 时间增量
    //  */
    // private _applyRotationDecay(dt: number) {
    //     const decayRate = 0.98; // 每秒的旋转衰减率
    //     const frameDecay = Math.pow(decayRate, dt);
        
    //     // 对所有球应用旋转衰减
    //     this._updateBallArrayCache();
    //     for (let i = 0; i < this._ballArray.length; i++) {
    //         const ball = this._ballArray[i];
    //         if (ball.speed > 0.1 && typeof ball.getRotationInfo === 'function') {
    //             const rotInfo = ball.getRotationInfo();
    //             if (rotInfo.isRotating) {
    //                 const decayedAxis = rotInfo.axis.clone().multiplyScalar(frameDecay);
    //                 ball.rotateSphere(decayedAxis.x, decayedAxis.y, decayedAxis.z);
    //             }
    //         }
    //     }
        
    //     // 对白球应用旋转衰减
    //     if (this._whiteBall && this._whiteBall.speed > 0.1 && typeof this._whiteBall.getRotationInfo === 'function') {
    //         const rotInfo = this._whiteBall.getRotationInfo();
    //         if (rotInfo && rotInfo.isRotating) {
    //             const decayedAxis = rotInfo.axis.clone().multiplyScalar(frameDecay);
    //             this._whiteBall.rotateSphere(decayedAxis.x, decayedAxis.y, decayedAxis.z);
    //         }
    //     }
    // }

    /**
     * 重置所有球的旋转状态
     */
    public resetAllBallRotations() {
        // 重置白球
        if (this._whiteBall && typeof this._whiteBall._resetRotationMatrix === 'function') {
            this._whiteBall._resetRotationMatrix();
        }
        // 重置其他球
        for (let key in this.ballDic) {
            const ball = this.ballDic[key];
            if (ball && typeof ball._resetRotationMatrix === 'function') {
                ball._resetRotationMatrix();
            }
        }
        // console.log('所有球的旋转矩阵已重置');
    }

    /**
     * 暴露速度调整方法到全局作用域
     */
    exposeDebugMethods() {
        // 暴露到window对象（浏览器环境）
        if (typeof window !== 'undefined') {
            // 旋转速度调节方法（保留）
            (window as any).adjustRotationSpeed = (multiplier: number = 1.5) => {
                // 调整所有球的旋转速度倍数
                if (this._whiteBall && this._whiteBall.rotationSpeedMultiplier !== undefined) {
                    this._whiteBall.rotationSpeedMultiplier = 8.0 * multiplier;
                }
                
                for (let key in this.ballDic) {
                    const ball = this.ballDic[key];
                    if (ball && ball.rotationSpeedMultiplier !== undefined) {
                        ball.rotationSpeedMultiplier = 8.0 * multiplier;
                    }
                }
                // console.log(`所有球的旋转速度已调整为: ${8.0 * multiplier}`);
                // console.log('建议值: 0.5(慢) 1.0(正常) 1.5(快) 2.0(很快)');
            };
            
            // 旋转效果开关（保留）
            (window as any).enableRotation = (enable: boolean = true) => {
                this.enableRotationEffects = enable;
                // console.log(`球旋转效果已${enable ? '启用' : '禁用'}`);
            };
            
            // 旋转强度调节（保留）
            (window as any).setRotationIntensity = (intensity: number = 1.0) => {
                this.rotationIntensity = Math.max(0, Math.min(5, intensity)); // 限制在0-5之间
                // console.log(`旋转强度设置为: ${this.rotationIntensity}`);
            };
        }
    }
}

export default new BallCtrl;