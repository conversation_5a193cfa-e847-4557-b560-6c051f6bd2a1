import { instantiate } from "cc";
import Global from "../../core/data/Global";
import i18n from "../../core/tools/i18n";
import Utils from "../../core/tools/Utils";
import { AllBallStopEvent, BallType, BilliardsStatus } from "../data/Enum";
import GameData from "../data/GameData";
import { TipItem } from "../subItem/TipItem";
import { TipNoHitItem } from "../subItem/TipNoHitItem";
import { ToastItem } from "../subItem/ToastItem";
import { TipObjectBallItem } from "../subItem/TipObjectBallItem";
import { GameNodePool } from "./GameNodePool";
import { LinkItem } from "../subItem/LinkItem";
import { TipFoulItem } from "../subItem/TipFoulItem";
import { GameUtil } from "../../core/tools/GameUtil";

class TipManager {
    private _nodeParent = null;

    private _tipObjectBallItem: TipObjectBallItem = null;
    private _tipFoulItem: TipFoulItem = null;
    private _tipItem: TipItem = null;
    private _tipNoHitItem: TipNoHitItem = null;
    private _linkItem: LinkItem = null;
    private _toastItem: ToastItem = null;

    initTip(nodeParent): void {//prefb,noHitPrefb, comboxPrefb, toastPrefb
        this._nodeParent = nodeParent;
    }

    async showTestTip(msg: string) {
        if (!this._tipItem) {
            let prefab = await GameNodePool.instance.tipItemPrefab();
            let node = instantiate(prefab);
            this._nodeParent && this._nodeParent.insertChild(node, this._nodeParent.children.length);
            this._tipItem = node.getComponent(TipItem);
        }
        this._tipItem.showTip(msg);
    }

    /**开局提示 */
    async showBreakingTip(idx: number) {
        if (!Global.animationState) return;
        if (!Global.isResume) return;
        if (!this._tipItem) {
            let prefab = await GameNodePool.instance.tipItemPrefab();
            let node = instantiate(prefab);
            this._nodeParent && this._nodeParent.insertChild(node, this._nodeParent.children.length);
            this._tipItem = node.getComponent(TipItem);
        }

        let msg = '';
        if (!GameData.isMyOperate) msg = i18n.t('view_tip_opponentturn');
        else msg = i18n.t('view_tip_yourturn');
        // console.log('===showBreakingTip==msg:', msg);
        if (msg.length > 0) this._tipItem.showTip(msg);
    }

    /**切换用户提示 */
    async showChangePlayerTip(idx: number) {
        if (!Global.animationState) return;
        if (!Global.isResume) return;
        if (!this._tipItem) {
            let prefab = await GameNodePool.instance.tipItemPrefab();
            let node = instantiate(prefab);
            this._nodeParent && this._nodeParent.insertChild(node, this._nodeParent.children.length);
            this._tipItem = node.getComponent(TipItem);
        }

        let msg = '';
        if (!GameData.isMyOperate) msg = i18n.t('view_tip_opponentturn');
        else msg = i18n.t('view_tip_yourturn');
        if (msg.length > 0) this._tipItem.showTip(msg);
    }

    /**犯规提示 */
    async showFoulTip(idx: number, event: AllBallStopEvent) {
        if (!Global.animationState) return;
        if (!Global.isResume) return;
        if (event != AllBallStopEvent.FOUL) return;
        if (!this._tipFoulItem) {
            let prefab = await GameNodePool.instance.foulItemPrefab();
            let node = instantiate(prefab);
            this._nodeParent && this._nodeParent.insertChild(node, this._nodeParent.children.length);
            this._tipFoulItem = node.getComponent(TipFoulItem);
        }

        let msg = '';
        let billiardsStatus = GameData.whiteBall ? GameData.whiteBall.billiardsStatus : 1;
        if (idx == Global.userId) {
            if (billiardsStatus = BilliardsStatus.FirstOpen) msg = i18n.t('view_tip_youfoul');
            else msg = i18n.t('view_tip_youfoul');
        } else {
            if (billiardsStatus = BilliardsStatus.FirstOpen) msg = i18n.t('view_tip_opponentfoul');
            else msg = i18n.t('view_tip_opponentfoul');
        }
        if (msg.length > 0) this._tipFoulItem?.showTip(msg);
    }

    /**连杆提示 */
    async showBallLink(num: number) {
        if (!Global.animationState) return;
        if (!Global.isResume) return;
        if (num == 1) {
            if (!this._tipItem) {
                let prefab = await GameNodePool.instance.tipItemPrefab();
                let node = instantiate(prefab);
                this._nodeParent && this._nodeParent.insertChild(node, this._nodeParent.children.length);
                this._tipItem = node.getComponent(TipItem);
            }
            this._tipItem?.showTip(i18n.t('view_tip_keepHit'));
        }
        else {
            if (!this._linkItem) {
                let prefab = await GameNodePool.instance.linkItemPrefab();
                let node = instantiate(prefab);
                this._nodeParent && this._nodeParent.insertChild(node, this._nodeParent.children.length);
                this._linkItem = node.getComponent(LinkItem);
            }
            this._linkItem?.showTip(num);
        }
    }

    /**未击杆提示 */
    async showNoHitTip(msgResult = 0) {
        if (!Global.animationState) return;
        if (!Global.isResume) return;
        if (!this._tipNoHitItem) {
            let prefab = await GameNodePool.instance.tipNoHitItemPrefab();
            let node = instantiate(prefab);
            this._nodeParent && this._nodeParent.insertChild(node, this._nodeParent.children.length);
            this._tipNoHitItem = node.getComponent(TipNoHitItem);
        }
        let msg = Utils.paramFormat(i18n.t('view_tip_nohit_fail1'), msgResult, GameData.maxRound);
        if (msg.length > 0 && msgResult > 0) {
            let countStr = Utils.paramFormat(i18n.t('view_tip_nohit'), msgResult, GameData.maxRound);
            this._tipNoHitItem.showTip(msg, countStr);
        }
        // console.log('===showNoHitTip===msg:', msg)
    }

    /**确定目标球提示 */
    async showBallTypeTip() {
        if (!Global.animationState) return;
        if (!Global.isResume) return;
        if (!this._tipObjectBallItem) {
            let prefab = await GameNodePool.instance.tipObjectBallPrefab();
            let node = instantiate(prefab);
            this._nodeParent && this._nodeParent.insertChild(node, this._nodeParent.children.length);
            this._tipObjectBallItem = node.getComponent(TipObjectBallItem);
        }

        let msg = '';
        let playerData = GameData.getPlayerData(Global.userId);
        if (playerData.ballType == BallType.Solid) {
            msg = i18n.t('view_tip_hitSolid');
        } else {
            msg = i18n.t('view_tip_hitStrip');
        }
        GameUtil.log('===TipManager.showBallTypeTip==msg:'+ msg);
        if (msg.length > 0) this._tipObjectBallItem.showTip(msg);
    }

    async showRestartTip() {
        if (!Global.animationState) return;
        if (!Global.isResume) return;
        if (!this._tipItem) {
            let prefab = await GameNodePool.instance.tipItemPrefab();
            let node = instantiate(prefab);
            this._nodeParent && this._nodeParent.insertChild(node, this._nodeParent.children.length);
            this._tipItem = node.getComponent(TipItem);
        }
        this._tipItem.showTip(i18n.t('view_tip_8ball_restart'));
    }

    /**显示toast */
    async showToast(msgkey) {
        if (!Global.animationState) return;
        if (!Global.isResume) return;
        if (!this._toastItem) {
            let prefab = await GameNodePool.instance.toastItemPrefab();
            let node = instantiate(prefab);
            this._nodeParent && this._nodeParent.insertChild(node, this._nodeParent.children.length);
            this._toastItem = node.getComponent(ToastItem);
        }
        this._toastItem.showTip(msgkey);
    }

    clear() {
        this._tipItem = null;
        this._tipNoHitItem = null;
        this._toastItem = null;
        this._linkItem = null;
        this._tipObjectBallItem = null;
        this._nodeParent = null;
    }
}

export default new TipManager();