import { _decorator, Component, UITransform, EventTouch, Vec2, PhysicsSystem2D, ERaycast2DType, v2, view, Node, RaycastResult2D, macro, Sprite, color, Input } from 'cc';
import BallCtrl from '../ball/BallCtrl';
import GameData from '../data/GameData';
import { CueBallTip } from './CueBallTip';
import GameService from '../data/GameService';
import { BilliardsStatus } from '../data/Enum';
import Global from '../../core/data/Global';
import AudioMgr from '../../core/tools/AudioMgr';
import Utils from '../../core/tools/Utils';
import { EnumEvent, EventMgr } from '../../core/event/EventManager';
import { Ball } from '../ball/Ball';
const { ccclass } = _decorator;

@ccclass('Cue')
export class Cue extends Component {

    private _cueSp: Sprite = null;
    private _cueBall: Ball = null;
    private _transform: UITransform = null;

    private _rayLine: Node = null;
    private _rayLineTransform: UITransform = null;
    private _cueBallTip: CueBallTip = null;
    private _hitBall: Node = null;
    private _hitTimeOut;

    private _dir: Vec2 = null;
    private _rawCueAnchorY = 1.05;
    public _rad: number = 0;
    private _isDrag: boolean = false;
    private _color255 = color(255, 255, 255, 255);
    private _color125 = color(255, 255, 255, 125);
    private _color0 = color(255, 255, 255, 0);

    private touchNode: Node = null;//触摸节点

    // {{ YUM: [新增] - 球杆跟踪相关变量 }}
    private currentAngle: number = 0;
    private isRotating: boolean = false;
    private lastTouchPos: Vec2 = new Vec2();

    // {{ YUM: [新增] - 调试参数 }}
    private touchDistance: number = 0;
    private touchSpeed: number = 0;
    private smoothFactor: number = 0.2;//0.8;
    private angleDiff: number = 0;
    private globalSensitivity: number = 0.3; // 全局灵敏度系数，可以动态调整

    onLoad() {
        this._rad = 0;
        this._cueSp = this.getComponent(Sprite);

        this._transform = this.getComponent(UITransform);
        this._cueBallTip = this.node.getComponent(CueBallTip);
        this._rayLine = this.node.getChildByName('ray');
        this._rayLineTransform = this._rayLine.getComponent(UITransform);
        this._hitBall = this.node.getChildByName('hitball');


        this.touchNode = this.node.parent.getChildByPath('table/touchNode');
        this.touchNode.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        this.touchNode.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.touchNode.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        this.touchNode.on(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        EventMgr.addEventListener(EnumEvent.CUSTOM_CUE_POSITION_UPDATE, this.updateCueInfo, this);
    }

    init(cueBall: Ball) {
        this._cueBall = cueBall;
        this.resetCue();
        this.updateCuePos();
        this._transform.anchorY = this._rawCueAnchorY;
        this._rayLine.setPosition(0, GameData.rayOffsetY);
        this.isDrag = GameData.isMyOperate;
        if (!GameData.isMyOperate) {
            this._cueSp.color = this._color125;
        }
    }

    /**滑动 方向杆 */
    fineTurning(deltaAngle) {
        this.node.angle += deltaAngle;
        let rad = (this.angle - 90) * Math.PI / 180;
        this.rayCast(rad, this.angle, true);
    }

    /**
     * TODO::还要结合是否自己操作状态，这里暂时不考虑
     */
    get canOperate(): boolean {
        if (!this._cueBall) return false;
        if (GameData.isGameOver) return false;
        if (this._cueBall.isValidPos) return false;
        return GameData.isMyOperate;
    }

    /**更改球杆锚点 */
    changeCueAnchorY(anchorY: number) {
        this._transform.anchorY = anchorY + this._rawCueAnchorY;
    }

    /**
     * 球杆射击白球
     * @param power 射击力度
     */
    shootCueBall(power: number, percent: number) {
        this.isDrag = false;
        this.ballHit(power, percent);
    }

    /**开球、移动球杆执行此方法 
     * 拖动母球，球位置更新后，需要延迟一帧更新辅助线。不延迟的话，辅助线计算还未结束，导致异常
    */
    updateCueInfo(eventName: string, isShow: any, isMyOperate = true) {
        this.clearHitTimeOut();
        this.updateCuePos();
        this._cueSp.color = isShow ? (isMyOperate ? this._color255 : this._color125) : this._color0;
        if (!isShow) return;
        this._transform.anchorY = this._rawCueAnchorY;

        this.scheduleOnce(() => {
            this.setRad();
            this.rayCast(this._rad, this.angle);
        }, 0);

    }

    /**
        * {{ YUM: [新增] - 触摸开始事件处理 }}
        */
    private onTouchStart(event: EventTouch) {
        if (!this.canOperate || !this._isDrag) return;
        this._cueSp.color = this._color255;

        this.isRotating = true;

        const mousePos = Utils.getUILocation(event);
        this.lastTouchPos.set(mousePos.x, mousePos.y);
        
        // 立即更新球杆指向触摸位置
        const ballPos = this._cueBall.node.getWorldPosition();
        const currentAngleRad = Math.atan2(mousePos.y - ballPos.y, mousePos.x - ballPos.x);
        const targetAngle = (currentAngleRad * 180 / Math.PI) + 90;
        this.currentAngle = this.normalizeAngle(targetAngle);
        this.updateCueRotation();

        // console.log(`[CueDebugger] Touch start - Initial angle: ${this.currentAngle.toFixed(2)}°`);
    }

    /**
     * {{ YUM: [新增] - 触摸移动事件处理（核心跟踪逻辑） }}
     */
    private onTouchMove(event: EventTouch) {
        if (!this.canOperate || !this.isRotating) return;

        const mousePos = Utils.getUILocation(event);
        const ballPos = this._cueBall.node.getWorldPosition();

        // {{ YUM: [计算] - 触摸距离和速度 }}
        this.touchDistance = Math.sqrt(
            Math.pow(mousePos.x - ballPos.x, 2) +
            Math.pow(mousePos.y - ballPos.y, 2)
        );

        const deltaX = mousePos.x - this.lastTouchPos.x;
        const deltaY = mousePos.y - this.lastTouchPos.y;
        const moveDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        this.touchSpeed = moveDistance;

        // {{ YUM: [计算] - 基于触摸移动的相对角度变化 }}
        // 计算触摸移动的向量
        const touchDeltaX = mousePos.x - this.lastTouchPos.x;
        const touchDeltaY = mousePos.y - this.lastTouchPos.y;

        // 计算触摸移动相对于球心的切线方向
        const ballTouchX = mousePos.x - ballPos.x;
        const ballTouchY = mousePos.y - ballPos.y;

        // {{ YUM: [修复] - 使用叉积计算旋转方向和强度，修正方向和敏感度 }}
        const crossProduct = touchDeltaY * ballTouchX - touchDeltaX * ballTouchY; // 修正方向

        // {{ YUM: [调整] - 根据触摸距离调整角度变化的敏感度，提高到1:1比例 }}
        const distanceNormalization = Math.max(30, this.touchDistance);
        this.angleDiff = (crossProduct / distanceNormalization); // 大幅提高敏感度系数实现1:1比例 *2.0太快

        // {{ YUM: [计算] - 自适应阈值（基于相对角度变化） }}
        const baseThreshold = 0.05; // 提高阈值，匹配新的敏感度
        const distanceFactor = Math.max(0.1, Math.min(1.0, this.touchDistance / 200));
        const speedFactor = Math.max(0.1, Math.min(1.0, 10 / (this.touchSpeed + 1)));
        const adaptiveThreshold = baseThreshold * distanceFactor * speedFactor;

        if (Math.abs(this.angleDiff) < adaptiveThreshold) {
            return;
        }

        // {{ YUM: [计算] - 自适应平滑系数（基于相对角度变化） }}
        const isRapidCircling = this.touchSpeed > 25 && this.touchDistance < 150;

        // 基础平滑系数，根据距离调整（进一步降低灵敏度）

        // if (this.touchDistance < 80) {
        //     this.smoothFactor = isRapidCircling ? 0.3 : 0.25; // 大幅降低近距离灵敏度
        // } else if (this.touchDistance < 150) {
        //     if (isRapidCircling) {
        //         this.smoothFactor = 0.25;
        //     } else {
        //         this.smoothFactor = Math.max(0.15, 0.4 - this.touchSpeed * 0.01); // 降低中距离灵敏度
        //     }
        // } else if (this.touchSpeed > 30) {
        //     this.smoothFactor = Math.min(0.3, 0.15 + this.touchSpeed * 0.005); // 降低远距离快速移动灵敏度
        // } else {
        //     this.smoothFactor = 0.2; // 大幅降低默认灵敏度
        // }

        // {{ YUM: [调整] - 根据角度变化强度调整（匹配新的敏感度范围） }}
        //         1. 重新校准平滑系数判断条件 ：

        //    - 将强角度变化阈值从 0.5 提高到 2.0
        //    - 将中等角度变化阈值从 0.2 提高到 1.0
        //    - 将快速环绕判断阈值从 0.1 提高到 0.5
        const absAngleDiff = Math.abs(this.angleDiff);
        console.log(isRapidCircling+`[CueDebugger] 角度变化: ${absAngleDiff.toFixed(2)}°`);
        if (absAngleDiff > 2.0) {
            this.smoothFactor = Math.max(this.smoothFactor, 0.5); // 降低大角度变化的响应
        }else if (absAngleDiff > 1.0) {
            this.smoothFactor = 0.1;//Math.max(this.smoothFactor, 0.1); // 降低中等角度变化的响应
        }else{
            this.smoothFactor = 0.08;//Math.max(this.smoothFactor, 0.05); // 降低小角度变化的响应
        }

        if (isRapidCircling && absAngleDiff > 0.5) {
            this.smoothFactor = Math.max(this.smoothFactor, 1); // 降低快速环绕的响应
        }

        // {{ YUM: [更新] - 显示和位置 }}
        this.lastTouchPos.set(mousePos.x, mousePos.y);

        this.updateCueRotation();
    }

    /**
     * {{ YUM: [新增] - 触摸结束事件处理 }}
     */
    private onTouchEnd(event: EventTouch) {
        this.isRotating = false;
        if (!this._cueSp.color.a || !this.canOperate || !this._isDrag) return;
        this.updateCueRotation();
    }

    /**
     * {{ YUM: [新增] - 角度归一化 }}
     */
    private normalizeAngle(angle: number): number {
        if (isNaN(angle) || !isFinite(angle)) {
            return 0;
        }

        angle = angle % 360;
        if (angle < 0) {
            angle += 360;
        }
        return angle;
    }

    /**
     * {{ YUM: [新增] - 更新球杆旋转 }}
     */
    private updateCueRotation() {
        if (!this._cueBall) return;

        // {{ YUM: [获取] - 球杆当前的实际角度 }}
        const currentCueRotation = this.node.eulerAngles.z;

        // {{ YUM: [计算] - 基于角度差值进行增量旋转 }}
        const rotationDelta = this.angleDiff * this.smoothFactor * this.globalSensitivity;
        const newCueAngle = this.normalizeAngle(currentCueRotation + rotationDelta);

        // {{ YUM: [应用] - 设置球杆新角度 }}
        this.node.setRotationFromEuler(0, 0, newCueAngle);

        // {{ YUM: [更新] - 同步currentAngle为实际球杆角度 }}
        this.currentAngle = newCueAngle;
        // this.node.angle = this.currentAngle;
        this._rad = (this.currentAngle - 90) * Math.PI / 180;
        this.rayCast(this._rad, this.currentAngle, true);
    }

    // 计算两个向量之间的距离
    distance(a, b): number {
        const dx = a.x - b.x;
        const dy = a.y - b.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    rayCast(rad: number, angle: number, isSendMsg = false) {
        this.handleCue(rad, angle);
        if (isSendMsg) {
            GameService.instance.DragMoveRequest({
                cue: JSON.stringify({ rad: rad, angle: angle }),
                whiteBall: JSON.stringify({ x: this.ballPos.x, y: this.ballPos.y })
            })
        }
    }
    drawRay(startPos: Vec2, result: RaycastResult2D) {
        let distance = this.distance(startPos, result.point);
        this._cueBallTip.updateUI(startPos, result, distance);
    }

    setRad(e = null): number {
        const location = e ? Utils.getUILocation(e) : v2(view.getDesignResolutionSize().width * 0.5, 0);
        const cueBallPos = this._cueBall.node.worldPosition;
        const rad = Math.atan2(location.y - cueBallPos.y, location.x - cueBallPos.x);
        const angle = rad * macro.DEG + 90;
        this.angle = angle;
        this._rad = rad;
        return rad;
    }

    stopDrag() {
        this.isRotating = false;
        this._isDrag = false;
    }

    handleCue(rad: number, angle: number) {
        if (!PhysicsSystem2D.instance.enable || GameData.isGameOver || !this._cueBall) return;
        this._rad = rad;
        this.angle = angle;
        this._dir = v2(Math.cos(rad), Math.sin(rad)).negative();
        let cueBallPos = this._cueBall.node.getWorldPosition();
        let p1 = v2(cueBallPos.x, cueBallPos.y);
        let p2 = this._dir.clone().multiplyScalar(1000).add(p1);
        let results = PhysicsSystem2D.instance.raycast(p1, p2, ERaycast2DType.Closest);
        if (!results || results.length < 1) return
        this.drawRay(p1, results[0]);
    }

    handleBallHit(hitball) {
        this.isDrag = false;
        let power = hitball.hitImpulse.x;
        let angle = hitball.cue.angle;
        let rad = hitball.cue.rad;
        let percent = hitball.hitImpulse.percent;
        let dir = hitball.cue.dir;
        this._rad = rad;
        if (this.angle != angle) this.angle = angle;
        this._dir = v2(dir.x, dir.y);
        this.ballHit(power, percent);
    }

    ballHit(power: number, percent: number = 1) {
        this.clearHitTimeOut();
        this._transform.anchorY = 1;
        let impulse = this._dir.multiplyScalar(power);
        this._cueBall.rb2D.applyLinearImpulseToCenter(impulse, true);
        BallCtrl.shootCue();

        let self = this;
        if (Global.isResume) {
            this._hitBall.active = true;
            this._hitTimeOut = setTimeout(() => {
                self._hitBall.active = false;
                self.updateActive(false);
            }, 100);
        } else {
            this.updateActive(false);
        }

        AudioMgr.playEffect(AudioMgr.audios.cueHitBall, percent);
    }

    /**球停，需要更新球杆角度 切换到下一个用户 */
    ballStop_updateCue(billiardsStatus, ballDic, cueBallType = '') {
        // if (GameData.isGameOver || BallCtrl.isOtherView()) return;
        if (GameData.isGameOver) return;
        let result;
        const cueBallPos = this._cueBall?.node.position;
        if (billiardsStatus == BilliardsStatus.Object) {
            result = this._cueBallTip.cueAngleHitStatus(this._cueBall, ballDic, cueBallType);
        } else {
            result = this._cueBallTip.cueAngleOpenStatus(cueBallPos, ballDic);
        }
        if (result) {
            //TODO::再开一局，当前自己操作，可是球杆的色值是0，不可见
            this._cueSp.color = GameData.isMyOperate ? this._color255 : this._color125;
            this.handleCue(result[0], result[1]);
        }
    }

    updateActive(isShow: boolean) {
        this._cueSp.color = isShow ? this._color255 : this._color0;
        if (!isShow) this._rayLineTransform.height = 0;
    }

    playerChange(isMyOperate: boolean) {
        this.clearHitTimeOut(); //TODO::手机卡顿情况下，会出现击球后的settimeout执行晚于playerchange，导致球杆不显示
        this._cueSp.color = isMyOperate ? this._color255 : this._color125;
        this.isDrag = isMyOperate;
        this.updateCuePos();
        this._transform.anchorY = this._rawCueAnchorY;
    }

    updateCuePos() {
        this._cueBall && this.node.setPosition(this._cueBall.x, this._cueBall.y, 0);
    }

    set isDrag(v) {
        this._isDrag = v;
    }

    get rad() {
        return this._rad;
    }

    get angle() {
        return this.node.angle;
    }
    set angle(v) {
        this.node.angle = v;
    }

    get ballPos() {
        return this._cueBall.node.position;
    }

    get isVisible() {
        return this.node.active && (this._cueSp.color == this._color255);
    }

    get dir() {
        return this._dir;
    }

    clearHitTimeOut() {
        if (this._hitTimeOut) {
            this.updateActive(false);
            this._hitBall.active = false;
            clearTimeout(this._hitTimeOut);
            //TODO::弱网情况下，发送击球情况下，消息顺序可能不对，先收到切换用户，击球响应可能收不到
            this._hitTimeOut = 0;
        }
    }

    resetCue() {
        this.currentAngle = 0;
    }

    clear() {
        clearTimeout(this._hitTimeOut);
        this._hitTimeOut = 0;
        if (this.touchNode?.isValid) {
            this.touchNode.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
            this.touchNode.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
            this.touchNode.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);
            this.touchNode.off(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        }
        EventMgr.removeEventListener(EnumEvent.CUSTOM_CUE_POSITION_UPDATE, this.updateCueInfo, this);
    }

    onDestroy() {
        this.clear();
    }
}

