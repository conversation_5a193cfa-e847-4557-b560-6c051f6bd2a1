import { _decorator, Component, UITransform, EventTouch, Vec2, PhysicsSystem2D, ERaycast2DType, v2, view, Node, RaycastResult2D, macro, Sprite, color, Input, math, Vec3 } from 'cc';
import BallCtrl from '../ball/BallCtrl';
import GameData from '../data/GameData';
import { CueBallTip } from './CueBallTip';
import GameService from '../data/GameService';
import { BilliardsStatus } from '../data/Enum';
import Global from '../../core/data/Global';
import AudioMgr from '../../core/tools/AudioMgr';
import Utils from '../../core/tools/Utils';
import { EnumEvent, EventMgr } from '../../core/event/EventManager';
import { Ball } from '../ball/Ball';
const { ccclass } = _decorator;

@ccclass('Cue')
export class Cue extends Component {
    private _cueSp: Sprite = null;
    private whiteBall: Ball = null;
    private _transform: UITransform = null;

    private _rayLine: Node = null;
    private _rayLineTransform: UITransform = null;
    private _cueBallTip: CueBallTip = null;
    private _hitBall: Node = null;
    private _hitTimeOut;

    private _dir: Vec2 = null;
    private _rawCueAnchorY = 1.05;
    public _rad: number = 0;
    private _isDrag: boolean = false;
    private _color255 = color(255, 255, 255, 255);
    private _color125 = color(255, 255, 255, 125);
    private _color0 = color(255, 255, 255, 0);

    private touchNode: Node = null;//触摸节点

    private isRotating: boolean = false; // 是否正在旋转
    private currentAngle: number = 0; // 当前旋转角度（可以超过360度）
    private lastTouchPos: { x: number, y: number } = { x: 0, y: 0 }; // 上次触摸位置
    private totalRotation: number = 0; // 总旋转角度（支持多圈）


    private _isAiming: boolean = false;
    private _currentLength: number = 0;
    private _targetAngle: number = 0; // 目标角度（用于平滑旋转）
    private _currentAngle: number = 0; // 当前角度
    private _cueTransform: UITransform = null;
    private _whiteBallPos: Vec3 = new Vec3(); // 白球固定位置
    private rotationSmoothing: number = 0.1;

    onLoad() {
        this._rad = 0;
        this._cueSp = this.getComponent(Sprite);

        this._transform = this.getComponent(UITransform);
        this._cueBallTip = this.node.getComponent(CueBallTip);
        this._rayLine = this.node.getChildByName('ray');
        this._rayLineTransform = this._rayLine.getComponent(UITransform);
        this._hitBall = this.node.getChildByName('hitball');

        
        this.touchNode = this.node.parent.getChildByPath('table/touchNode');
        this.touchNode.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        this.touchNode.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.touchNode.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        this.touchNode.on(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        EventMgr.addEventListener(EnumEvent.CUSTOM_CUE_POSITION_UPDATE, this.updateCueInfo, this);
    }
    start() {
    }

    init(whiteBall: Ball) {
        // 记录白球初始位置（固定不变）
        this._whiteBallPos = whiteBall.node.position.clone();

        this.whiteBall = whiteBall;
        this.resetCue();
        this.updateCuePos();
        this._transform.anchorY = this._rawCueAnchorY;
        this._rayLine.setPosition(0, GameData.rayOffsetY);
        this.isDrag = GameData.isMyOperate;
        if (!GameData.isMyOperate) {
            this._cueSp.color = this._color125;
        }
    }

    /**滑动 方向杆 */
    fineTurning(deltaAngle) {
        this.node.angle += deltaAngle;
        let rad = (this.angle - 90) * Math.PI / 180;
        this.rayCast(rad, this.angle, true);
    }

    /**
     * TODO::还要结合是否自己操作状态，这里暂时不考虑
     */
    get canOperate(): boolean {
        if (!this.whiteBall) return false;
        if (GameData.isGameOver) return false;
        if (this.whiteBall.isValidPos) return false;
        return GameData.isMyOperate;
    }

    /**更改球杆锚点 */
    changeCueAnchorY(anchorY: number) {
        this._transform.anchorY = anchorY + this._rawCueAnchorY;
    }

    /**
     * 球杆射击白球
     * @param power 射击力度
     */
    shootCueBall(power: number, percent: number) {
        this.isDrag = false;
        this.ballHit(power, percent);
    }

    /**开球、移动球杆执行此方法 
     * 拖动母球，球位置更新后，需要延迟一帧更新辅助线。不延迟的话，辅助线计算还未结束，导致异常
    */
    updateCueInfo(eventName: string, isShow: any, isMyOperate = true) {
        this.clearHitTimeOut();
        this.updateCuePos();
        this._cueSp.color = isShow ? (isMyOperate ? this._color255 : this._color125) : this._color0;
        if (!isShow) return;
        this._transform.anchorY = this._rawCueAnchorY;
        
        this.scheduleOnce(() => {
            this.setRad();
            this.rayCast(this._rad, this.angle);
        }, 0);
        
    }


    update(deltaTime: number) {
        // 平滑旋转效果
        if (Math.abs(this._currentAngle - this._targetAngle) > 0.1) {
            this._currentAngle = math.lerp(this._currentAngle, this._targetAngle, 1 - Math.pow(this.rotationSmoothing, deltaTime * 60));
            this.node.angle = this._currentAngle;
        }
    }

    /**
     * 计算从触摸点到白球的角度（球杆指向与触摸方向相反）
     */
    private calculateAngle(touchPos: Vec2): number {
        if (!this.whiteBall) return 0;

        // 将白球世界坐标转换为屏幕坐标
        const whiteBallScreenPos = new Vec2(this._whiteBallPos.x, this._whiteBallPos.y);

        // 计算方向向量
        const dirX = touchPos.x - whiteBallScreenPos.x;
        const dirY = touchPos.y - whiteBallScreenPos.y;

        // 计算角度（弧度转度）
        let angle = Math.atan2(dirY, dirX) * 180 / Math.PI;

        // 调整角度，使球杆指向与触摸点相反的方向（球杆在白球后方）
        angle += 180;

        return angle;
    }

    private onTouchStart(event: EventTouch) {
        if (!this.canOperate || !this._isDrag) return;
            this._cueSp.color = this._color255;
        // 击球后重新显示球杆
        this._isAiming = true;
        const touchPos = event.getLocation();

        // 计算初始角度和长度
        this._targetAngle = this.calculateAngle(touchPos);
        this._currentAngle = this._targetAngle; // 初始时直接设置角度

        // 更新球杆
        this.node.angle = this._currentAngle;
    }

    private onTouchMove(event: EventTouch) {
        if (!this._isAiming) return;

        const touchPos = event.getLocation();

        // 更新目标角度和长度
        this._targetAngle = this.calculateAngle(touchPos);
    }

    private onTouchEnd() {
        if (!this._isAiming) return;
        if (!this._cueSp.color.a || !this.canOperate || !this._isDrag) return;
            this.updateCueRotation();

        this._isAiming = false;
    }

    private onTouchCancel() {
        this._isAiming = false;
    }

    // onTouchStart(event: EventTouch) {
    //     if (!this.canOperate || !this._isDrag) return;
    //     this._cueSp.color = this._color255;

    //     // 总是启用旋转模式，让球杆跟随手指方向
    //     this.isRotating = true;

    //     // 保持当前球杆角度，从上次位置继续旋转
    //     this.currentAngle = this.node.angle;
    //     this.totalRotation = this.currentAngle; // 初始化总旋转角度

    //     // 保存初始触摸位置
    //     const mousePos = event.getLocation();
    //     this.lastTouchPos = { x: mousePos.x, y: mousePos.y };
    // }

    // onTouchMove(event) {
    //     if (!this.canOperate || !this.isRotating) return;

    //     const mousePos = event.getLocation();

    //     // 如果是第一次移动，记录初始位置
    //     if (!this.lastTouchPos || this.lastTouchPos.x === 0) {
    //         this.lastTouchPos = { x: mousePos.x, y: mousePos.y };
    //         return;
    //     }

    //     const ballPos = this.whiteBall.node.worldPosition;

    //     // 计算移动距离，如果移动太小则忽略（避免噪声）
    //     const deltaX = mousePos.x - this.lastTouchPos.x;
    //     const deltaY = mousePos.y - this.lastTouchPos.y;
    //     const moveDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    //     if (moveDistance < 1) {
    //         return; // 移动太小，忽略
    //     }

    //     // 统一使用角度方法，避免模式切换导致的不连贯
    //     const lastAngle = Math.atan2(this.lastTouchPos.y - ballPos.y, this.lastTouchPos.x - ballPos.x);
    //     const currentPosAngle = Math.atan2(mousePos.y - ballPos.y, mousePos.x - ballPos.x);

    //     // 计算角度变化（弧度）
    //     let angleDelta = currentPosAngle - lastAngle;

    //     // 处理角度跨越边界的情况（-π到π的跳跃）
    //     if (angleDelta > Math.PI) {
    //         angleDelta -= 2 * Math.PI;
    //     } else if (angleDelta < -Math.PI) {
    //         angleDelta += 2 * Math.PI;
    //     }

    //     // 转换为度数
    //     const angleChangeDegrees = angleDelta * 180 / Math.PI;

    //     // 直接累积到当前角度，避免totalRotation过大导致的精度问题
    //     this.currentAngle += angleChangeDegrees;

    //     // 使用数学方法进行角度归一化，更稳定
    //     this.currentAngle = this.normalizeAngle(this.currentAngle);

    //     // 更新上次触摸位置
    //     this.lastTouchPos.x = mousePos.x;
    //     this.lastTouchPos.y = mousePos.y;

    //     // 立即更新球杆旋转
    //     this.updateCueRotation();
    // }

    // onTouchEnd(event = null) {
    //     this.isRotating = false;
    //     if (!this._cueSp.color.a || !this.canOperate || !this._isDrag) return;
    //     this.updateCueRotation();
    //     // console.log(`[球杆] 拖动结束，最终角度: ${this.currentAngle.toFixed(1)}°, 总旋转: ${this.totalRotation.toFixed(1)}°`);
    // }

    // 设置球杆角度
    updateCueRotation() {
        this.node.angle = this.currentAngle;
        this._rad = (this.currentAngle - 90) * Math.PI / 180;
        this.rayCast(this._rad, this.currentAngle, true);
    }

    // 角度归一化方法，确保角度在0-360度范围内
    normalizeAngle(angle: number): number {
        // 使用更精确的数学方法
        const normalized = angle - Math.floor(angle / 360) * 360;
        return normalized < 0 ? normalized + 360 : normalized;
    }

    // 计算两个向量之间的距离
    distance(a, b): number {
        const dx = a.x - b.x;
        const dy = a.y - b.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    rayCast(rad: number, angle: number, isSendMsg = false) {
        this.handleCue(rad, angle);
        if (isSendMsg) {
            GameService.instance.DragMoveRequest({
                cue: JSON.stringify({ rad: rad, angle: angle }),
                whiteBall: JSON.stringify({ x: this.ballPos.x, y: this.ballPos.y })
            })
        }
    }
    drawRay(startPos: Vec2, result: RaycastResult2D) {
        let distance = this.distance(startPos, result.point);
        this._cueBallTip.updateUI(startPos, result, distance);
    }

    setRad(e = null): number {
        const location = e ? Utils.getUILocation(e) : v2(view.getDesignResolutionSize().width * 0.5, 0);
        const cueBallPos = this.whiteBall.node.worldPosition;
        const rad = Math.atan2(location.y - cueBallPos.y, location.x - cueBallPos.x);
        const angle = rad * macro.DEG + 90;
        this.angle = angle;
        this._rad = rad;
        return rad;
    }

    stopDrag() {
        this.isRotating = false;
        this._isDrag = false;
    }

    handleCue(rad: number, angle: number) {
        if (!PhysicsSystem2D.instance.enable || GameData.isGameOver || !this.whiteBall) return;
        this._rad = rad;
        this.angle = angle;
        this._dir = v2(Math.cos(rad), Math.sin(rad)).negative();
        let cueBallPos = this.whiteBall.node.getWorldPosition();
        let p1 = v2(cueBallPos.x, cueBallPos.y);
        let p2 = this._dir.clone().multiplyScalar(1000).add(p1);
        let results = PhysicsSystem2D.instance.raycast(p1, p2, ERaycast2DType.Closest);
        if (!results || results.length < 1) return
        this.drawRay(p1, results[0]);
    }

    handleBallHit(hitball) {
        this.isDrag = false;
        let power = hitball.hitImpulse.x;
        let angle = hitball.cue.angle;
        let rad = hitball.cue.rad;
        let percent = hitball.hitImpulse.percent;
        let dir = hitball.cue.dir;
        this._rad = rad;
        if (this.angle != angle) this.angle = angle;
        this._dir = v2(dir.x, dir.y);
        this.ballHit(power, percent);
    }

    ballHit(power: number, percent: number = 1) {
        this.clearHitTimeOut();
        this._transform.anchorY = 1;
        let impulse = this._dir.multiplyScalar(power);
        this.whiteBall.rb2D.applyLinearImpulseToCenter(impulse, true);
        BallCtrl.shootCue();

        let self = this;
        if (Global.isResume) {
            this._hitBall.active = true;
            this._hitTimeOut = setTimeout(() => {
                self._hitBall.active = false;
                self.updateActive(false);
            }, 100);
        } else {
            this.updateActive(false);
        }

        AudioMgr.playEffect(AudioMgr.audios.cueHitBall, percent);
    }

    /**球停，需要更新球杆角度 切换到下一个用户 */
    ballStop_updateCue(billiardsStatus, ballDic, cueBallType = '') {
        // if (GameData.isGameOver || BallCtrl.isOtherView()) return;
        if (GameData.isGameOver) return;
        let result;
        const cueBallPos = this.whiteBall?.node.position;
        if (billiardsStatus == BilliardsStatus.Object) {
            result = this._cueBallTip.cueAngleHitStatus(this.whiteBall, ballDic, cueBallType);
        } else {
            result = this._cueBallTip.cueAngleOpenStatus(cueBallPos, ballDic);
        }
        // GameUtil.log(result);
        if (result) {
            //TODO::再开一局，当前自己操作，可是球杆的色值是0，不可见
            this._cueSp.color = GameData.isMyOperate ? this._color255 : this._color125;
            this.handleCue(result[0], result[1]);
        }
    }

    updateActive(isShow: boolean) {
        this._cueSp.color = isShow ? this._color255 : this._color0;
        if (!isShow) this._rayLineTransform.height = 0;
    }

    playerChange(isMyOperate: boolean) {
        this.clearHitTimeOut(); //TODO::手机卡顿情况下，会出现击球后的settimeout执行晚于playerchange，导致球杆不显示
        this._cueSp.color = isMyOperate ? this._color255 : this._color125;
        this.isDrag = isMyOperate;
        this.updateCuePos();
        this._transform.anchorY = this._rawCueAnchorY;
    }

    updateCuePos() {
        this.whiteBall && this.node.setPosition(this.whiteBall.x, this.whiteBall.y, 0);
    }

    set isDrag(v) {
        this._isDrag = v;
    }

    get rad() {
        return this._rad;
    }

    get angle() {
        return this.node.angle;
    }
    set angle(v) {
        this.node.angle = v;
    }

    get ballPos() {
        return this.whiteBall.node.position;
    }

    get isVisible() {
        return this.node.active && (this._cueSp.color == this._color255);
    }

    get dir() {
        return this._dir;
    }

    clearHitTimeOut() {
        if (this._hitTimeOut) {
            this.updateActive(false);
            this._hitBall.active = false;
            clearTimeout(this._hitTimeOut);
            //TODO::弱网情况下，发送击球情况下，消息顺序可能不对，先收到切换用户，击球响应可能收不到
            this._hitTimeOut = 0;
        }
    }

    resetCue() {
        this.currentAngle = 0;
    }

    clear() {
        clearTimeout(this._hitTimeOut);
        this._hitTimeOut = 0;
        if (this.touchNode?.isValid) {
            this.touchNode.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
            this.touchNode.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
            this.touchNode.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);
            this.touchNode.off(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        }
        EventMgr.removeEventListener(EnumEvent.CUSTOM_CUE_POSITION_UPDATE, this.updateCueInfo, this);
    }

    onDestroy() {
        this.clear();
    }
}

