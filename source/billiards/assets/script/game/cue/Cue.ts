import { _decorator, Component, UITransform, EventTouch, Vec2, PhysicsSystem2D, ERaycast2DType, v2, view, Node, RaycastResult2D, macro, Sprite, color, Input } from 'cc';
import BallCtrl from '../ball/BallCtrl';
import GameData from '../data/GameData';
import { CueBallTip } from './CueBallTip';
import GameService from '../data/GameService';
import { BilliardsStatus } from '../data/Enum';
import Global from '../../core/data/Global';
import AudioMgr from '../../core/tools/AudioMgr';
import Utils from '../../core/tools/Utils';
import { EnumEvent, EventMgr } from '../../core/event/EventManager';
import { Ball } from '../ball/Ball';
const { ccclass } = _decorator;

@ccclass('Cue')
export class Cue extends Component {
    
    private _cueSp: Sprite = null;
    private _cueBall: Ball = null;
    private _transform: UITransform = null;

    private _rayLine: Node = null;
    private _rayLineTransform: UITransform = null;
    private _cueBallTip: CueBallTip = null;
    private _hitBall: Node = null;
    private _hitTimeOut;

    private _dir: Vec2 = null;
    private _rawCueAnchorY = 1.05;
    public _rad: number = 0;
    private _isDrag: boolean = false;
    private _color255 = color(255, 255, 255, 255);
    private _color125 = color(255, 255, 255, 125);
    private _color0 = color(255, 255, 255, 0);

    private touchNode: Node = null;//触摸节点

    // private isRotating: boolean = false; // 是否正在旋转
    // private currentAngle: number = 0; // 当前旋转角度（可以超过360度）
    // private lastTouchPos: { x: number, y: number } = { x: 0, y: 0 }; // 上次触摸位置
    private totalRotation: number = 0; // 总旋转角度（支持多圈）

    // {{ YUM: [新增] - 球杆跟踪相关变量 }}
    private currentAngle: number = 0;
    private isRotating: boolean = false;
    private lastTouchPos: Vec2 = new Vec2();

    // {{ YUM: [新增] - 调试参数 }}
    private touchDistance: number = 0;
    private touchSpeed: number = 0;
    private smoothFactor: number = 0.8;
    private angleDiff: number = 0;

    onLoad() {
        this._rad = 0;
        this._cueSp = this.getComponent(Sprite);

        this._transform = this.getComponent(UITransform);
        this._cueBallTip = this.node.getComponent(CueBallTip);
        this._rayLine = this.node.getChildByName('ray');
        this._rayLineTransform = this._rayLine.getComponent(UITransform);
        this._hitBall = this.node.getChildByName('hitball');

        
        this.touchNode = this.node.parent.getChildByPath('touchNode');
        this.touchNode.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        this.touchNode.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.touchNode.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        // this.touchNode.on(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        // EventMgr.addEventListener(EnumEvent.CUSTOM_CUE_POSITION_UPDATE, this.updateCueInfo, this);
    }

    init(cueBall: Ball) {
        this._cueBall = cueBall;
        this.resetCue();
        this.updateCuePos();
        // this._transform.anchorY = this._rawCueAnchorY;
        // this._rayLine.setPosition(0, GameData.rayOffsetY);
        this.isDrag = GameData.isMyOperate;
        if (!GameData.isMyOperate) {
            this._cueSp.color = this._color125;
        }
    }

    /**滑动 方向杆 */
    fineTurning(deltaAngle) {
        this.node.angle += deltaAngle;
        let rad = (this.angle - 90) * Math.PI / 180;
        this.rayCast(rad, this.angle, true);
    }

    /**
     * TODO::还要结合是否自己操作状态，这里暂时不考虑
     */
    get canOperate(): boolean {
        if (!this._cueBall) return false;
        if (GameData.isGameOver) return false;
        if (this._cueBall.isValidPos) return false;
        return GameData.isMyOperate;
    }

    /**更改球杆锚点 */
    // changeCueAnchorY(anchorY: number) {
    //     this._transform.anchorY = anchorY + this._rawCueAnchorY;
    // }

    /**
     * 球杆射击白球
     * @param power 射击力度
     */
    shootCueBall(power: number, percent: number) {
        this.isDrag = false;
        this.ballHit(power, percent);
    }

    /**开球、移动球杆执行此方法 
     * 拖动母球，球位置更新后，需要延迟一帧更新辅助线。不延迟的话，辅助线计算还未结束，导致异常
    */
    updateCueInfo(eventName: string, isShow: any, isMyOperate = true) {
        // this.clearHitTimeOut();
        // this.updateCuePos();
        // this._cueSp.color = isShow ? (isMyOperate ? this._color255 : this._color125) : this._color0;
        // if (!isShow) return;
        // this._transform.anchorY = this._rawCueAnchorY;
        
        // this.scheduleOnce(() => {
        //     this.setRad();
        //     this.rayCast(this._rad, this.angle);
        // }, 0);
        
    }

    /**
     * {{ YUM: [新增] - 触摸开始事件处理 }}
     */
    private onTouchStart(event: EventTouch) {
        this.isRotating = true;

        const mousePos = event.getLocation();
        this.lastTouchPos.set(mousePos.x, mousePos.y);

        // 立即更新球杆指向触摸位置
        const ballPos = this._cueBall.node.getWorldPosition();
        const currentAngleRad = Math.atan2(mousePos.y - ballPos.y, mousePos.x - ballPos.x);
        const targetAngle = (currentAngleRad * 180 / Math.PI) + 90;
        this.currentAngle = this.normalizeAngle(targetAngle);
        this.updateCueRotation();

        console.log(`[CueDebugger] Touch start - Initial angle: ${this.currentAngle.toFixed(2)}°`);
    }

    /**
     * {{ YUM: [新增] - 触摸移动事件处理（核心跟踪逻辑） }}
     */
    private onTouchMove(event: EventTouch) {
        // if (!this.isRotating) return;

        const mousePos = event.getLocation();
        const ballPos = this._cueBall.node.getWorldPosition();

        // {{ YUM: [计算] - 触摸距离和速度 }}
        this.touchDistance = Math.sqrt(
            Math.pow(mousePos.x - ballPos.x, 2) +
            Math.pow(mousePos.y - ballPos.y, 2)
        );

        const deltaX = mousePos.x - this.lastTouchPos.x;
        const deltaY = mousePos.y - this.lastTouchPos.y;
        const moveDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        this.touchSpeed = moveDistance;

        // {{ YUM: [计算] - 目标角度 }}
        const currentAngleRad = Math.atan2(mousePos.y - ballPos.y, mousePos.x - ballPos.x);
        const targetAngle = (currentAngleRad * 180 / Math.PI) + 90;

        const normalizedTarget = this.normalizeAngle(targetAngle);
        const normalizedCurrent = this.normalizeAngle(this.currentAngle);

        // {{ YUM: [计算] - 角度差 }}
        this.angleDiff = normalizedTarget - normalizedCurrent;
        if (this.angleDiff > 180) {
            this.angleDiff -= 360;
        } else if (this.angleDiff < -180) {
            this.angleDiff += 360;
        }

        // {{ YUM: [计算] - 自适应阈值 }}
        const baseThreshold = 0.5;
        const distanceFactor = Math.max(0.1, Math.min(1.0, this.touchDistance / 200));
        const speedFactor = Math.max(0.1, Math.min(1.0, 10 / (this.touchSpeed + 1)));
        const adaptiveThreshold = baseThreshold * distanceFactor * speedFactor;

        console.log(adaptiveThreshold+"====拖动角度 差值angleDiff；" + this.angleDiff);
        if (Math.abs(this.angleDiff) < adaptiveThreshold) {
            // return;
        }

        // {{ YUM: [计算] - 自适应平滑系数 }}
        const isRapidCircling = this.touchSpeed > 25 && this.touchDistance < 150;

        if (this.touchDistance < 80) {
            this.smoothFactor = isRapidCircling ? 0.98 : 0.95;
        } else if (this.touchDistance < 150) {
            if (isRapidCircling) {
                this.smoothFactor = 0.95;
            } else {
                this.smoothFactor = Math.max(0.85, 1.0 - this.touchSpeed * 0.005);
            }
        } else if (this.touchSpeed > 30) {
            this.smoothFactor = Math.min(0.92, 0.75 + this.touchSpeed * 0.005);
        } else {
            this.smoothFactor = 0.8;
        }

        if (Math.abs(this.angleDiff) > 45) {
            this.smoothFactor = Math.max(this.smoothFactor, 0.92);
        } else if (Math.abs(this.angleDiff) > 20) {
            this.smoothFactor = Math.max(this.smoothFactor, 0.88);
        }

        if (isRapidCircling && Math.abs(this.angleDiff) > 10) {
            this.smoothFactor = Math.max(this.smoothFactor, 0.9);
        }

        // {{ YUM: [应用] - 角度变化 }}
        const oldAngle = this.currentAngle;
        this.currentAngle += this.angleDiff * this.smoothFactor;
        this.currentAngle = this.normalizeAngle(this.currentAngle);

        // {{ YUM: [更新] - 显示和位置 }}
        this.lastTouchPos.set(mousePos.x, mousePos.y);

        // // {{ YUM: [记录] - 轨迹点 }}
        // if (this.showTrajectory) {
        //     this.trajectoryPoints.push(new Vec2(mousePos.x, mousePos.y));
        //     // 限制轨迹点数量，避免内存过多占用
        //     if (this.trajectoryPoints.length > 50) {
        //         this.trajectoryPoints.shift();
        //     }
        // }

        this.updateCueRotation();
        // this.updateDebugLabels();
        // this.drawDebugInfo(mousePos);

        // {{ YUM: [调试] - 输出关键参数 }}
        if (this.touchSpeed > 15 || this.touchDistance < 120) {
            console.log(`[CueDebugger] Distance: ${this.touchDistance.toFixed(1)}, Speed: ${this.touchSpeed.toFixed(1)}, AngleDiff: ${this.angleDiff.toFixed(2)}°, Smooth: ${this.smoothFactor.toFixed(2)}, Angle: ${oldAngle.toFixed(1)}° → ${this.currentAngle.toFixed(1)}°`);
        }
    }

    /**
     * {{ YUM: [新增] - 触摸结束事件处理 }}
     */
    private onTouchEnd(event: EventTouch) {
        this.isRotating = false;
        console.log(`[CueDebugger] Touch end - Final angle: ${this.currentAngle.toFixed(2)}°`);

        // // {{ YUM: [清除] - 轨迹记录 }}
        // this.trajectoryPoints = [];

        // // 清除调试绘制
        // if (this.debugGraphics) {
        //     this.debugGraphics.clear();
        // }
    }

    /**
     * {{ YUM: [新增] - 角度归一化 }}
     */
    private normalizeAngle(angle: number): number {
        if (isNaN(angle) || !isFinite(angle)) {
            return 0;
        }

        angle = angle % 360;
        if (angle < 0) {
            angle += 360;
        }
        return angle;
    }

    /**
     * {{ YUM: [新增] - 更新球杆旋转 }}
     */
    private updateCueRotation() {
        if (!this.node) return;

        // 设置球杆角度
        this.node.setRotationFromEuler(0, 0, this.currentAngle);

        // // 计算球杆位置（距离球心100像素）
        // const radian = (this.currentAngle - 90) * Math.PI / 180;
        // const cueDistance = 100;
        // const cueX = Math.cos(radian) * cueDistance;
        // const cueY = Math.sin(radian) * cueDistance;

        // this.cueNode.setPosition(cueX, cueY, 0);
    }

    // /**
    //  * {{ YUM: [新增] - 更新调试标签显示 }}
    //  */
    // private updateDebugLabels() {
    //     if (this.angleLabel) {
    //         this.angleLabel.string = `角度: ${this.currentAngle.toFixed(2)}°`;
    //     }

    //     if (this.distanceLabel) {
    //         this.distanceLabel.string = `距离: ${this.touchDistance.toFixed(1)}px`;
    //     }

    //     if (this.speedLabel) {
    //         this.speedLabel.string = `速度: ${this.touchSpeed.toFixed(1)}px/frame`;
    //     }

    //     if (this.smoothFactorLabel) {
    //         this.smoothFactorLabel.string = `平滑系数: ${this.smoothFactor.toFixed(3)}`;
    //     }
    // }

    // /**
    //  * {{ YUM: [增强] - 绘制完整调试信息和辅助线 }}
    //  */
    // private drawDebugInfo(touchPos: Vec2) {
    //     if (!this.debugGraphics) return;

    //     this.debugGraphics.clear();

    //     const ballPos = this.ballNode.getWorldPosition();

    //     // {{ YUM: [绘制] - 角度网格 }}
    //     if (this.showAngleGrid) {
    //         this.drawAngleGrid(ballPos);
    //     }

    //     // {{ YUM: [绘制] - 距离环 }}
    //     if (this.showDistanceRings) {
    //         this.drawDistanceRings(ballPos);
    //     }

    //     // {{ YUM: [绘制] - 阈值区域 }}
    //     if (this.showThresholdZone) {
    //         this.drawThresholdZone(ballPos);
    //     }

    //     // {{ YUM: [绘制] - 轨迹 }}
    //     if (this.showTrajectory && this.trajectoryPoints.length > 1) {
    //         this.drawTrajectory();
    //     }

    //     // {{ YUM: [绘制] - 触摸点到球心的连线 }}
    //     this.debugGraphics.strokeColor = Color.RED;
    //     this.debugGraphics.lineWidth = 2;
    //     this.debugGraphics.moveTo(ballPos.x, ballPos.y);
    //     this.debugGraphics.lineTo(touchPos.x, touchPos.y);
    //     this.debugGraphics.stroke();

    //     // {{ YUM: [绘制] - 触摸点 }}
    //     this.debugGraphics.fillColor = Color.RED;
    //     this.debugGraphics.circle(touchPos.x, touchPos.y, 5);
    //     this.debugGraphics.fill();

    //     // {{ YUM: [绘制] - 当前距离圆圈 }}
    //     this.debugGraphics.strokeColor = new Color(255, 0, 0, 150);
    //     this.debugGraphics.lineWidth = 2;
    //     this.debugGraphics.circle(ballPos.x, ballPos.y, this.touchDistance);
    //     this.debugGraphics.stroke();

    //     // {{ YUM: [绘制] - 球杆方向线 }}
    //     this.debugGraphics.strokeColor = Color.GREEN;
    //     this.debugGraphics.lineWidth = 4;
    //     const radian = (this.currentAngle - 90) * Math.PI / 180;
    //     const lineLength = 150;
    //     const endX = ballPos.x + Math.cos(radian) * lineLength;
    //     const endY = ballPos.y + Math.sin(radian) * lineLength;
    //     this.debugGraphics.moveTo(ballPos.x, ballPos.y);
    //     this.debugGraphics.lineTo(endX, endY);
    //     this.debugGraphics.stroke();

    //     // {{ YUM: [绘制] - 速度向量 }}
    //     if (this.showSpeedVector && this.touchSpeed > 1) {
    //         this.drawSpeedVector(touchPos);
    //     }
    // }

    // /**
    //  * {{ YUM: [新增] - 绘制角度网格 }}
    //  */
    // private drawAngleGrid(ballPos: Vec3) {
    //     this.debugGraphics.strokeColor = new Color(128, 128, 128, 100);
    //     this.debugGraphics.lineWidth = 1;

    //     const gridRadius = 200;
    //     const angleStep = 30; // 每30度一条线

    //     for (let angle = 0; angle < 360; angle += angleStep) {
    //         const radian = angle * Math.PI / 180;
    //         const endX = ballPos.x + Math.cos(radian) * gridRadius;
    //         const endY = ballPos.y + Math.sin(radian) * gridRadius;

    //         this.debugGraphics.moveTo(ballPos.x, ballPos.y);
    //         this.debugGraphics.lineTo(endX, endY);
    //         this.debugGraphics.stroke();
    //     }
    // }

    // /**
    //  * {{ YUM: [新增] - 绘制距离环 }}
    //  */
    // private drawDistanceRings(ballPos: Vec3) {
    //     this.debugGraphics.strokeColor = new Color(0, 255, 255, 80);
    //     this.debugGraphics.lineWidth = 1;

    //     const rings = [50, 100, 150, 200, 250]; // 距离环半径

    //     for (const radius of rings) {
    //         this.debugGraphics.circle(ballPos.x, ballPos.y, radius);
    //         this.debugGraphics.stroke();
    //     }
    // }

    // /**
    //  * {{ YUM: [新增] - 绘制阈值区域 }}
    //  */
    // private drawThresholdZone(ballPos: Vec3) {
    //     // 绘制近距离高敏感区域
    //     this.debugGraphics.strokeColor = new Color(255, 255, 0, 120);
    //     this.debugGraphics.lineWidth = 2;
    //     this.debugGraphics.circle(ballPos.x, ballPos.y, 80); // 近距离区域
    //     this.debugGraphics.stroke();

    //     // 绘制中距离区域
    //     this.debugGraphics.strokeColor = new Color(255, 165, 0, 100);
    //     this.debugGraphics.lineWidth = 1;
    //     this.debugGraphics.circle(ballPos.x, ballPos.y, 150); // 中距离区域
    //     this.debugGraphics.stroke();
    // }

    // /**
    //  * {{ YUM: [新增] - 绘制轨迹 }}
    //  */
    // private drawTrajectory() {
    //     if (this.trajectoryPoints.length < 2) return;

    //     this.debugGraphics.strokeColor = new Color(255, 0, 255, 150);
    //     this.debugGraphics.lineWidth = 2;

    //     // 绘制轨迹线
    //     this.debugGraphics.moveTo(this.trajectoryPoints[0].x, this.trajectoryPoints[0].y);
    //     for (let i = 1; i < this.trajectoryPoints.length; i++) {
    //         this.debugGraphics.lineTo(this.trajectoryPoints[i].x, this.trajectoryPoints[i].y);
    //     }
    //     this.debugGraphics.stroke();

    //     // 绘制轨迹点
    //     this.debugGraphics.fillColor = new Color(255, 0, 255, 200);
    //     for (const point of this.trajectoryPoints) {
    //         this.debugGraphics.circle(point.x, point.y, 2);
    //         this.debugGraphics.fill();
    //     }
    // }

    // /**
    //  * {{ YUM: [新增] - 绘制速度向量 }}
    //  */
    // private drawSpeedVector(touchPos: Vec2) {
    //     if (this.trajectoryPoints.length < 2) return;

    //     const lastPoint = this.trajectoryPoints[this.trajectoryPoints.length - 2];
    //     const currentPoint = touchPos;

    //     // 计算速度向量
    //     const deltaX = currentPoint.x - lastPoint.x;
    //     const deltaY = currentPoint.y - lastPoint.y;

    //     // 放大速度向量以便可视化
    //     const scale = 3;
    //     const vectorEndX = currentPoint.x + deltaX * scale;
    //     const vectorEndY = currentPoint.y + deltaY * scale;

    //     // 绘制速度向量
    //     this.debugGraphics.strokeColor = new Color(255, 128, 0, 200);
    //     this.debugGraphics.lineWidth = 3;
    //     this.debugGraphics.moveTo(currentPoint.x, currentPoint.y);
    //     this.debugGraphics.lineTo(vectorEndX, vectorEndY);
    //     this.debugGraphics.stroke();

    //     // 绘制箭头
    //     const arrowLength = 10;
    //     const arrowAngle = Math.atan2(deltaY, deltaX);
    //     const arrowX1 = vectorEndX - arrowLength * Math.cos(arrowAngle - 0.5);
    //     const arrowY1 = vectorEndY - arrowLength * Math.sin(arrowAngle - 0.5);
    //     const arrowX2 = vectorEndX - arrowLength * Math.cos(arrowAngle + 0.5);
    //     const arrowY2 = vectorEndY - arrowLength * Math.sin(arrowAngle + 0.5);

    //     this.debugGraphics.moveTo(vectorEndX, vectorEndY);
    //     this.debugGraphics.lineTo(arrowX1, arrowY1);
    //     this.debugGraphics.moveTo(vectorEndX, vectorEndY);
    //     this.debugGraphics.lineTo(arrowX2, arrowY2);
    //     this.debugGraphics.stroke();
    // }

    // /**
    //  * {{ YUM: [新增] - 切换辅助线显示 }}
    //  */
    // public toggleAngleGrid() { this.showAngleGrid = !this.showAngleGrid; }
    // public toggleDistanceRings() { this.showDistanceRings = !this.showDistanceRings; }
    // public toggleSpeedVector() { this.showSpeedVector = !this.showSpeedVector; }
    // public toggleThresholdZone() { this.showThresholdZone = !this.showThresholdZone; }
    // public toggleTrajectory() { this.showTrajectory = !this.showTrajectory; }

    // /**
    //  * {{ YUM: [新增] - 重置调试器 }}
    //  */
    // public resetDebugger() {
    //     this.currentAngle = 0;
    //     this.isRotating = false;
    //     this.touchDistance = 0;
    //     this.touchSpeed = 0;
    //     this.smoothFactor = 0.8;
    //     this.angleDiff = 0;

    //     // {{ YUM: [清除] - 轨迹记录 }}
    //     this.trajectoryPoints = [];

    //     this.updateCueRotation();
    //     this.updateDebugLabels();

    //     if (this.debugGraphics) {
    //         this.debugGraphics.clear();
    //     }

    //     console.log('[CueDebugger] 调试器已重置');
    // }

    // 计算两个向量之间的距离
    distance(a, b): number {
        const dx = a.x - b.x;
        const dy = a.y - b.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    rayCast(rad: number, angle: number, isSendMsg = false) {
        this.handleCue(rad, angle);
        if (isSendMsg) {
            GameService.instance.DragMoveRequest({
                cue: JSON.stringify({ rad: rad, angle: angle }),
                whiteBall: JSON.stringify({ x: this.ballPos.x, y: this.ballPos.y })
            })
        }
    }
    drawRay(startPos: Vec2, result: RaycastResult2D) {
        // let distance = this.distance(startPos, result.point);
        // this._cueBallTip.updateUI(startPos, result, distance);
    }

    setRad(e = null): number {
        const location = e ? Utils.getUILocation(e) : v2(view.getDesignResolutionSize().width * 0.5, 0);
        const cueBallPos = this._cueBall.node.worldPosition;
        const rad = Math.atan2(location.y - cueBallPos.y, location.x - cueBallPos.x);
        const angle = rad * macro.DEG + 90;
        this.angle = angle;
        this._rad = rad;
        return rad;
    }

    stopDrag() {
        this.isRotating = false;
        this._isDrag = false;
    }

    handleCue(rad: number, angle: number) {
        if (!PhysicsSystem2D.instance.enable || GameData.isGameOver || !this._cueBall) return;
        this._rad = rad;
        this.angle = angle;
        this._dir = v2(Math.cos(rad), Math.sin(rad)).negative();
        let cueBallPos = this._cueBall.node.getWorldPosition();
        let p1 = v2(cueBallPos.x, cueBallPos.y);
        let p2 = this._dir.clone().multiplyScalar(1000).add(p1);
        let results = PhysicsSystem2D.instance.raycast(p1, p2, ERaycast2DType.Closest);
        if (!results || results.length < 1) return
        this.drawRay(p1, results[0]);
    }

    handleBallHit(hitball) {
        this.isDrag = false;
        let power = hitball.hitImpulse.x;
        let angle = hitball.cue.angle;
        let rad = hitball.cue.rad;
        let percent = hitball.hitImpulse.percent;
        let dir = hitball.cue.dir;
        this._rad = rad;
        if (this.angle != angle) this.angle = angle;
        this._dir = v2(dir.x, dir.y);
        this.ballHit(power, percent);
    }

    ballHit(power: number, percent: number = 1) {
        this.clearHitTimeOut();
        this._transform.anchorY = 1;
        let impulse = this._dir.multiplyScalar(power);
        this._cueBall.rb2D.applyLinearImpulseToCenter(impulse, true);
        BallCtrl.shootCue();

        let self = this;
        if (Global.isResume) {
            this._hitBall.active = true;
            this._hitTimeOut = setTimeout(() => {
                self._hitBall.active = false;
                self.updateActive(false);
            }, 100);
        } else {
            this.updateActive(false);
        }

        AudioMgr.playEffect(AudioMgr.audios.cueHitBall, percent);
    }

    /**球停，需要更新球杆角度 切换到下一个用户 */
    ballStop_updateCue(billiardsStatus, ballDic, cueBallType = '') {
        // // if (GameData.isGameOver || BallCtrl.isOtherView()) return;
        // if (GameData.isGameOver) return;
        // let result;
        // const cueBallPos = this._cueBall?.node.position;
        // if (billiardsStatus == BilliardsStatus.Object) {
        //     result = this._cueBallTip.cueAngleHitStatus(this._cueBall, ballDic, cueBallType);
        // } else {
        //     result = this._cueBallTip.cueAngleOpenStatus(cueBallPos, ballDic);
        // }
        // // GameUtil.log(result);
        // if (result) {
        //     //TODO::再开一局，当前自己操作，可是球杆的色值是0，不可见
        //     this._cueSp.color = GameData.isMyOperate ? this._color255 : this._color125;
        //     this.handleCue(result[0], result[1]);
        // }
    }

    updateActive(isShow: boolean) {
        this._cueSp.color = isShow ? this._color255 : this._color0;
        if (!isShow) this._rayLineTransform.height = 0;
    }

    playerChange(isMyOperate: boolean) {
        this.clearHitTimeOut(); //TODO::手机卡顿情况下，会出现击球后的settimeout执行晚于playerchange，导致球杆不显示
        this._cueSp.color = isMyOperate ? this._color255 : this._color125;
        this.isDrag = isMyOperate;
        this.updateCuePos();
        this._transform.anchorY = this._rawCueAnchorY;
    }

    updateCuePos() {
        this._cueBall && this.node.setPosition(this._cueBall.x, this._cueBall.y, 0);
    }

    set isDrag(v) {
        this._isDrag = v;
    }

    get rad() {
        return this._rad;
    }

    get angle() {
        return this.node.angle;
    }
    set angle(v) {
        this.node.angle = v;
    }

    get ballPos() {
        return this._cueBall.node.position;
    }

    get isVisible() {
        return this.node.active && (this._cueSp.color == this._color255);
    }

    get dir() {
        return this._dir;
    }

    clearHitTimeOut() {
        if (this._hitTimeOut) {
            this.updateActive(false);
            this._hitBall.active = false;
            clearTimeout(this._hitTimeOut);
            //TODO::弱网情况下，发送击球情况下，消息顺序可能不对，先收到切换用户，击球响应可能收不到
            this._hitTimeOut = 0;
        }
    }

    resetCue() {
        this.currentAngle = 0;
    }

    clear() {
        clearTimeout(this._hitTimeOut);
        this._hitTimeOut = 0;
        if (this.touchNode?.isValid) {
            this.touchNode.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
            this.touchNode.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
            this.touchNode.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);
            this.touchNode.off(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        }
        EventMgr.removeEventListener(EnumEvent.CUSTOM_CUE_POSITION_UPDATE, this.updateCueInfo, this);
    }

    onDestroy() {
        this.clear();
    }
}

