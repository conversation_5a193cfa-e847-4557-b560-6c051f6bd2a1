import { _decorator, Component, Node, Label, Sprite, Vec3, UITransform, Color, Graphics, Vec2, EventTouch, input, Input } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 球杆调试器 - 专门用于测试和调试球杆跟踪逻辑
 * 提供可视化界面和实时参数显示
 */
@ccclass('CueDebugger')
export class CueDebugger extends Component {
    
    @property(Node)
    ballNode: Node = null; // 模拟球的节点
    
    @property(Node)
    cueNode: Node = null; // 模拟球杆的节点
    
    @property(Label)
    angleLabel: Label = null; // 显示角度
    
    @property(Label)
    distanceLabel: Label = null; // 显示距离
    
    @property(Label)
    speedLabel: Label = null; // 显示速度
    
    @property(Label)
    smoothFactorLabel: Label = null; // 显示平滑系数
    
    @property(Graphics)
    debugGraphics: Graphics = null; // 用于绘制调试信息
    
    // {{ YUM: [新增] - 球杆跟踪相关变量 }}
    private currentAngle: number = 0;
    private isRotating: boolean = false;
    private lastTouchPos: Vec2 = new Vec2();
    
    // {{ YUM: [新增] - 调试参数 }}
    private touchDistance: number = 0;
    private touchSpeed: number = 0;
    private smoothFactor: number = 0.8;
    private angleDiff: number = 0;
    
    // {{ YUM: [新增] - 辅助线显示控制 }}
    private showAngleGrid: boolean = true;        // 显示角度网格
    private showDistanceRings: boolean = true;    // 显示距离环
    private showSpeedVector: boolean = true;      // 显示速度向量
    private showThresholdZone: boolean = true;    // 显示阈值区域
    private showTrajectory: boolean = true;       // 显示轨迹
    private trajectoryPoints: Vec2[] = [];        // 轨迹点记录
    
    onLoad() {
        this.initializeDebugScene();
        this.setupTouchEvents();
    }
    
    /**
     * {{ YUM: [新增] - 初始化调试场景 }}
     */
    private initializeDebugScene() {
        // 设置球的位置（屏幕中心）
        if (this.ballNode) {
            this.ballNode.setPosition(0, 0, 0);
        }
        
        // 设置球杆的初始位置和角度
        if (this.cueNode) {
            this.cueNode.setPosition(0, 0, 0); // 球杆在球的上方
            this.currentAngle = 0;
        }
        
        // 初始化标签显示
        this.updateDebugLabels();
    }
    
    /**
     * {{ YUM: [新增] - 设置触摸事件监听 }}
     */
    private setupTouchEvents() {
        this.node.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
    }
    
    /**
     * {{ YUM: [新增] - 触摸开始事件处理 }}
     */
    private onTouchStart(event: EventTouch) {
        this.isRotating = true;
        
        const mousePos = event.getLocation();
        this.lastTouchPos.set(mousePos.x, mousePos.y);
        
        // 立即更新球杆指向触摸位置
        const ballPos = this.ballNode.getWorldPosition();
        const currentAngleRad = Math.atan2(mousePos.y - ballPos.y, mousePos.x - ballPos.x);
        const targetAngle = (currentAngleRad * 180 / Math.PI) + 90;
        this.currentAngle = this.normalizeAngle(targetAngle);
        this.updateCueRotation();
        
        console.log(`[CueDebugger] Touch start - Initial angle: ${this.currentAngle.toFixed(2)}°`);
    }
    
    /**
     * {{ YUM: [新增] - 触摸移动事件处理（核心跟踪逻辑） }}
     */
    private onTouchMove(event: EventTouch) {
        if (!this.isRotating) return;
        
        const mousePos = event.getLocation();
        const ballPos = this.ballNode.getWorldPosition();
        
        // {{ YUM: [计算] - 触摸距离和速度 }}
        this.touchDistance = Math.sqrt(
            Math.pow(mousePos.x - ballPos.x, 2) + 
            Math.pow(mousePos.y - ballPos.y, 2)
        );
        
        const deltaX = mousePos.x - this.lastTouchPos.x;
        const deltaY = mousePos.y - this.lastTouchPos.y;
        const moveDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        this.touchSpeed = moveDistance;
        
        // {{ YUM: [计算] - 目标角度 }}
        const currentAngleRad = Math.atan2(mousePos.y - ballPos.y, mousePos.x - ballPos.x);
        const targetAngle = (currentAngleRad * 180 / Math.PI) + 90;
        
        const normalizedTarget = this.normalizeAngle(targetAngle);
        const normalizedCurrent = this.normalizeAngle(this.currentAngle);
        
        // {{ YUM: [计算] - 角度差 }}
        this.angleDiff = normalizedTarget - normalizedCurrent;
        if (this.angleDiff > 180) {
            this.angleDiff -= 360;
        } else if (this.angleDiff < -180) {
            this.angleDiff += 360;
        }
        
        // {{ YUM: [计算] - 自适应阈值 }}
        const baseThreshold = 0.5;
        const distanceFactor = Math.max(0.1, Math.min(1.0, this.touchDistance / 200));
        const speedFactor = Math.max(0.1, Math.min(1.0, 10 / (this.touchSpeed + 1)));
        const adaptiveThreshold = baseThreshold * distanceFactor * speedFactor;
        
        if (Math.abs(this.angleDiff) < adaptiveThreshold) {
            return;
        }
        
        // {{ YUM: [计算] - 自适应平滑系数 }}
        const isRapidCircling = this.touchSpeed > 25 && this.touchDistance < 150;
        
        if (this.touchDistance < 80) {
            this.smoothFactor = isRapidCircling ? 0.98 : 0.95;
        } else if (this.touchDistance < 150) {
            if (isRapidCircling) {
                this.smoothFactor = 0.95;
            } else {
                this.smoothFactor = Math.max(0.85, 1.0 - this.touchSpeed * 0.005);
            }
        } else if (this.touchSpeed > 30) {
            this.smoothFactor = Math.min(0.92, 0.75 + this.touchSpeed * 0.005);
        } else {
            this.smoothFactor = 0.8;
        }
        
        if (Math.abs(this.angleDiff) > 45) {
            this.smoothFactor = Math.max(this.smoothFactor, 0.92);
        } else if (Math.abs(this.angleDiff) > 20) {
            this.smoothFactor = Math.max(this.smoothFactor, 0.88);
        }
        
        if (isRapidCircling && Math.abs(this.angleDiff) > 10) {
            this.smoothFactor = Math.max(this.smoothFactor, 0.9);
        }
        
        // {{ YUM: [应用] - 角度变化 }}
        const oldAngle = this.currentAngle;
        this.currentAngle += this.angleDiff * this.smoothFactor;
        this.currentAngle = this.normalizeAngle(this.currentAngle);
        
        // {{ YUM: [更新] - 显示和位置 }}
        this.lastTouchPos.set(mousePos.x, mousePos.y);
        
        // {{ YUM: [记录] - 轨迹点 }}
        if (this.showTrajectory) {
            this.trajectoryPoints.push(new Vec2(mousePos.x, mousePos.y));
            // 限制轨迹点数量，避免内存过多占用
            if (this.trajectoryPoints.length > 50) {
                this.trajectoryPoints.shift();
            }
        }
        
        this.updateCueRotation();
        this.updateDebugLabels();
        this.drawDebugInfo(mousePos);
        
        // {{ YUM: [调试] - 输出关键参数 }}
        if (this.touchSpeed > 15 || this.touchDistance < 120) {
            console.log(`[CueDebugger] Distance: ${this.touchDistance.toFixed(1)}, Speed: ${this.touchSpeed.toFixed(1)}, AngleDiff: ${this.angleDiff.toFixed(2)}°, Smooth: ${this.smoothFactor.toFixed(2)}, Angle: ${oldAngle.toFixed(1)}° → ${this.currentAngle.toFixed(1)}°`);
        }
    }
    
    /**
     * {{ YUM: [新增] - 触摸结束事件处理 }}
     */
    private onTouchEnd(event: EventTouch) {
        this.isRotating = false;
        console.log(`[CueDebugger] Touch end - Final angle: ${this.currentAngle.toFixed(2)}°`);
        
        // {{ YUM: [清除] - 轨迹记录 }}
        this.trajectoryPoints = [];
        
        // 清除调试绘制
        if (this.debugGraphics) {
            this.debugGraphics.clear();
        }
    }
    
    /**
     * {{ YUM: [新增] - 角度归一化 }}
     */
    private normalizeAngle(angle: number): number {
        if (isNaN(angle) || !isFinite(angle)) {
            return 0;
        }
        
        angle = angle % 360;
        if (angle < 0) {
            angle += 360;
        }
        return angle;
    }
    
    /**
     * {{ YUM: [新增] - 更新球杆旋转 }}
     */
    private updateCueRotation() {
        if (!this.cueNode) return;
        
        // 设置球杆角度
        this.cueNode.setRotationFromEuler(0, 0, this.currentAngle);
        
        // 计算球杆位置（距离球心100像素）
        const radian = (this.currentAngle - 90) * Math.PI / 180;
        const cueDistance = 100;
        const cueX = Math.cos(radian) * cueDistance;
        const cueY = Math.sin(radian) * cueDistance;
        
        // this.cueNode.setPosition(cueX, cueY, 0);
    }
    
    /**
     * {{ YUM: [新增] - 更新调试标签显示 }}
     */
    private updateDebugLabels() {
        if (this.angleLabel) {
            this.angleLabel.string = `角度: ${this.currentAngle.toFixed(2)}°`;
        }
        
        if (this.distanceLabel) {
            this.distanceLabel.string = `距离: ${this.touchDistance.toFixed(1)}px`;
        }
        
        if (this.speedLabel) {
            this.speedLabel.string = `速度: ${this.touchSpeed.toFixed(1)}px/frame`;
        }
        
        if (this.smoothFactorLabel) {
            this.smoothFactorLabel.string = `平滑系数: ${this.smoothFactor.toFixed(3)}`;
        }
    }
    
    /**
     * {{ YUM: [增强] - 绘制完整调试信息和辅助线 }}
     */
    private drawDebugInfo(touchPos: Vec2) {
        if (!this.debugGraphics) return;
        
        this.debugGraphics.clear();
        
        const ballPos = this.ballNode.getWorldPosition();
        
        // {{ YUM: [绘制] - 角度网格 }}
        if (this.showAngleGrid) {
            this.drawAngleGrid(ballPos);
        }
        
        // {{ YUM: [绘制] - 距离环 }}
        if (this.showDistanceRings) {
            this.drawDistanceRings(ballPos);
        }
        
        // {{ YUM: [绘制] - 阈值区域 }}
        if (this.showThresholdZone) {
            this.drawThresholdZone(ballPos);
        }
        
        // {{ YUM: [绘制] - 轨迹 }}
        if (this.showTrajectory && this.trajectoryPoints.length > 1) {
            this.drawTrajectory();
        }
        
        // {{ YUM: [绘制] - 触摸点到球心的连线 }}
        this.debugGraphics.strokeColor = Color.RED;
        this.debugGraphics.lineWidth = 2;
        this.debugGraphics.moveTo(ballPos.x, ballPos.y);
        this.debugGraphics.lineTo(touchPos.x, touchPos.y);
        this.debugGraphics.stroke();
        
        // {{ YUM: [绘制] - 触摸点 }}
        this.debugGraphics.fillColor = Color.RED;
        this.debugGraphics.circle(touchPos.x, touchPos.y, 5);
        this.debugGraphics.fill();
        
        // {{ YUM: [绘制] - 当前距离圆圈 }}
        this.debugGraphics.strokeColor = new Color(255, 0, 0, 150);
        this.debugGraphics.lineWidth = 2;
        this.debugGraphics.circle(ballPos.x, ballPos.y, this.touchDistance);
        this.debugGraphics.stroke();
        
        // {{ YUM: [绘制] - 球杆方向线 }}
        this.debugGraphics.strokeColor = Color.GREEN;
        this.debugGraphics.lineWidth = 4;
        const radian = (this.currentAngle - 90) * Math.PI / 180;
        const lineLength = 150;
        const endX = ballPos.x + Math.cos(radian) * lineLength;
        const endY = ballPos.y + Math.sin(radian) * lineLength;
        this.debugGraphics.moveTo(ballPos.x, ballPos.y);
        this.debugGraphics.lineTo(endX, endY);
        this.debugGraphics.stroke();
        
        // {{ YUM: [绘制] - 速度向量 }}
        if (this.showSpeedVector && this.touchSpeed > 1) {
            this.drawSpeedVector(touchPos);
        }
    }
    
    /**
     * {{ YUM: [新增] - 绘制角度网格 }}
     */
    private drawAngleGrid(ballPos: Vec3) {
        this.debugGraphics.strokeColor = new Color(128, 128, 128, 100);
        this.debugGraphics.lineWidth = 1;
        
        const gridRadius = 200;
        const angleStep = 30; // 每30度一条线
        
        for (let angle = 0; angle < 360; angle += angleStep) {
            const radian = angle * Math.PI / 180;
            const endX = ballPos.x + Math.cos(radian) * gridRadius;
            const endY = ballPos.y + Math.sin(radian) * gridRadius;
            
            this.debugGraphics.moveTo(ballPos.x, ballPos.y);
            this.debugGraphics.lineTo(endX, endY);
            this.debugGraphics.stroke();
        }
    }
    
    /**
     * {{ YUM: [新增] - 绘制距离环 }}
     */
    private drawDistanceRings(ballPos: Vec3) {
        this.debugGraphics.strokeColor = new Color(0, 255, 255, 80);
        this.debugGraphics.lineWidth = 1;
        
        const rings = [50, 100, 150, 200, 250]; // 距离环半径
        
        for (const radius of rings) {
            this.debugGraphics.circle(ballPos.x, ballPos.y, radius);
            this.debugGraphics.stroke();
        }
    }
    
    /**
     * {{ YUM: [新增] - 绘制阈值区域 }}
     */
    private drawThresholdZone(ballPos: Vec3) {
        // 绘制近距离高敏感区域
        this.debugGraphics.strokeColor = new Color(255, 255, 0, 120);
        this.debugGraphics.lineWidth = 2;
        this.debugGraphics.circle(ballPos.x, ballPos.y, 80); // 近距离区域
        this.debugGraphics.stroke();
        
        // 绘制中距离区域
        this.debugGraphics.strokeColor = new Color(255, 165, 0, 100);
        this.debugGraphics.lineWidth = 1;
        this.debugGraphics.circle(ballPos.x, ballPos.y, 150); // 中距离区域
        this.debugGraphics.stroke();
    }
    
    /**
     * {{ YUM: [新增] - 绘制轨迹 }}
     */
    private drawTrajectory() {
        if (this.trajectoryPoints.length < 2) return;
        
        this.debugGraphics.strokeColor = new Color(255, 0, 255, 150);
        this.debugGraphics.lineWidth = 2;
        
        // 绘制轨迹线
        this.debugGraphics.moveTo(this.trajectoryPoints[0].x, this.trajectoryPoints[0].y);
        for (let i = 1; i < this.trajectoryPoints.length; i++) {
            this.debugGraphics.lineTo(this.trajectoryPoints[i].x, this.trajectoryPoints[i].y);
        }
        this.debugGraphics.stroke();
        
        // 绘制轨迹点
        this.debugGraphics.fillColor = new Color(255, 0, 255, 200);
        for (const point of this.trajectoryPoints) {
            this.debugGraphics.circle(point.x, point.y, 2);
            this.debugGraphics.fill();
        }
    }
    
    /**
     * {{ YUM: [新增] - 绘制速度向量 }}
     */
    private drawSpeedVector(touchPos: Vec2) {
        if (this.trajectoryPoints.length < 2) return;
        
        const lastPoint = this.trajectoryPoints[this.trajectoryPoints.length - 2];
        const currentPoint = touchPos;
        
        // 计算速度向量
        const deltaX = currentPoint.x - lastPoint.x;
        const deltaY = currentPoint.y - lastPoint.y;
        
        // 放大速度向量以便可视化
        const scale = 3;
        const vectorEndX = currentPoint.x + deltaX * scale;
        const vectorEndY = currentPoint.y + deltaY * scale;
        
        // 绘制速度向量
        this.debugGraphics.strokeColor = new Color(255, 128, 0, 200);
        this.debugGraphics.lineWidth = 3;
        this.debugGraphics.moveTo(currentPoint.x, currentPoint.y);
        this.debugGraphics.lineTo(vectorEndX, vectorEndY);
        this.debugGraphics.stroke();
        
        // 绘制箭头
        const arrowLength = 10;
        const arrowAngle = Math.atan2(deltaY, deltaX);
        const arrowX1 = vectorEndX - arrowLength * Math.cos(arrowAngle - 0.5);
        const arrowY1 = vectorEndY - arrowLength * Math.sin(arrowAngle - 0.5);
        const arrowX2 = vectorEndX - arrowLength * Math.cos(arrowAngle + 0.5);
        const arrowY2 = vectorEndY - arrowLength * Math.sin(arrowAngle + 0.5);
        
        this.debugGraphics.moveTo(vectorEndX, vectorEndY);
        this.debugGraphics.lineTo(arrowX1, arrowY1);
        this.debugGraphics.moveTo(vectorEndX, vectorEndY);
        this.debugGraphics.lineTo(arrowX2, arrowY2);
        this.debugGraphics.stroke();
    }
    
    /**
     * {{ YUM: [新增] - 切换辅助线显示 }}
     */
    public toggleAngleGrid() { this.showAngleGrid = !this.showAngleGrid; }
    public toggleDistanceRings() { this.showDistanceRings = !this.showDistanceRings; }
    public toggleSpeedVector() { this.showSpeedVector = !this.showSpeedVector; }
    public toggleThresholdZone() { this.showThresholdZone = !this.showThresholdZone; }
    public toggleTrajectory() { this.showTrajectory = !this.showTrajectory; }
    
    /**
     * {{ YUM: [新增] - 重置调试器 }}
     */
    public resetDebugger() {
        this.currentAngle = 0;
        this.isRotating = false;
        this.touchDistance = 0;
        this.touchSpeed = 0;
        this.smoothFactor = 0.8;
        this.angleDiff = 0;
        
        // {{ YUM: [清除] - 轨迹记录 }}
        this.trajectoryPoints = [];
        
        this.updateCueRotation();
        this.updateDebugLabels();
        
        if (this.debugGraphics) {
            this.debugGraphics.clear();
        }
        
        console.log('[CueDebugger] 调试器已重置');
    }
}