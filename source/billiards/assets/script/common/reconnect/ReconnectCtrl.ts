import { instantiate,Node } from "cc";
import Global from "../../core/data/Global";
import { GameUtil } from "../../core/tools/GameUtil";
import { ReconnectParams } from "../../game/data/GameInterface";
import { GameNodePool } from "../../game/manager/GameNodePool";
import { dialogManager } from "../dialog/DialogManager";
import { ReconnectUI } from "./ReconnectUI";
import GameData from "../../game/data/GameData";

export class ReconnectCtrl {
    static _ins: ReconnectCtrl = null;
    static get ins(): ReconnectCtrl { return this._ins || (this._ins = new ReconnectCtrl) }
    static Event = { TIMEOVER: "connect_timeover" }
    private timeer = null;
    public justShowAni: boolean = false;
    public param: ReconnectParams = null
    private preventTouch: boolean = true;
    public onOpen: boolean = false;
    /**true：表示还在重连中  false 小马动画&重连弹框都关闭；  
     * 这个字段目的：30s后弹出是否重连面板，但是原生继续通知JsbWebsocket.onNetConnecting,导致小马动画覆盖在确认框上方 */
    private connectExist: boolean = false;
    /**status: -1(未知) 0(没有网络) 1(数据网络) 2（wifi） */
    public netState: number = 1;
    private _reconnectUI: ReconnectUI = null;
    public mainGame: Node = null;

    constructor() {
        // EventMgr.addEventListener(JsbEnumEvent.RESUME, this.onResume, this);
    }
    private onResume() {
        GameUtil.log("重连  socket onResume"+this.onOpen);
        if (this.onOpen) this.createConnectView();
    }
    connectTips(): void {
        this.connectSucss();
    }
    reconnectTips(isShow: boolean = true): void {
        isShow && this.connect();
    }
    requestTips(isShow: boolean): void { }
    /**
     * showConnect 断线重连小马动画
     * @param linktNow 是否立即重连 不展示小马动画
     * @param par ReconnectParams domino联赛需要改变参数，其它模式默认值就行
     * @param preventTouch 是否显示半透明黑色背景 Domino联赛模式会用到
     * @param noTicketLogin socket连上，但是游戏服的ticketLogin登陆失败，需要表现断线重连动画，且再次发起票据登陆
     */
    public connect(linktNow: boolean = true, param: ReconnectParams = {}, preventTouch: boolean = true, noTicketLogin = false) {
        GameUtil.log(this.connectExist + "=重连 =socket==" + GameData.isGameOver);
        if (this.connectExist || GameData.isGameOver) return;
        this.param = param;
        this.preventTouch = preventTouch;
        if (!linktNow && Global.isResume) {//暂时不显示小马动画 5s后没有连上再显示小马动画
            clearTimeout(this.timeer);
            this.timeer = setTimeout(this.showAniDelay.bind(this), 5000, noTicketLogin);
        } else {//立即显示小马动画
            this.createConnectView(this.param, preventTouch);
        }
    }
    public connectSucss(): void {
        GameUtil.log("重连 success");
        this.closeView();
        dialogManager.hideConfirm();
        this.connectExist = false;
    }
    public closeView() {
        // let view = uiManager.getUI(UIID.ReConnect);
        // console.log("重连 socket closeView", view);
        // this.onOpen = false;
        // view && uiManager.close(view);
        // clearTimeout(this.timeer);
        if (this._reconnectUI && this._reconnectUI.node.parent) {
            this._reconnectUI.onClose();
        }
        clearTimeout(this.timeer);
    }
    private showAniDelay(noTicketLogin = false): void {//不展示小马动画的重连 倒计时结束后展示
        GameUtil.log(!Global.isconnect+ " socket showAniDelay " + noTicketLogin);
        (!Global.isconnect || noTicketLogin) && this.createConnectView(this.param, this.preventTouch);
    }
    //在后台断连的时候创建的页面 会在切回前台之后集中回调onOpen的事件，造成回前台之后闪多个断连页面，切后台不执行打开切回前台之后判断是否需要打开
    private createConnectView(par: ReconnectParams = {}, preventTouch: boolean = true): void {
        GameUtil.log("重连 socket createConnectView:" + Global.isconnect);
        clearTimeout(this.timeer);
        if (Global.isResume) {
            if (this._reconnectUI && this._reconnectUI.node.parent && this._reconnectUI.node.active) return;

            this.addReconnectUI();
            this.connectExist = true;
            // let uiid = UIID.ReConnect;
            // let view = uiManager.getUI(uiid);
            // if (view) return;
            // UIConfig[uiid].preventTouch = preventTouch;
            // if (par.showTipBg == undefined) par.showTipBg = !preventTouch;
            // uiManager.open(uiid, par);
            // this.connectExist = true;
        }
        this.onOpen = true;
    }

    async addReconnectUI() {
        if (!this._reconnectUI) {
            let prefab = await GameNodePool.instance.reconnectUI();
            let node = instantiate(prefab);
            this._reconnectUI = node.getComponent(ReconnectUI);
        }
        if (this._reconnectUI && !this._reconnectUI.node.parent) {
            this.mainGame.insertChild(this._reconnectUI.node, this.mainGame.children.length);
        }
        this._reconnectUI?.startTime();
    }
    /**是否处于断线重连 */
    get isReconnect(): boolean {
        return this.connectExist;
    }
    set isReconnect(v) {
        this.connectExist = v;
    }
    clear() {
        GameUtil.logCatch("重连 socket Reconnect clear ");
        // EventMgr.removeEventListener(JsbEnumEvent.RESUME, this.onResume, this);
        this.closeView();
        this.onOpen = false;
        this.connectExist = false;
        this.justShowAni = false;

    }
}