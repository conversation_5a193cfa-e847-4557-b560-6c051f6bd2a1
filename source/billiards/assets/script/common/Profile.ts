import { _decorator, Component, Sprite, UITransform, Size, SpriteFrame, Label, Color } from 'cc';
import Global from '../core/data/Global';
import Utils from '../core/tools/Utils';
import { CacheManager } from '../core/res/CacheManage';
import { FileUtil } from '../core/tools/FileUtil';
import { JsbBrid } from '../core/tools/JsbBridge';
const { ccclass, property, executeInEditMode, menu } = _decorator;

@ccclass('Profile')
@executeInEditMode(true)
@menu('common/Profile')
export class Profile extends Component {
    private _userId: number = null;
    private _avatar: string = null;
    private _nickname: string = null;

    // private _spAvatar: Sprite = null;
    private _sfDefaultHead: SpriteFrame = null;
    private _cancelToken: CancelToken = null;
    // private _lblName: Label = null;
    public _headFrame: SpriteFrame = null;
    public autoMatically: boolean = false;//是否自动销毁资源 入座玩家不要自动销毁资源 弹幕或者聊天记录 观战列表里面需要勾选为true

    @property({ serializable: true })
    private _size = 72;
    @property({ step: 1 })
    private get size() {
        return this._size;
    }
    private set size(value) {
        this._size = value;
        const size = new Size(value, value);
        this.getComponent(UITransform).contentSize = this.spAvatar.getComponent(UITransform).contentSize =
            this.spAvatar.node.parent.getComponent(UITransform).contentSize = size;
    }

    @property(Sprite)
    private spAvatar = null;

    @property(Label)
    private lbName = null;

    @property({ step: 1 })
    private nameFormatLength = 16;

    // @property
    // private clickShowProfile = true;

    onLoad() {
        // this._spAvatar = this.node.getChildByPath('mask/spAvatar').getComponent(Sprite);
        // this._sfDefaultHead = this._spAvatar.spriteFrame;
        // this._lblName = this.node.getChildByName('lblName').getComponent(Label);
        // if (this.clickShowProfile) {
        //     this._spAvatar.node.on(Node.EventType.TOUCH_START, this._showProfile, this);
        // }
        this._sfDefaultHead = this.spAvatar.spriteFrame;
    }

    setInfo(url: string, name: string, userId: number = Global.userId) {
        if (url && this._avatar != url) {
            this._userId = userId;
            // this._cancelToken?.cancel();
            // Utils.releaseRemote(this.spAvatar);
            // this._cancelToken = Utils.loadRemote(this.spAvatar, url);
            
            url = url + "?imageView2/1/w/200/h/200";//裁剪正中部分，等比缩小生成200x200缩略图（原生和游戏内对url的md5存在差异，原生没有对后缀进行一起加密）
            if (this._avatar == url && this._headFrame) {
                this.setHeadSPF(this._headFrame);
            } else {
                this.setHeadSPF(this._sfDefaultHead);
                this._avatar = url;
                JsbBrid.downloadImgByUrl(url).then(data => {
                    if (data && this._avatar == data.url) {
                        let localPath = data.localPath;
                        let md5FileName = data.md5Url;
                        // console.log(localPath+"==加密的url地址文件名==" + md5FileName);
                        CacheManager.instance.loadAndCacheSpByFullPath(localPath).then(data => {
                            let spriteFrame = data?.spriteFrame;
                            this.setHeadSPF(spriteFrame);
                            // if (spriteFrame) {
                                // JsbBrid.encryptionMd5(this._avatar, (md5) => {
                                //     console.log("==加密的url地址文件名===" + md5);
                                //     let path = FileUtil.instance.getPathByUrl(md5);
                                //     if (data.filePath == path) {
                                //         this.setHeadSPF(spriteFrame);
                                //     }
                                // });
                            // }
                        }).catch((err) => {
                            console.log(err)
                        });
                    }
                }).catch(err => {
                    console.log("head downloadImgByUrl err----->>>>", err);
                });
            }
        }

        this.setName(name);
    }

    setName(name: string, format = true) {
        if (name && this._nickname != name) {
            this._nickname = name;
            this.lbName.string = format ? Utils.formatName(name, this.nameFormatLength) : name;
        }
    }

    /**设置名字颜色 */
    set lblNameColor(color: Color) {
        this.lbName.color = color;
    }

    reset(name?: string) {
        // this._cancelToken?.cancel();
        // Utils.releaseRemote(this.spAvatar);
        this.spAvatar.spriteFrame = this._sfDefaultHead;
        this._userId = this._avatar = null;
        if (name) this.setName(name, false);
    }

    // /**打开原生玩家信息面板 */
    // private _showProfile() {
    //     if (this._userId != null) {
    //         console.log('showProfile');
    //         Native.showUserProfile(this._userId);
    //     }
    // }

    private setHeadSPF(spf: SpriteFrame, isLocal = false) {
        if (!this.autoMatically) {
            this.spAvatar.spriteFrame = spf;
        } else {
            CacheManager.instance.setSpriteFrame(this.spAvatar, spf);
        }
        if (!isLocal) this._headFrame = spf;
        this.setHeadUrlSize(spf);
    }

    /**
     * 设置头像URL的大小
     * 
     * 用于根据提供的精灵帧（SpriteFrame）调整头像的大小它通过计算精灵帧的宽高比，
     * 
     * @param sp 精灵帧对象，用于计算头像大小的比例
    */
    setHeadUrlSize(sp: SpriteFrame) {
        if (!sp || !sp.width || !sp.height) {
            console.error('Invalid SpriteFrame provided');
            return;
        }

        let wh_ratio: number;
        if (sp.height === 0) {
            console.error('SpriteFrame height is zero');
            return;
        }
        wh_ratio = sp.width / sp.height;

        const contentSize = wh_ratio >= 1 ? [wh_ratio * this._size, this._size] : [this._size, this._size * wh_ratio];
        this.spAvatar.node.getComponent(UITransform).setContentSize(contentSize[0], contentSize[1]);
        // this.spAvatar.getMaterialInstance(0).setProperty('wh_ratio', wh_ratio);
    }

    onDestroy() {
        CacheManager.instance.destoryRes(this.spAvatar);
        // this._cancelToken?.cancel();
        // Utils.releaseRemote(this.spAvatar);
    }
}