import { _decorator, screen, view, game, Game, director,Sprite } from 'cc';
import Global from './core/data/Global';
import GameService from './game/data/GameService';
import { UIConf, uiManager } from './core/ui/UIManager';
import { UIView } from './core/ui/UIView';
import { GameUtil } from './core/tools/GameUtil';
import { EnumEvent, EventMgr } from './core/event/EventManager';
import AudioMgr from './core/tools/AudioMgr';
import i18n from './core/tools/i18n';
import { GameNodePool } from './game/manager/GameNodePool';
import { ReconnectCtrl } from './common/reconnect/ReconnectCtrl';
import { JsbBrid, JsbEnumEvent } from './core/tools/JsbBridge';
import Utils from './core/tools/Utils';
import { FileUtil } from './core/tools/FileUtil';
export interface UISize {
    width: number;
    height: number;
}
/**必须从1开始 */
export enum UIID {
    Match = 1,
    BilliardGame = 2,
    Result = 3,

    Confirm = 10,
    // ReConnect = 11,
    Settings = 12,
}

/**
 * isPop: 带有弹出效果（show type 选择UIAddition）
 * isDialog：为弹窗属性标签用于打开关闭管理
 * UIView: 三种打开方式：Add， full， UISingle; 
 * 全屏的进行Full
 * 弹窗进行add
 * UISingle界面暂时没有此UI需求，设置后只出现当前的界面；其它界面会被关闭
 */
export let UIConfig: { [key: number]: UIConf } = {
    [UIID.Match]: { prefab: "prefab/match/matchVs", preventTouch: false },
    [UIID.BilliardGame]: { prefab: "prefab/gameUI", preventTouch: false },
    [UIID.Result]: { prefab: "prefab/gameOver/resultUI", preventTouch: true },
    [UIID.Confirm]: { prefab: "prefab/common/confirmDialog", isPop: false, preventTouch: true, isDialog: true, preventTouchClose: true },
    // [UIID.ReConnect]: { prefab: "prefab/common/reconnectUI", preventTouch: true, isDialog: true },
    [UIID.Settings]: { prefab: "prefab/common/settingDialog", isPop: false, preventTouch: true, isDialog: true, preventTouchClose: true },
}
const { ccclass, property } = _decorator;
@ccclass('Main')
export class Main extends UIView {
    @property(Sprite)
    private imgBg: Sprite = null;
    @property(Sprite)
    private imgGameBg: Sprite = null;
    
    onLoad() {
        GameNodePool.instance.imgBg = this.imgBg;
        GameNodePool.instance.imgGameBg = this.imgGameBg;

        this.setWindowSize();
        // profiler.showStats();
        game.frameRate = 60;

        this.addEvent();
        JsbBrid.initNative();
        uiManager.initUIConf(UIConfig);
        //TODO错误日志上报
        // window.onerror = function (msg, url, line, column, detail) {
        // Native.uploadLogMsg({ errorMsg: msg });
        // }
    }

    protected start(): void {
        JsbBrid.gameLoadingProgress();
        FileUtil.instance.init();
        JsbBrid.sendToNative("currentLanguageType");
        JsbBrid.sendToNative("loadProfileInfo");

        const scene = director.getScene();
        if (scene) {
            const canvas = scene.getChildByName('Canvas');
            if (canvas) {
                ReconnectCtrl.ins.mainGame = canvas.getChildByName('MainGame');
            }
        }
        if (Utils.IsBrowser()) {
            if (!Global.urlSearch) {
                Global.urlSearch = new URLSearchParams(location.search || '');
            }

            if (!!location.search) {
                var search: Array<string> = location.search.slice(1).split("&");
                search.forEach(str => {
                    var arr = str.split("=");
                    Global.userInfo[arr[0]] = arr[1];
                    Global.gameInfo[arr[0]] = arr[1];
                    if (arr[0] == 'language') {
                        Global.isArabic = (Number(arr[1]) == 2);
                    }
                    i18n.init(Global.isArabic ? 'ar' : 'en');
                });
                Global.userId = Global.userInfo.userId;
                // Global.gameInfo.instanceId = '5f684dbd69jgtzq';
                Global.gameInfo.url = `wss://dev-tyr.yalla.games?appId=ludo&packageType=3&version=1040300&userId=${Global.userId}`;
                Global.gameState = 0;
                this.initialized();
            }
        }
    }

    addEvent() { 
        EventMgr.addEventListener(JsbEnumEvent.ONLOGIN, this.onLogin, this);
        EventMgr.addEventListener(JsbEnumEvent.KICKEDOUT, this.onKickedOut, this);
        EventMgr.addEventListener(JsbEnumEvent.HASBACKHALL, this.backHallClear, this);
        game.on(Game.EVENT_HIDE, () => {
            GameUtil.log("=切后台=");
            Global.isResume = false;
            EventMgr.raiseEvent(EnumEvent.HIDE);
            AudioMgr.playMusic(AudioMgr.audios.bg, 2);
        })
        game.on(Game.EVENT_SHOW, () => {
            GameUtil.log("=切前台=");
            Global.isResume = true;
            EventMgr.raiseEvent(EnumEvent.SHOW);
            AudioMgr.playMusic(AudioMgr.audios.bg, 1);
        })
    }
    /**原生通知游戏 msg */
    onLogin(eventName, msg) {
        if (msg) {
            if (msg.hasOwnProperty('userInfo')) {
                Global.userInfo = msg.userInfo;
                Global.userId = msg.userInfo.id;
                Global.userInfo.nickName = atob(msg.userInfo.name);
                Global.userInfo.faceUrl = msg.userInfo.avatar;
            }
            if (msg.hasOwnProperty('gameInfo')) {
                Global.gameInfo = msg.gameInfo;
                Global.gameState = msg.gameInfo.gameState || 0;
                Global.gameInfo.gameId = Global.gameId;
            }
            this.initialized();
        }
    }

    setWindowSize() {
        let visibleSize = view.getVisibleSize();
        let winsize = screen.windowSize;
        let ratio = winsize.width / winsize.height;
        let drs = view.getDesignResolutionSize();
        let drsRatio = drs.width / drs.height;
        let scale = 1;
        if (ratio > drsRatio) {
            // view.setDesignResolutionSize(750, 1334, ResolutionPolicy.FIXED_HEIGHT);
            scale = ratio / drsRatio;//winsize.height / drs.height;
            Global.screen_top_width = visibleSize.height * (ratio - drsRatio);
        } else {
            // scale = winsize.width / drs.width;
            scale = drsRatio / ratio;
            Global.screen_top_height = (winsize.height / winsize.width * drs.width) - visibleSize.height;
        }
        Global.visibleWidth = visibleSize.width;
        Global.visibleHeight = visibleSize.height;
        Global.screen_scale = scale;
        console.log(visibleSize.width + ":" + visibleSize.height + " " + drs.width + ":" + drs.height + " " + winsize.width + ":" + winsize.height + "  " + scale)
        console.log(Global.screen_top_width + "=屏幕适配=" + Global.screen_top_height + ":" + scale + "-" + ratio + ":" + drsRatio);
        console.log("height:"+(winsize.height / winsize.width * drs.width));
        if (scale > 1) {
            this.imgBg.node.setScale(scale, scale, scale);
            this.imgGameBg.node.setScale(scale, scale, scale);
        }
    }

    /**初始化完成 */
    private initialized() {
        //TODO::预加载重连ui，避免加载失败
        GameNodePool.instance.reconnectUI();
        // Global.gameState = 1;
        GameUtil.log(Global.urlSearch, "=Main=游戏状态GameState=", Global.gameState);
        let uId = Global.gameState < 1 ? UIID.Match : UIID.BilliardGame;
        uiManager.open(uId, null, null, () => {
            //打开界面同事，发起socket链接
            // Global.playerAccount.url = `ws://172.20.44.97:9091?appId=ludo&packageType=3&userId=${Global.playerAccount.idx}`;
            // console.log(Global.playerAccount.url+"===界面=====" + uId + "加载完成");
            if (uId == UIID.BilliardGame) GameService.instance.connectSocket();
            JsbBrid.removeMatchView();
        });
    }

    private onKickedOut() {
        GameService.instance.backHall(false, null, false);
        this.backHallClear('', { isQuit: false, isBackHall: false });
    }

    private backHallClear(eventName, result) {
        let [isQuit, params, isBackHall] = [result.isQuit, result.params, result.isBackHall];
        console.log("===返回大厅=="+JSON.stringify(result));
        if (isBackHall) {
            JsbBrid.backHall((isQuit || !params) ? null : params);
        }
        AudioMgr.clear();
        JsbBrid.resetAccount();
        JsbBrid.removeAllJsbView();
        GameService._instance && GameService.instance.clear();
        GameService._instance = null;
        uiManager.closeAll();
    }

    removeEvent() {
        EventMgr.removeEventListener(JsbEnumEvent.ONLOGIN, this.onLogin, this);
        EventMgr.removeEventListener(JsbEnumEvent.KICKEDOUT, this.onKickedOut, this);
        EventMgr.removeEventListener(JsbEnumEvent.HASBACKHALL, this.backHallClear, this);
    }

    public onDestroy(): void {
        this.removeEvent();
        AudioMgr.clear();
    }
}
