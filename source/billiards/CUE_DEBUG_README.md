# 球杆跟踪调试工具

## 概述

这个调试工具专门用于测试和调试球杆跟随手指旋转的逻辑，帮助解决快速转圈和近距离画圈时出现的左右晃动问题。

## 文件说明

### 1. `cue_debug.html` - 独立HTML调试器
**推荐使用** - 这是一个完整的独立调试工具，可以直接在浏览器中运行。

**特性：**
- 🎯 实时可视化球杆跟踪
- 📊 详细的参数显示面板
- 🔧 交互式调试控制
- 📝 自动日志记录和导出
- 📱 支持鼠标和触摸操作

**使用方法：**
1. 直接在浏览器中打开 `cue_debug.html`
2. 在绿色游戏区域点击并拖动鼠标/手指
3. 观察左侧调试面板的实时参数变化
4. 测试不同的移动模式：
   - 慢速移动
   - 快速移动
   - 近距离画圈
   - 远距离画圈
   - 快速转圈

### 2. `CueDebugger.ts` - Cocos Creator组件
这是集成到Cocos Creator项目中的调试组件。

**使用方法：**
1. 将 `CueDebugger.ts` 添加到场景中的节点
2. 在编辑器中配置相关节点引用
3. 运行场景进行测试

### 3. `cue_debug.scene` - 调试场景
Cocos Creator的调试场景文件。

## 调试参数说明

### 实时显示参数

- **当前角度**: 球杆当前的旋转角度（0-360°）
- **触摸距离**: 触摸点到球心的距离（像素）
- **移动速度**: 触摸移动的速度（像素/帧）
- **平滑系数**: 当前使用的平滑插值系数（0-1）
- **角度差**: 目标角度与当前角度的差值
- **快速转圈**: 是否检测到快速转圈操作

### 算法逻辑

#### 自适应阈值
```javascript
const baseThreshold = 0.5;
const distanceFactor = Math.max(0.1, Math.min(1.0, touchDistance / 200));
const speedFactor = Math.max(0.1, Math.min(1.0, 10 / (touchSpeed + 1)));
const adaptiveThreshold = baseThreshold * distanceFactor * speedFactor;
```

#### 自适应平滑系数
- **极近距离** (<80px): 响应性95-98%
- **近距离** (<150px): 根据速度动态调整
- **快速移动** (>30px/frame): 提高响应性
- **快速转圈**: 特殊优化，响应性90-95%

## 测试场景

### 1. 正常操作测试
- 慢速拖动球杆，观察是否平滑跟随
- 快速移动，检查是否有延迟

### 2. 问题场景测试
- **快速转圈**: 在球周围快速画圈，观察是否有晃动
- **近距离画圈**: 靠近球心画小圈，检查精度
- **距离变化**: 从近到远拖动，观察响应性变化

### 3. 边界条件测试
- 极小移动（测试阈值）
- 极大角度变化（测试平滑性）
- 快速方向切换（测试稳定性）

## 调试功能

### 控制按钮
- **重置**: 重置所有参数到初始状态
- **调试模式**: 开启/关闭可视化调试线
- **导出日志**: 导出详细的操作日志

### 可视化元素
- 🔴 红色触摸点：当前触摸位置
- 🔵 蓝色圆圈：触摸距离范围
- 🟢 绿色线条：球杆方向
- 🔴 红色连线：触摸点到球心的连线（调试模式）

## 性能监控

调试器会自动记录关键操作的性能数据：
- 高速移动时的参数变化
- 近距离操作的响应性
- 算法决策过程

## 问题排查

### 常见问题

1. **球杆晃动**
   - 检查平滑系数是否过低
   - 观察角度差的变化幅度
   - 确认快速转圈检测是否正常工作

2. **响应延迟**
   - 检查触摸距离和速度参数
   - 确认自适应算法是否正确触发
   - 观察平滑系数的动态变化

3. **精度不足**
   - 检查自适应阈值计算
   - 确认角度归一化是否正确
   - 观察小幅度移动的处理

### 日志分析

导出的日志包含：
- 触摸事件时间戳
- 详细的计算参数
- 算法决策过程
- 性能关键点

## 优化建议

基于调试结果，可以调整以下参数：

1. **基础阈值**: 调整 `baseThreshold` 值
2. **距离因子**: 修改距离计算公式
3. **速度因子**: 调整速度响应曲线
4. **平滑系数**: 优化不同场景下的响应性

## 集成到项目

将调试验证的参数应用到实际的 `Cue.ts` 文件中：

1. 复制验证有效的算法参数
2. 更新 `onTouchMove` 方法
3. 保留关键的调试日志
4. 进行最终测试验证

---

**注意**: 这个调试工具仅用于开发和测试，不应包含在生产版本中。