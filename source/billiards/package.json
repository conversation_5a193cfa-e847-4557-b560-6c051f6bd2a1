{"name": "ball", "uuid": "02251870-9406-4d1a-bbaa-************", "version": "3.5.0", "creator": {"version": "3.8.5"}, "scripts": {"pbjs": "pbjs -t static-module -w commonjs -o ./assets/script/libs/billiarProto.js ./proto/*.proto", "pbts": "pbts -o ./assets/script/libs/billiarProto.d.ts ./assets/script/libs/billiarProto.js"}, "devDependencies": {"@types/ws": "^8.5.3", "buffer": "^6.0.3", "gulp": "^4.0.2", "gulp-static-hash": "^0.1.4", "http": "^0.0.1-security", "jszip": "^3.10.1", "protobufjs": "^7.2.6", "protobufjs-cli": "^1.1.2", "webview-javascript-bridge": "^2.0.0", "ws": "^8.8.1"}, "dependencies": {"@types/node": "^20.14.11", "vconsole": "^3.15.1", "vconsole-outputlog-plugin": "^0.3.0"}}