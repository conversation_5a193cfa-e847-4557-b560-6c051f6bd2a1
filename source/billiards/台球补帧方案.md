# 台球切前后台补帧方案

## 🔴 问题描述

台球游戏在切前后台时需要补帧，以确保物理世界的连续性和游戏状态的一致性。

### 核心需求
1. **物理连续性**：球的运动轨迹在切前后台后保持连续
2. **状态一致性**：游戏状态与实际物理状态同步
3. **性能平衡**：补帧不能导致UI卡顿
4. **智能判断**：根据实际情况决定是否需要补帧

## ✅ 解决方案

### 1. **时间记录机制**

#### onHide方法 - 记录后台时间
```javascript
private onHide(): void {
    // 记录切后台的时间
    this.backgroundTime = GameUtil.getSystemTime();
    console.log("切后台，记录时间戳:", this.backgroundTime);
    GameUtil.log("=切后台=");
}
```

#### onShow方法 - 计算后台时长
```javascript
private onShow(): void {
    // 清理后台定时器
    clearInterval(this.backgroundTimer);
    
    // 计算后台时间
    const elapsed = (GameUtil.getSystemTime() - this.backgroundTime);
    console.log(`切前台：后台时间=${elapsed}ms`);
    
    // 智能补帧策略
    if (elapsed > 0 && elapsed < 10000) { // 10秒内进行补帧
        this._compensateFrames(elapsed);
    } else if (elapsed >= 10000) {
        // 超过10秒，直接同步状态，不补帧
        console.log("后台时间过长，跳过补帧，直接同步状态");
        this._syncGameState();
    }
}
```

### 2. **智能补帧策略**

#### 补帧决策逻辑
```javascript
private _compensateFrames(elapsed: number): void {
    // 检查是否有球在运动（简单检查）
    const hasMovingBalls = this._hasMovingBalls();
    
    if (!hasMovingBalls) {
        // 没有球在运动，无需补帧
        console.log("无球运动，跳过补帧");
        this._syncGameState();
        return;
    }
    
    // 计算需要补偿的帧数
    const frameTime = 1000 / 60; // 假设60fps
    const maxFrames = 120; // 最多补偿2秒的帧数
    const frames = Math.min(Math.floor(elapsed / frameTime), maxFrames);
    
    if (frames > 0) {
        console.log(`开始补帧：${frames}帧`);
        this._executeFrameCompensation(frames);
    } else {
        this._syncGameState();
    }
}
```

#### 运动状态检查
```javascript
private _hasMovingBalls(): boolean {
    // 简单检查：如果游戏状态表示球在运动
    return !GameData.isGameOver && GameData.billiardsStatus !== 0;
}
```

### 3. **分批补帧执行**

#### 避免UI卡顿的分批处理
```javascript
private _executeFrameCompensation(totalFrames: number): void {
    const batchSize = 10; // 每批处理10帧
    let processedFrames = 0;
    
    const processBatch = () => {
        const currentBatch = Math.min(batchSize, totalFrames - processedFrames);
        
        // 执行物理更新
        for (let i = 0; i < currentBatch; i++) {
            BallCtrl.update(Global.dt);
        }
        
        processedFrames += currentBatch;
        
        if (processedFrames < totalFrames) {
            // 下一帧继续处理，避免阻塞UI
            this.scheduleOnce(processBatch, 0);
        } else {
            console.log(`补帧完成：共处理${processedFrames}帧`);
            this._syncGameState();
        }
    };
    
    // 开始处理
    processBatch();
}
```

## 🎯 补帧策略详解

### 时间阈值设计

| 后台时间 | 处理策略 | 原因 |
|----------|----------|------|
| 0-10秒 | 智能补帧 | 短时间内，物理状态可以准确补偿 |
| 10秒以上 | 跳过补帧 | 时间过长，补帧意义不大，直接同步状态 |

### 补帧条件判断

1. **有球运动** + **短时间后台** = 执行补帧
2. **无球运动** + **任意时间** = 跳过补帧
3. **任意状态** + **长时间后台** = 跳过补帧

### 分批处理参数

```javascript
const frameTime = 1000 / 60;    // 60fps，每帧约16.67ms
const maxFrames = 120;          // 最多补偿2秒
const batchSize = 10;           // 每批10帧，避免卡顿
```

## 📊 性能优化

### 1. **帧数限制**
- 最多补偿120帧（2秒）
- 避免过度补帧导致的性能问题

### 2. **分批处理**
- 每批处理10帧
- 使用`scheduleOnce(processBatch, 0)`分帧执行
- 避免一次性计算导致的UI卡顿

### 3. **智能跳过**
- 无球运动时跳过补帧
- 长时间后台跳过补帧
- 减少不必要的计算

## 🔧 使用示例

### 典型场景1：短时间后台，有球运动
```
用户切后台2秒，此时球正在运动
→ 计算需要补偿约120帧
→ 分12批处理，每批10帧
→ 总耗时约12帧（200ms）完成补帧
→ 球的运动轨迹保持连续
```

### 典型场景2：短时间后台，无球运动
```
用户切后台3秒，此时球已停止
→ 检测到无球运动
→ 直接跳过补帧
→ 同步游戏状态
→ 瞬间完成恢复
```

### 典型场景3：长时间后台
```
用户切后台15秒
→ 超过10秒阈值
→ 直接跳过补帧
→ 同步游戏状态
→ 可能需要服务器同步
```

## 🚀 优势特点

### 1. **智能化**
- 根据实际情况决定是否补帧
- 避免不必要的计算开销

### 2. **性能友好**
- 分批处理，不阻塞UI
- 限制最大补帧数量
- 智能跳过机制

### 3. **准确性**
- 使用固定时间步长`Global.dt`
- 保持物理计算的一致性
- 确保轨迹连续性

### 4. **可调节性**
- 时间阈值可配置
- 批处理大小可调整
- 最大帧数可限制

## 📝 配置参数

### 可调整的参数
```javascript
// 时间阈值
const MAX_COMPENSATION_TIME = 10000; // 10秒

// 帧率设置
const FRAME_RATE = 60; // 60fps
const frameTime = 1000 / FRAME_RATE;

// 补帧限制
const maxFrames = 120; // 最多2秒

// 批处理大小
const batchSize = 10; // 每批10帧
```

### 性能监控
```javascript
// 添加性能监控
console.time('frameCompensation');
this._executeFrameCompensation(frames);
console.timeEnd('frameCompensation');

// 监控补帧效果
console.log(`补帧统计: 后台${elapsed}ms, 补偿${frames}帧, 耗时${compensationTime}ms`);
```

## 🔍 故障排除

### 如果补帧不生效
1. 检查`backgroundTime`是否正确记录
2. 确认`_hasMovingBalls()`判断逻辑
3. 验证`BallCtrl.update()`是否正常工作

### 如果出现卡顿
1. 减少`batchSize`（如改为5）
2. 降低`maxFrames`限制
3. 增加时间阈值检查

### 如果轨迹不连续
1. 检查`Global.dt`时间步长设置
2. 确认物理更新的准确性
3. 验证补帧帧数计算

## 📋 总结

这个补帧方案通过智能判断、分批处理和性能优化，实现了台球游戏切前后台时的平滑过渡：

1. **智能补帧**：只在需要时进行补帧
2. **性能优化**：分批处理避免卡顿
3. **准确计算**：保持物理世界的连续性
4. **灵活配置**：可根据需求调整参数

该方案既保证了游戏体验的连续性，又避免了不必要的性能开销。
