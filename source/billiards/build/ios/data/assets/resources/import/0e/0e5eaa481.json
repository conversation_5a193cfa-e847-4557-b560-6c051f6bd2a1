[1, ["fd0c36d8-11b4-4071-9acf-20a783b46e64", "c18a041a-09d6-4c0d-b15e-c37eec8af09d@f9941", "a5b1f664-6203-424c-963a-338ea221ccf0@f9941", "9f1c6d4b-dc7d-4a46-a7b2-c64a75aa9f30", "710f2af7-9a54-4e93-98cb-3db833f42d6e@f9941", "39e1240d-8073-41c2-847c-b87a3885106c"], ["node", "root", "_spriteFrame", "_font", "data", "asset", "lbName", "spAvatar", "_customMaterial"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_children", "_lpos", "_parent"], -1, 4, 9, 2, 5, 1], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos"], 1, 1, 12, 4, 5], ["cc.Label", ["_string", "_horizontalAlign", "_actualFontSize", "_fontSize", "_overflow", "_enableWrapText", "_isSystemFontUsed", "_enableOutline", "node", "__prefab", "_font"], -5, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["eae12tdJ0xDvrl1RyZv5yGz", ["node", "__prefab"], 3, 1, 4], ["cc.UIOpacity", ["node", "__prefab"], 3, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "mountedChil<PERSON>n", "propertyOverrides"], 2, 1, 9, 9], ["cc.MountedChildrenInfo", ["targetInfo", "nodes"], 3, 4, 2], ["cc.TargetInfo", ["localID"], 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["97323FPHLdFhLcK/XCsmO6e", ["nameFormatLength", "node", "__prefab", "spAvatar", "lbName"], 2, 1, 4, 1, 1]], [[6, 0, 2], [14, 0, 2], [5, 0, 1, 2, 1], [11, 0, 1, 2, 3, 4, 5, 5], [16, 0, 1, 2, 2], [15, 0, 1, 2, 3], [0, 0, 1, 8, 5, 4, 7, 3], [4, 0, 2], [1, 2, 3, 4, 1], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 9], [0, 0, 1, 6, 5, 4, 7, 3], [0, 2, 3, 8, 4, 3], [0, 0, 1, 6, 5, 4, 3], [2, 0, 1, 2, 3, 4, 3], [2, 0, 1, 2, 3, 4, 5, 3], [7, 0, 1, 1], [8, 0, 1, 1], [9, 0, 1, 2, 3, 4, 5, 4], [10, 0, 1, 2, 3, 4, 5, 4], [12, 0, 1, 2, 3, 2], [13, 0, 1, 1], [1, 0, 2, 3, 4, 2], [1, 0, 1, 2, 3, 3], [3, 0, 1, 2, 3, 4, 5, 6, 8, 9, 8], [17, 0, 1, 2, 3, 4, 2]], [[[[7, "listItem"], [10, "listItem", 33554432, [-6, -7, -8, -9, -10], [[2, -3, [0, "46ZXyLcBZGzaOdSooHuHvP"], [5, 750, 127]], [15, -4, [0, "f1S1oygc5I/4d8mejvwOdl"]], [16, -5, [0, "e9Cp5c/p1LIKExkkUneEoe"]]], [17, "d8uU8PhlBBqbA2e+9dgwgg", null, null, -2, 0, [-1]], [1, 0, 90.907, 0]], [11, 0, {}, 1, [18, "71bowyxotJ6afTnNLPFuEK", null, null, -12, [19, "2aJWFU2eJD4buX5d8CULHz", 1, [[20, [1, ["71bowyxotJ6afTnNLPFuEK"]], [-11]]], [[5, "profile", ["_name"], [1, ["71bowyxotJ6afTnNLPFuEK"]]], [4, ["_lpos"], [1, ["71bowyxotJ6afTnNLPFuEK"]], [1, -221.405, 0, 0]], [4, ["_lrot"], [1, ["71bowyxotJ6afTnNLPFuEK"]], [3, 0, 0, 0, 1]], [4, ["_euler"], [1, ["71bowyxotJ6afTnNLPFuEK"]], [1, 0, 0, 0]], [4, ["_contentSize"], [1, ["74AVQX6GtLfJungOZQSbHM"]], [5, 80, 80]], [4, ["_contentSize"], [1, ["8eJSxPcjpGkJzueDLrHL3E"]], [5, 80, 80]], [4, ["_contentSize"], [1, ["0a4vgLaGhCJIgFKQ6FYCCf"]], [5, 80, 80]], [4, ["_color"], [1, ["9fYLk6JI5HbLnq9Xl4sfDW"]], [4, 4294967295]], [5, false, ["_isBold"], [1, ["9fYLk6JI5HbLnq9Xl4sfDW"]]], [5, 28, ["_fontSize"], [1, ["9fYLk6JI5HbLnq9Xl4sfDW"]]], [5, 28, ["_actualFontSize"], [1, ["9fYLk6JI5HbLnq9Xl4sfDW"]]], [4, ["_lpos"], [1, ["f6BoldCFdFVZVJTlzTZL5z"]], [1, 155, 0, 0]], [5, 80, ["_size"], [1, ["cb/btYo1BI2aOkCWI/c/Qd"]]], [5, 11, ["nameFormatLength"], [1, ["cb/btYo1BI2aOkCWI/c/Qd"]]], [5, true, ["_enableOutline"], [1, ["9fYLk6JI5HbLnq9Xl4sfDW"]]]]], 4]], [6, "icon_win", 33554432, 1, [[2, -13, [0, "4aMziHJrdNG6eKki82h5f3"], [5, 61, 76]], [8, -14, [0, "c1SxSg1kJHoZd/O18rBOUl"], 0]], [3, "e19rPPbHtAraqJhn+9/90k", null, null, null, 1, 0], [1, -319.088, -3, 0]], [6, "coin", 33554432, 1, [[2, -15, [0, "241rW5tMNJ7ocIOtrbKa1i"], [5, 32, 32]], [21, 0, -16, [0, "a9NgnNiTRCxrwGivQd+DjZ"], 1]], [3, "a5VtdUBn9OE47+z7WKH2VR", null, null, null, 1, 0], [1, 165.423, -3.9459999999999997, 0]], [6, "lblGoalsNum", 33554432, 1, [[2, -17, [0, "7elVOpeSNKGIYvIKIgoYSW"], [5, 30, 40]], [9, "0", 0, 28, 28, 1, false, false, true, -18, [0, "a7m02LMUBHzYFjgmTq58wq"], 2]], [3, "eeboYgBbtKBb5M8Nd+oOyj", null, null, null, 1, 0], [1, 62.524, -5, 0]], [6, "lblCoinNum", 33554432, 1, [[2, -19, [0, "dd8LLivehCcLKLaOMyjxZW"], [5, 100, 40]], [9, "+0", 0, 28, 28, 1, false, false, true, -20, [0, "02EhKdAttDWalHCKmUYgBr"], 3]], [3, "bfYT9jwWtPboEvDOlSSa3I", null, null, null, 1, 0], [1, 245, -5, 0]], [6, "image_defaultFrame", 33554432, 2, [[2, -21, [0, "8f+xaA6ElDgoCvc+lSKPyi"], [5, 80, 80]], [8, -22, [0, "65CgRDAVdFgJ9zV//7/2Zj"], 5]], [3, "42QJdtzM9HcIdt3LTs+NjT", null, null, null, 1, 0], [1, -1.034, 0, 0]]], 0, [0, -1, 2, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 2, 0, -1, 7, 0, 1, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 4, 1, 22], [0, 0, 0, 0, 0, 0], [2, 2, 3, 3, 5, 2], [1, 2, 0, 0, 3, 4]], [[[7, "profile"], [12, "profile", 33554432, [-6, -7], [[2, -2, [0, "0a4vgLaGhCJIgFKQ6FYCCf"], [5, 72, 72]], [24, 13, -5, [0, "cb/btYo1BI2aOkCWI/c/Qd"], -4, -3]], [3, "71bowyxotJ6afTnNLPFuEK", null, null, null, -1, 0]], [13, "spAvatar", 33554432, 1, [[[2, -8, [0, "180udMlktHpbI9sQr+ZEyI"], [5, 72, 72]], -9], 4, 1], [3, "afgC4dKx1MEJUTiLd3MU4g", null, null, null, 1, 0]], [14, "lblName", 33554432, 1, [[[2, -10, [0, "e8E4PjnXFAdrsv6pdhuJeL"], [5, 200, 40]], -11], 4, 1], [3, "f6BoldCFdFVZVJTlzTZL5z", null, null, null, 1, 0], [1, 150, 23, 0]], [22, 0, false, 2, [0, "98jdu7htVCQ7MogbuCxB4d"]], [23, "Name", 0, 23, 23, 1, false, false, 3, [0, "9fYLk6JI5HbLnq9Xl4sfDW"]]], 0, [0, 1, 1, 0, 0, 1, 0, 6, 5, 0, 7, 4, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, -2, 4, 0, 0, 3, 0, -2, 5, 0, 4, 1, 11], [4, 5], [8, 3], [5, 0]]]]