[1, ["6e78b54d-6225-4a65-bb32-059f1e26139a@6c48a"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "skeleton", "\nskeleton.png\nsize: 1024,512\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n边框_00003\n  rotate: false\n  xy: 559, 23\n  size: 20, 23\n  orig: 400, 400\n  offset: 173, 350\n  index: -1\n边框_00004\n  rotate: false\n  xy: 989, 472\n  size: 32, 38\n  orig: 400, 400\n  offset: 172, 337\n  index: -1\n边框_00005\n  rotate: false\n  xy: 815, 195\n  size: 38, 46\n  orig: 400, 400\n  offset: 172, 329\n  index: -1\n边框_00006\n  rotate: false\n  xy: 262, 33\n  size: 46, 58\n  orig: 400, 400\n  offset: 171, 317\n  index: -1\n边框_00007\n  rotate: false\n  xy: 164, 16\n  size: 49, 68\n  orig: 400, 400\n  offset: 172, 307\n  index: -1\n边框_00008\n  rotate: false\n  xy: 57, 4\n  size: 53, 78\n  orig: 400, 400\n  offset: 172, 297\n  index: -1\n边框_00009\n  rotate: false\n  xy: 582, 51\n  size: 55, 87\n  orig: 400, 400\n  offset: 172, 288\n  index: -1\n边框_00010\n  rotate: false\n  xy: 351, 2\n  size: 56, 97\n  orig: 400, 400\n  offset: 172, 278\n  index: -1\n边框_00011\n  rotate: false\n  xy: 467, 9\n  size: 56, 107\n  orig: 400, 400\n  offset: 172, 268\n  index: -1\n边框_00012\n  rotate: false\n  xy: 931, 393\n  size: 56, 117\n  orig: 400, 400\n  offset: 172, 258\n  index: -1\n边框_00013\n  rotate: false\n  xy: 815, 243\n  size: 56, 129\n  orig: 400, 400\n  offset: 172, 246\n  index: -1\n边框_00014\n  rotate: false\n  xy: 815, 374\n  size: 56, 136\n  orig: 400, 400\n  offset: 172, 239\n  index: -1\n边框_00015\n  rotate: false\n  xy: 757, 362\n  size: 56, 148\n  orig: 400, 400\n  offset: 172, 227\n  index: -1\n边框_00016\n  rotate: false\n  xy: 699, 351\n  size: 56, 159\n  orig: 400, 400\n  offset: 172, 216\n  index: -1\n边框_00017\n  rotate: false\n  xy: 583, 160\n  size: 56, 169\n  orig: 400, 400\n  offset: 172, 206\n  index: -1\n边框_00018\n  rotate: false\n  xy: 583, 331\n  size: 56, 179\n  orig: 400, 400\n  offset: 172, 196\n  index: -1\n边框_00019\n  rotate: false\n  xy: 525, 322\n  size: 56, 188\n  orig: 400, 400\n  offset: 172, 186\n  index: -1\n边框_00020\n  rotate: false\n  xy: 409, 103\n  size: 56, 196\n  orig: 400, 400\n  offset: 172, 175\n  index: -1\n边框_00021\n  rotate: false\n  xy: 293, 96\n  size: 56, 200\n  orig: 400, 400\n  offset: 172, 167\n  index: -1\n边框_00022\n  rotate: false\n  xy: 235, 93\n  size: 56, 203\n  orig: 400, 400\n  offset: 172, 158\n  index: -1\n边框_00023\n  rotate: false\n  xy: 61, 85\n  size: 56, 210\n  orig: 400, 400\n  offset: 172, 146\n  index: -1\n边框_00024\n  rotate: false\n  xy: 351, 300\n  size: 56, 210\n  orig: 400, 400\n  offset: 172, 136\n  index: -1\n边框_00025\n  rotate: false\n  xy: 2, 298\n  size: 57, 212\n  orig: 400, 400\n  offset: 171, 125\n  index: -1\n边框_00026\n  rotate: false\n  xy: 2, 84\n  size: 57, 212\n  orig: 400, 400\n  offset: 171, 115\n  index: -1\n边框_00027\n  rotate: false\n  xy: 235, 298\n  size: 56, 212\n  orig: 400, 400\n  offset: 172, 105\n  index: -1\n边框_00028\n  rotate: false\n  xy: 293, 298\n  size: 56, 212\n  orig: 400, 400\n  offset: 172, 95\n  index: -1\n边框_00029\n  rotate: false\n  xy: 61, 297\n  size: 56, 213\n  orig: 400, 400\n  offset: 172, 84\n  index: -1\n边框_00030\n  rotate: false\n  xy: 119, 86\n  size: 56, 209\n  orig: 400, 400\n  offset: 172, 76\n  index: -1\n边框_00031\n  rotate: false\n  xy: 119, 297\n  size: 56, 213\n  orig: 400, 400\n  offset: 172, 64\n  index: -1\n边框_00032\n  rotate: false\n  xy: 177, 297\n  size: 56, 213\n  orig: 400, 400\n  offset: 172, 54\n  index: -1\n边框_00033\n  rotate: false\n  xy: 409, 301\n  size: 56, 209\n  orig: 400, 400\n  offset: 172, 46\n  index: -1\n边框_00034\n  rotate: false\n  xy: 177, 87\n  size: 56, 208\n  orig: 400, 400\n  offset: 172, 38\n  index: -1\n边框_00035\n  rotate: false\n  xy: 467, 309\n  size: 56, 201\n  orig: 400, 400\n  offset: 172, 34\n  index: -1\n边框_00036\n  rotate: false\n  xy: 351, 101\n  size: 56, 197\n  orig: 400, 400\n  offset: 172, 29\n  index: -1\n边框_00037\n  rotate: false\n  xy: 467, 118\n  size: 56, 189\n  orig: 400, 400\n  offset: 172, 27\n  index: -1\n边框_00038\n  rotate: false\n  xy: 525, 140\n  size: 56, 180\n  orig: 400, 400\n  offset: 172, 26\n  index: -1\n边框_00039\n  rotate: false\n  xy: 641, 341\n  size: 56, 169\n  orig: 400, 400\n  offset: 172, 25\n  index: -1\n边框_00040\n  rotate: false\n  xy: 641, 178\n  size: 56, 161\n  orig: 400, 400\n  offset: 172, 25\n  index: -1\n边框_00041\n  rotate: false\n  xy: 699, 199\n  size: 56, 150\n  orig: 400, 400\n  offset: 172, 25\n  index: -1\n边框_00042\n  rotate: false\n  xy: 757, 220\n  size: 56, 140\n  orig: 400, 400\n  offset: 172, 25\n  index: -1\n边框_00043\n  rotate: false\n  xy: 873, 381\n  size: 56, 129\n  orig: 400, 400\n  offset: 172, 25\n  index: -1\n边框_00044\n  rotate: false\n  xy: 873, 260\n  size: 56, 119\n  orig: 400, 400\n  offset: 172, 25\n  index: -1\n边框_00045\n  rotate: false\n  xy: 931, 282\n  size: 56, 109\n  orig: 400, 400\n  offset: 172, 25\n  index: -1\n边框_00046\n  rotate: false\n  xy: 409, 2\n  size: 56, 99\n  orig: 400, 400\n  offset: 172, 25\n  index: -1\n边框_00047\n  rotate: false\n  xy: 525, 48\n  size: 55, 90\n  orig: 400, 400\n  offset: 173, 25\n  index: -1\n边框_00048\n  rotate: false\n  xy: 2, 2\n  size: 53, 80\n  orig: 400, 400\n  offset: 175, 25\n  index: -1\n边框_00049\n  rotate: false\n  xy: 112, 13\n  size: 50, 70\n  orig: 400, 400\n  offset: 178, 25\n  index: -1\n边框_00050\n  rotate: false\n  xy: 215, 26\n  size: 45, 59\n  orig: 400, 400\n  offset: 183, 25\n  index: -1\n边框_00051\n  rotate: false\n  xy: 310, 46\n  size: 39, 48\n  orig: 400, 400\n  offset: 189, 25\n  index: -1\n边框_00052\n  rotate: false\n  xy: 525, 9\n  size: 32, 37\n  orig: 400, 400\n  offset: 196, 26\n  index: -1\n边框_00053\n  rotate: false\n  xy: 989, 443\n  size: 22, 27\n  orig: 400, 400\n  offset: 205, 26\n  index: -1\n边框_00054\n  rotate: false\n  xy: 641, 168\n  size: 7, 8\n  orig: 400, 400\n  offset: 217, 32\n  index: -1\n", ["skeleton.png"], {"skeleton": {"hash": "hMDAJ8/o3ASE8EfkzmGpDnC40aQ", "spine": "3.6.53", "width": 400, "height": 400, "images": "./边框/"}, "bones": [{"name": "root"}], "slots": [{"name": "bk", "bone": "root", "attachment": "边框_00054"}], "skins": {"default": {"bk": {"边框_00000": {"width": 400, "height": 400}, "边框_00001": {"width": 400, "height": 400}, "边框_00002": {"width": 400, "height": 400}, "边框_00003": {"width": 400, "height": 400}, "边框_00004": {"width": 400, "height": 400}, "边框_00005": {"width": 400, "height": 400}, "边框_00006": {"width": 400, "height": 400}, "边框_00007": {"width": 400, "height": 400}, "边框_00008": {"width": 400, "height": 400}, "边框_00009": {"width": 400, "height": 400}, "边框_00010": {"width": 400, "height": 400}, "边框_00011": {"width": 400, "height": 400}, "边框_00012": {"width": 400, "height": 400}, "边框_00013": {"width": 400, "height": 400}, "边框_00014": {"width": 400, "height": 400}, "边框_00015": {"width": 400, "height": 400}, "边框_00016": {"width": 400, "height": 400}, "边框_00017": {"width": 400, "height": 400}, "边框_00018": {"width": 400, "height": 400}, "边框_00019": {"width": 400, "height": 400}, "边框_00020": {"width": 400, "height": 400}, "边框_00021": {"width": 400, "height": 400}, "边框_00022": {"width": 400, "height": 400}, "边框_00023": {"width": 400, "height": 400}, "边框_00024": {"width": 400, "height": 400}, "边框_00025": {"width": 400, "height": 400}, "边框_00026": {"width": 400, "height": 400}, "边框_00027": {"width": 400, "height": 400}, "边框_00028": {"width": 400, "height": 400}, "边框_00029": {"width": 400, "height": 400}, "边框_00030": {"width": 400, "height": 400}, "边框_00031": {"width": 400, "height": 400}, "边框_00032": {"width": 400, "height": 400}, "边框_00033": {"width": 400, "height": 400}, "边框_00034": {"width": 400, "height": 400}, "边框_00035": {"width": 400, "height": 400}, "边框_00036": {"width": 400, "height": 400}, "边框_00037": {"width": 400, "height": 400}, "边框_00038": {"width": 400, "height": 400}, "边框_00039": {"width": 400, "height": 400}, "边框_00040": {"width": 400, "height": 400}, "边框_00041": {"width": 400, "height": 400}, "边框_00042": {"width": 400, "height": 400}, "边框_00043": {"width": 400, "height": 400}, "边框_00044": {"width": 400, "height": 400}, "边框_00045": {"width": 400, "height": 400}, "边框_00046": {"width": 400, "height": 400}, "边框_00047": {"width": 400, "height": 400}, "边框_00048": {"width": 400, "height": 400}, "边框_00049": {"width": 400, "height": 400}, "边框_00050": {"width": 400, "height": 400}, "边框_00051": {"width": 400, "height": 400}, "边框_00052": {"width": 400, "height": 400}, "边框_00053": {"width": 400, "height": 400}, "边框_00054": {"width": 400, "height": 400}}}}, "animations": {"animation": {"slots": {"bk": {"attachment": [{"time": 0, "name": "边框_00000"}, {"time": 0.0333, "name": "边框_00001"}, {"time": 0.0667, "name": "边框_00002"}, {"time": 0.1, "name": "边框_00003"}, {"time": 0.1333, "name": "边框_00004"}, {"time": 0.1667, "name": "边框_00005"}, {"time": 0.2, "name": "边框_00006"}, {"time": 0.2333, "name": "边框_00007"}, {"time": 0.2667, "name": "边框_00008"}, {"time": 0.3, "name": "边框_00009"}, {"time": 0.3333, "name": "边框_00010"}, {"time": 0.3667, "name": "边框_00011"}, {"time": 0.4, "name": "边框_00012"}, {"time": 0.4333, "name": "边框_00013"}, {"time": 0.4667, "name": "边框_00014"}, {"time": 0.5, "name": "边框_00015"}, {"time": 0.5333, "name": "边框_00016"}, {"time": 0.5667, "name": "边框_00017"}, {"time": 0.6, "name": "边框_00018"}, {"time": 0.6333, "name": "边框_00019"}, {"time": 0.6667, "name": "边框_00020"}, {"time": 0.7, "name": "边框_00021"}, {"time": 0.7333, "name": "边框_00022"}, {"time": 0.7667, "name": "边框_00023"}, {"time": 0.8, "name": "边框_00024"}, {"time": 0.8333, "name": "边框_00025"}, {"time": 0.8667, "name": "边框_00026"}, {"time": 0.9, "name": "边框_00027"}, {"time": 0.9333, "name": "边框_00028"}, {"time": 0.9667, "name": "边框_00029"}, {"time": 1, "name": "边框_00030"}, {"time": 1.0333, "name": "边框_00031"}, {"time": 1.0667, "name": "边框_00032"}, {"time": 1.1, "name": "边框_00033"}, {"time": 1.1333, "name": "边框_00034"}, {"time": 1.1667, "name": "边框_00035"}, {"time": 1.2, "name": "边框_00036"}, {"time": 1.2333, "name": "边框_00037"}, {"time": 1.2667, "name": "边框_00038"}, {"time": 1.3, "name": "边框_00039"}, {"time": 1.3333, "name": "边框_00040"}, {"time": 1.3667, "name": "边框_00041"}, {"time": 1.4, "name": "边框_00042"}, {"time": 1.4333, "name": "边框_00043"}, {"time": 1.4667, "name": "边框_00044"}, {"time": 1.5, "name": "边框_00045"}, {"time": 1.5333, "name": "边框_00046"}, {"time": 1.5667, "name": "边框_00047"}, {"time": 1.6, "name": "边框_00048"}, {"time": 1.6333, "name": "边框_00049"}, {"time": 1.6667, "name": "边框_00050"}, {"time": 1.7, "name": "边框_00051"}, {"time": 1.7333, "name": "边框_00052"}, {"time": 1.7667, "name": "边框_00053"}, {"time": 1.8, "name": "边框_00054"}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]