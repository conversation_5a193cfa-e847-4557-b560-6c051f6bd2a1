[1, ["1c8bb3bc-0750-4d89-be16-2a558253bcbb", "fd0c36d8-11b4-4071-9acf-20a783b46e64", "0763bc30-d6d2-4e40-9972-581e2ab82482@f9941"], ["node", "_font", "_spriteFrame", "_defaultClip", "root", "tipBg", "leftTx", "tipTx", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent", "_lpos"], 1, 9, 4, 2, 1, 5], ["cc.Node", ["_name", "_layer", "_active", "_parent", "_components", "_prefab", "_lpos"], 0, 1, 12, 4, 5], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_overflow", "node", "__prefab", "_color"], -2, 1, 4, 5], ["cc.Prefab", ["_name"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Animation", ["playOnLoad", "node", "__prefab", "_clips", "_defaultClip"], 2, 1, 4, 3, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["edf98QH2D1AUoCrtQlbxFrU", ["key", "node", "__prefab"], 2, 1, 4], ["fb145b+1HVAvqa2QHwWUEgx", ["node", "__prefab", "tipTx", "leftTx", "tipBg"], 3, 1, 4, 1, 1, 1]], [[6, 0, 2], [5, 0, 1, 2, 1], [8, 0, 1, 2, 3, 4, 5, 5], [1, 0, 1, 3, 4, 5, 6, 3], [4, 0, 2], [0, 0, 1, 4, 2, 3, 3], [0, 0, 1, 5, 2, 3, 6, 3], [1, 0, 2, 1, 3, 4, 5, 6, 4], [2, 0, 1, 2, 3, 2], [2, 1, 2, 1], [7, 0, 1, 2, 3, 4, 2], [3, 0, 1, 2, 4, 3, 5, 6, 7, 6], [3, 0, 1, 2, 3, 5, 6, 7, 5], [9, 0, 1, 2, 2], [10, 0, 1, 2, 3, 4, 1]], [[4, "reconnectUI"], [5, "reconnectUI", 33554432, [-7, -8, -9, -10], [[1, -2, [0, "6ezTT8h25HPYJJ1CueYgl8"], [5, 750, 750]], [14, -6, [0, "9dur76jrNKFoenXOF5jmEh"], -5, -4, -3]], [2, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [6, "reconnect", 33554432, 1, [[1, -11, [0, "c9Ru1R7WNEHrJ3gGT4mIUU"], [5, 64, 64]], [8, 0, -12, [0, "458o+UbilJ57GG149uRZ/x"], 0], [10, true, -13, [0, "eakn7aKy9KFL/mRgeQSjra"], [1], 2]], [2, "d28rLAIIxHcIBRMsSYZ4yU", null, null, null, 1, 0], [1, 0, 130.848, 0]], [3, "tipTx", 33554432, 1, [[[1, -14, [0, "94qyuxP19MAKLblXpu6071"], [5, 750, 40]], -15, [13, "reconnecting", -16, [0, "69qn3cVWlEyZ7fGg9TTgCA"]]], 4, 1, 4], [2, "7cXt/SKINDcqjowe2tdZSM", null, null, null, 1, 0], [1, 0, 32, 0]], [7, "tipBg", false, 33554432, 1, [[[1, -17, [0, "679lBIlApDoI2KAAWMVu9M"], [5, 498, 40]], -18], 4, 1], [2, "86oynVN2dJYYYwM8m32wBg", null, null, null, 1, 0], [1, 0, 32, 0]], [3, "leftTime", 33554432, 1, [[[1, -19, [0, "9cYeI7UnhKBrMCtKVhM9v7"], [5, 51.060546875, 50.4]], -20], 4, 1], [2, "3a+F1N1HdO1a++bTkWBVSa", null, null, null, 1, 0], [1, 0, -10, 0]], [9, 4, [0, "adh+fARdJKW5TOoh4FhPYK"]], [11, "Reconnecting...", 26, 26, 1, false, 3, [0, "81HG6SFZJK/Kg+wqwq5AVi"], [4, 4288672511]], [12, "(30)", 26, 26, false, 5, [0, "925jiUJA5FwYHhREy7PP6W"], [4, 4288672511]]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 6, 0, 6, 8, 0, 7, 7, 0, 0, 1, 0, -1, 2, 0, -2, 4, 0, -3, 3, 0, -4, 5, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, -2, 7, 0, 0, 3, 0, 0, 4, 0, -2, 6, 0, 0, 5, 0, -2, 8, 0, 8, 1, 20], [0, 0, 0, 7, 8], [2, -1, 3, 1, 1], [2, 0, 0, 1, 1]]