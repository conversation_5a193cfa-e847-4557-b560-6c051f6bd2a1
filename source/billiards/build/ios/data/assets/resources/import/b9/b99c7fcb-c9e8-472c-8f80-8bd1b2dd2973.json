[1, ["419a2325-6cd6-4a23-88f0-80f449419ccd@6c48a"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "win", "\nwin.png\nsize: 2048,1024\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nbaodian_00000\n  rotate: false\n  xy: 1, 723\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\nbaodian_00001\n  rotate: false\n  xy: 1, 422\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\nbaodian_00002\n  rotate: false\n  xy: 302, 723\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\nbaodian_00003\n  rotate: false\n  xy: 1, 121\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\nbaodian_00004\n  rotate: false\n  xy: 302, 422\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\nbaodian_00005\n  rotate: false\n  xy: 603, 723\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\nbaodian_00006\n  rotate: false\n  xy: 302, 121\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\nbaodian_00007\n  rotate: false\n  xy: 603, 422\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\nbaodian_00008\n  rotate: false\n  xy: 904, 723\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\nbaodian_00009\n  rotate: false\n  xy: 603, 121\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\nbaodian_00010\n  rotate: false\n  xy: 904, 422\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\nbaodian_00011\n  rotate: false\n  xy: 1205, 723\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\nbaodian_00012\n  rotate: false\n  xy: 904, 121\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\nbaodian_00013\n  rotate: false\n  xy: 1205, 422\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\ncaidai1\n  rotate: false\n  xy: 1506, 757\n  size: 31, 23\n  orig: 31, 23\n  offset: 0, 0\n  index: -1\ncaidai2\n  rotate: false\n  xy: 1205, 226\n  size: 37, 39\n  orig: 37, 39\n  offset: 0, 0\n  index: -1\ncaidai3\n  rotate: false\n  xy: 1355, 385\n  size: 33, 36\n  orig: 33, 36\n  offset: 0, 0\n  index: -1\ncaidai4\n  rotate: false\n  xy: 323, 90\n  size: 34, 30\n  orig: 34, 30\n  offset: 0, 0\n  index: -1\ncaidai5\n  rotate: false\n  xy: 1570, 830\n  size: 17, 37\n  orig: 17, 37\n  offset: 0, 0\n  index: -1\nglow\n  rotate: false\n  xy: 1, 23\n  size: 256, 97\n  orig: 256, 97\n  offset: 0, 0\n  index: -1\nmaishui_l1\n  rotate: false\n  xy: 1506, 868\n  size: 152, 155\n  orig: 152, 155\n  offset: 0, 0\n  index: -1\nmaishui_l2\n  rotate: false\n  xy: 1506, 781\n  size: 63, 86\n  orig: 63, 86\n  offset: 0, 0\n  index: -1\nmaishui_r1\n  rotate: false\n  xy: 1205, 266\n  size: 149, 155\n  orig: 149, 155\n  offset: 0, 0\n  index: -1\nmaishui_r2\n  rotate: false\n  xy: 258, 34\n  size: 64, 86\n  orig: 64, 86\n  offset: 0, 0\n  index: -1\npar\n  rotate: false\n  xy: 258, 1\n  size: 32, 32\n  orig: 32, 32\n  offset: 0, 0\n  index: -1\nstar\n  rotate: false\n  xy: 1659, 959\n  size: 64, 64\n  orig: 64, 64\n  offset: 0, 0\n  index: -1\n", ["win.png"], {"skeleton": {"hash": "at8bGQgT4LhSH8stHw3UE1y+IJ0", "spine": "3.6.53", "width": 1024, "height": 535.18, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "baodian", "parent": "root"}, {"name": "baodian_par", "parent": "baodian"}, {"name": "baodian_par1", "parent": "baodian_par", "rotation": -66.32, "y": 7.76, "scaleX": 0.6, "scaleY": 0.6, "color": "ff00d8ff"}, {"name": "baodian_par2", "parent": "baodian_par", "rotation": -27.84, "x": 10.58, "y": 7.76, "scaleX": 0.4, "scaleY": 0.4, "color": "ff00d8ff"}, {"name": "baodian_par3", "parent": "baodian_par", "rotation": 40.11, "x": 16.56, "y": 40.09, "scaleX": 0.406, "scaleY": 0.406, "color": "ff00d8ff"}, {"name": "baodian_par4", "parent": "baodian_par", "rotation": -54.5, "x": -20.19, "y": 19.12, "scaleX": 0.166, "scaleY": 0.166, "color": "ff00d8ff"}, {"name": "baodian_par5", "parent": "baodian_par", "rotation": -80.84, "x": -16.37, "y": 35.78, "scaleX": -0.843, "scaleY": -0.843, "color": "ff00d8ff"}, {"name": "baodian_par6", "parent": "baodian_par", "rotation": 139.68, "x": -1.18, "y": 56.75, "scaleX": 0.424, "scaleY": 0.424, "color": "ff00d8ff"}, {"name": "baodian_par7", "parent": "baodian_par", "rotation": -64.56, "x": 21.56, "y": 30.2, "scaleX": 0.769, "scaleY": 0.769, "color": "ff00d8ff"}, {"name": "baodian_par8", "parent": "baodian_par", "rotation": -61.29, "x": -31.36, "y": 7.07, "scaleX": 0.588, "scaleY": 0.588, "color": "ff00d8ff"}, {"name": "baodian_par9", "parent": "baodian_par", "rotation": -87.58, "x": 36.36, "y": 22.45, "scaleX": 0.878, "scaleY": 0.878, "color": "ff00d8ff"}, {"name": "baodian_par10", "parent": "baodian_par", "rotation": -92.56, "x": -33.32, "y": 48.72, "scaleX": 0.53, "scaleY": 0.53, "color": "ff00d8ff"}, {"name": "baodian_par11", "parent": "baodian_par", "rotation": 115.12, "x": -16.76, "y": 67.04, "scaleX": 0.922, "scaleY": 0.922, "color": "ff00d8ff"}, {"name": "baodian_par12", "parent": "baodian_par", "rotation": -75.17, "x": 22.25, "y": 68.32, "scaleX": 1.374, "scaleY": 1.374, "color": "ff00d8ff"}, {"name": "baodian_star", "parent": "baodian"}, {"name": "baodian_star1", "parent": "baodian_star", "rotation": 43.64, "x": 17.25, "y": 52.99, "scaleX": 0.35, "scaleY": 0.35, "color": "00cbffff"}, {"name": "baodian_star2", "parent": "baodian_star", "rotation": 45.78, "x": 13.27, "y": 24.04, "scaleX": 0.4, "scaleY": 0.4, "color": "00cbffff"}, {"name": "baodian_star3", "parent": "baodian_star", "rotation": -82.99, "x": -19.35, "y": 10.47, "scaleX": 0.45, "scaleY": 0.45, "color": "00cbffff"}, {"name": "baodian_star4", "parent": "baodian_star", "rotation": -76.63, "x": -33.6, "y": 39.79, "scaleX": 0.36, "scaleY": 0.36, "color": "00cbffff"}, {"name": "baodian_star5", "parent": "baodian_star", "rotation": -19.3, "x": -28.72, "y": 58.99, "scaleX": 0.5, "scaleY": 0.5, "color": "00cbffff"}, {"name": "baodian_star6", "parent": "baodian_star", "rotation": -54.4, "x": 30.82, "y": 27.87, "scaleX": 0.4, "scaleY": 0.4, "color": "00cbffff"}, {"name": "baodian_star7", "parent": "baodian_star", "rotation": -77.09, "x": -2.55, "y": 19.84, "scaleX": 0.6, "scaleY": 0.6, "color": "00cbffff"}, {"name": "baodian_star8", "parent": "baodian_star", "rotation": -35.53, "x": 7.2, "y": 46.99, "scaleX": 0.3, "scaleY": 0.3, "color": "00cbffff"}, {"name": "baodian_star9", "parent": "baodian_star", "rotation": -34.87, "x": -14.1, "y": 39.42, "scaleX": 0.3, "scaleY": 0.3, "color": "00cbffff"}, {"name": "baodian_star10", "parent": "baodian_star", "rotation": -69.77, "x": 6.52, "y": 31.02, "scaleX": 0.5, "scaleY": 0.5, "color": "00cbffff"}, {"name": "bao<PERSON>_xulie", "parent": "baodian", "y": 42.51, "scaleX": 3, "scaleY": 3}, {"name": "bone", "parent": "root", "length": 40.97, "rotation": 26.97, "x": 136.6, "y": -2.72}, {"name": "bone2", "parent": "bone", "length": 34.11, "rotation": 27.36, "x": 40.39, "y": 0.3}, {"name": "bone3", "parent": "bone2", "length": 32.76, "rotation": 29.95, "x": 34.11}, {"name": "bone4", "parent": "bone3", "length": 24.75, "rotation": 18.65, "x": 32.76}, {"name": "bone5", "parent": "bone4", "length": 25.1, "rotation": 24.68, "x": 24.75}, {"name": "bone6", "parent": "root", "length": 23.61, "rotation": 22.06, "x": 202.74, "y": 17.84}, {"name": "bone7", "parent": "bone6", "length": 17.71, "rotation": 25.23, "x": 23.61}, {"name": "bone8", "parent": "bone7", "length": 18.93, "rotation": 11.3, "x": 17.71}, {"name": "bone9", "parent": "bone8", "length": 13.56, "rotation": 17.37, "x": 18.93}, {"name": "bone10", "parent": "bone9", "length": 15.47, "rotation": 17.22, "x": 13.56}, {"name": "bone11", "parent": "root", "length": 35.58, "rotation": 161.57, "x": -128.78, "y": -0.61}, {"name": "bone12", "parent": "bone11", "length": 36.39, "rotation": -31.86, "x": 35.58}, {"name": "bone13", "parent": "bone12", "length": 27.85, "rotation": -27.79, "x": 36.39}, {"name": "bone14", "parent": "bone13", "length": 27.14, "rotation": -17.73, "x": 27.85}, {"name": "bone15", "parent": "bone14", "length": 30.1, "rotation": -26.29, "x": 27.14}, {"name": "bone16", "parent": "root", "length": 22.62, "rotation": 159.96, "x": -198.03, "y": 17.89}, {"name": "bone17", "parent": "bone16", "length": 22.48, "rotation": -28.12, "x": 22.62}, {"name": "bone18", "parent": "bone17", "length": 17.21, "rotation": -14.05, "x": 22.66, "y": 0.05}, {"name": "bone19", "parent": "bone18", "length": 13.8, "rotation": -12.93, "x": 17.21}, {"name": "bone20", "parent": "bone19", "length": 13.04, "rotation": -20.58, "x": 13.8}, {"name": "caidai", "parent": "root", "y": -35.04}, {"name": "caidai1", "parent": "caidai", "x": -224.2, "y": -278.17, "color": "00faffff"}, {"name": "caidai2", "parent": "caidai", "x": -277.82, "y": 172.28, "color": "00faffff"}, {"name": "caidai3", "parent": "caidai", "x": -81.2, "y": 142.25, "color": "00faffff"}, {"name": "caidai4", "parent": "caidai", "x": 278.45, "y": 235.91, "color": "00faffff"}, {"name": "caidai5", "parent": "caidai", "x": 313.93, "y": 67.89, "color": "00faffff"}, {"name": "glow_1", "parent": "root", "scaleX": 4, "scaleY": 4}, {"name": "par", "parent": "root"}, {"name": "par1", "parent": "par", "x": 63.41, "y": 11.09, "scaleX": 0.3, "scaleY": 0.3, "color": "ff0000ff"}, {"name": "par2", "parent": "par", "x": 5.54, "y": 62.52, "scaleX": 0.323, "scaleY": 0.323, "color": "ff0000ff"}, {"name": "par3", "parent": "par", "x": -119.59, "y": 12.13, "scaleX": 0.4, "scaleY": 0.4, "color": "ff0000ff"}, {"name": "par4", "parent": "par", "x": 6.71, "y": 23.59, "scaleX": 0.5, "scaleY": 0.5, "color": "ff0000ff"}, {"name": "par5", "parent": "par", "x": -59.7, "y": 47.73, "scaleX": 0.42, "scaleY": 0.42, "color": "ff0000ff"}, {"name": "par6", "parent": "par", "x": 58.09, "y": 44.42, "scaleX": 0.6, "scaleY": 0.6, "color": "ff0000ff"}, {"name": "par7", "parent": "par", "x": -62.27, "y": 4.1, "scaleX": 0.5, "scaleY": 0.5, "color": "ff0000ff"}, {"name": "par8", "parent": "par", "x": 134.62, "y": 43.07, "scaleX": 0.4, "scaleY": 0.4, "color": "ff0000ff"}, {"name": "par9", "parent": "par", "x": -57.27, "y": 18.38, "scaleX": 0.3, "scaleY": 0.3, "color": "ff0000ff"}, {"name": "par10", "parent": "par", "x": 33.03, "y": 65.11, "scaleX": 0.369, "scaleY": 0.369, "color": "ff0000ff"}, {"name": "par11", "parent": "par", "x": -45.17, "y": 56.26, "scaleX": 0.302, "scaleY": 0.302, "color": "ff0000ff"}, {"name": "par12", "parent": "par", "x": 113.09, "y": 41.43, "color": "ff0000ff"}, {"name": "par13", "parent": "par", "x": -117.47, "y": 52.73, "scaleX": 0.774, "scaleY": 0.774, "color": "ff0000ff"}, {"name": "par14", "parent": "par", "x": -17.9, "y": 53.02, "scaleX": 0.386, "scaleY": 0.386, "color": "ff0000ff"}, {"name": "par15", "parent": "par", "x": 17.96, "y": 80.85, "scaleX": 0.346, "scaleY": 0.346, "color": "ff0000ff"}, {"name": "par16", "parent": "par", "x": -8.04, "y": 42.95, "scaleX": 0.42, "scaleY": 0.6, "color": "ff0000ff"}, {"name": "par17", "parent": "par", "x": 22.25, "y": 14.78, "scaleX": 0.5, "scaleY": 0.5, "color": "ff0000ff"}, {"name": "par18", "parent": "par", "x": -36.26, "y": 12.23, "scaleX": 0.4, "scaleY": 0.4, "color": "ff0000ff"}, {"name": "par19", "parent": "par", "x": 114.62, "y": 10.39, "scaleX": 0.3, "scaleY": 0.3, "color": "ff0000ff"}, {"name": "par20", "parent": "par", "x": -143.17, "y": 27.07, "scaleX": 0.2, "scaleY": 0.2, "color": "ff0000ff"}, {"name": "par21", "parent": "par", "x": 24.5, "y": 55.86, "scaleX": 0.3, "scaleY": 0.3, "color": "ff0000ff"}, {"name": "par22", "parent": "par", "x": -64.84, "y": 63.01, "scaleX": 0.344, "scaleY": 0.344, "color": "ff0000ff"}, {"name": "par23", "parent": "par", "x": -9.75, "y": 95.79, "scaleX": 0.315, "scaleY": 0.315, "color": "ff0000ff"}, {"name": "par24", "parent": "par", "x": -47.19, "y": 38.72, "scaleX": 0.6, "scaleY": 0.6, "color": "ff0000ff"}, {"name": "par25", "parent": "par", "x": 84.83, "y": 9.99, "scaleX": 0.5, "scaleY": 0.5, "color": "ff0000ff"}, {"name": "par26", "parent": "par", "x": 80.75, "y": 62.23, "scaleX": 0.35, "scaleY": 0.35, "color": "ff0000ff"}, {"name": "par27", "parent": "par", "x": -31.81, "y": 30, "scaleX": 0.3, "scaleY": 0.3, "color": "ff0000ff"}, {"name": "par28", "parent": "par", "x": 42.84, "y": 35.6, "scaleX": 0.1, "scaleY": 0.1, "color": "ff0000ff"}, {"name": "par29", "parent": "par", "x": -10.22, "y": 8.28, "scaleX": 0.2, "scaleY": 0.2, "color": "ff0000ff"}, {"name": "par30", "parent": "par", "x": -135.75, "y": 79.08, "scaleX": 0.3, "scaleY": 0.3, "color": "ff0000ff"}, {"name": "par31", "parent": "par", "x": 102.49, "y": 55.3, "scaleX": 0.4, "scaleY": 0.4, "color": "ff0000ff"}, {"name": "par32", "parent": "par", "x": 63.28, "y": 35.1, "scaleX": 0.5, "scaleY": 0.5, "color": "ff0000ff"}, {"name": "par33", "parent": "par", "x": -107.1, "y": -2.04, "scaleX": 0.4, "scaleY": 0.4, "color": "ff0000ff"}, {"name": "par34", "parent": "par", "x": 106.55, "y": 24.07, "scaleX": 0.7, "scaleY": 0.7, "color": "ff0000ff"}, {"name": "par35", "parent": "par", "x": -39.65, "y": 94.5, "scaleX": 0.408, "scaleY": 0.408, "color": "ff0000ff"}, {"name": "par36", "parent": "par", "x": 57.34, "y": 62.39, "scaleX": 0.4, "scaleY": 0.4, "color": "ff0000ff"}, {"name": "par37", "parent": "par", "x": -94.11, "y": 70.22, "scaleX": 0.429, "scaleY": 0.429, "color": "ff0000ff"}, {"name": "par38", "parent": "par", "x": 27.35, "y": 41.51, "scaleX": 0.5, "scaleY": 0.5, "color": "ff0000ff"}, {"name": "par39", "parent": "par", "x": -14.03, "y": 24.84, "scaleX": 0.4, "scaleY": 0.4, "color": "ff0000ff"}, {"name": "par40", "parent": "par", "x": 37.23, "y": -13.34, "scaleX": 0.3, "scaleY": 0.3, "color": "ff0000ff"}, {"name": "star", "parent": "root"}, {"name": "star1", "parent": "star"}, {"name": "xinxilan1", "parent": "root", "y": -109.22}, {"name": "xinxilan2", "parent": "root", "y": -142.66}, {"name": "you win", "parent": "root", "y": 35.66}], "slots": [{"name": "glow", "bone": "glow_1", "attachment": "glow"}, {"name": "glow2", "bone": "glow_1", "attachment": "glow"}, {"name": "maishui_l2", "bone": "root", "attachment": "maishui_l2"}, {"name": "maishui_l1", "bone": "root", "attachment": "maishui_l1"}, {"name": "maishui_r2", "bone": "root", "attachment": "maishui_r2"}, {"name": "maishui_r1", "bone": "root", "attachment": "maishui_r1"}, {"name": "caidai1", "bone": "caidai1", "attachment": "caidai1"}, {"name": "caidai2", "bone": "caidai2", "attachment": "caidai2"}, {"name": "caidai3", "bone": "caidai3", "attachment": "caidai3"}, {"name": "caidai4", "bone": "caidai4", "attachment": "caidai4"}, {"name": "caidai5", "bone": "caidai5", "attachment": "caidai5"}, {"name": "par", "bone": "par1", "attachment": "par", "blend": "additive"}, {"name": "par2", "bone": "par2", "attachment": "par", "blend": "additive"}, {"name": "par3", "bone": "par3", "attachment": "par", "blend": "additive"}, {"name": "par4", "bone": "par4", "attachment": "par", "blend": "additive"}, {"name": "par5", "bone": "par5", "attachment": "par", "blend": "additive"}, {"name": "par6", "bone": "par6", "attachment": "par", "blend": "additive"}, {"name": "par7", "bone": "par7", "attachment": "par", "blend": "additive"}, {"name": "par8", "bone": "par8", "attachment": "par", "blend": "additive"}, {"name": "par9", "bone": "par9", "attachment": "par", "blend": "additive"}, {"name": "par10", "bone": "par10", "attachment": "par", "blend": "additive"}, {"name": "par11", "bone": "par11", "attachment": "par", "blend": "additive"}, {"name": "par12", "bone": "par12", "attachment": "par", "blend": "additive"}, {"name": "par13", "bone": "par13", "attachment": "par", "blend": "additive"}, {"name": "par14", "bone": "par14", "attachment": "par", "blend": "additive"}, {"name": "par15", "bone": "par15", "attachment": "par", "blend": "additive"}, {"name": "par16", "bone": "par16", "attachment": "par", "blend": "additive"}, {"name": "par17", "bone": "par17", "attachment": "par", "blend": "additive"}, {"name": "par18", "bone": "par18", "attachment": "par", "blend": "additive"}, {"name": "par19", "bone": "par19", "attachment": "par", "blend": "additive"}, {"name": "par20", "bone": "par20", "attachment": "par", "blend": "additive"}, {"name": "par21", "bone": "par21", "attachment": "par", "blend": "additive"}, {"name": "par22", "bone": "par22", "attachment": "par", "blend": "additive"}, {"name": "par23", "bone": "par23", "attachment": "par", "blend": "additive"}, {"name": "par24", "bone": "par24", "attachment": "par", "blend": "additive"}, {"name": "par25", "bone": "par25", "attachment": "par", "blend": "additive"}, {"name": "par26", "bone": "par26", "attachment": "par", "blend": "additive"}, {"name": "par27", "bone": "par27", "attachment": "par", "blend": "additive"}, {"name": "par28", "bone": "par28", "attachment": "par", "blend": "additive"}, {"name": "par29", "bone": "par29", "attachment": "par", "blend": "additive"}, {"name": "par30", "bone": "par30", "attachment": "par", "blend": "additive"}, {"name": "par31", "bone": "par31", "attachment": "par", "blend": "additive"}, {"name": "par32", "bone": "par32", "attachment": "par", "blend": "additive"}, {"name": "par33", "bone": "par33", "attachment": "par", "blend": "additive"}, {"name": "par34", "bone": "par34", "attachment": "par", "blend": "additive"}, {"name": "par35", "bone": "par35", "attachment": "par", "blend": "additive"}, {"name": "par36", "bone": "par36", "attachment": "par", "blend": "additive"}, {"name": "par37", "bone": "par37", "attachment": "par", "blend": "additive"}, {"name": "par38", "bone": "par38", "attachment": "par", "blend": "additive"}, {"name": "par39", "bone": "par39", "attachment": "par", "blend": "additive"}, {"name": "par40", "bone": "par40", "attachment": "par", "blend": "additive"}, {"name": "bao<PERSON>_xulie", "bone": "bao<PERSON>_xulie", "blend": "additive"}, {"name": "baodian_par1", "bone": "baodian_par1", "blend": "additive"}, {"name": "baodian_par2", "bone": "baodian_par2", "blend": "additive"}, {"name": "baodian_par3", "bone": "baodian_par3", "blend": "additive"}, {"name": "baodian_par4", "bone": "baodian_par4", "blend": "additive"}, {"name": "baodian_par5", "bone": "baodian_par5", "blend": "additive"}, {"name": "baodian_par6", "bone": "baodian_par6", "blend": "additive"}, {"name": "baodian_par7", "bone": "baodian_par7", "blend": "additive"}, {"name": "baodian_par8", "bone": "baodian_par8", "blend": "additive"}, {"name": "baodian_par9", "bone": "baodian_par9", "blend": "additive"}, {"name": "baodian_par10", "bone": "baodian_par10", "blend": "additive"}, {"name": "baodian_par11", "bone": "baodian_par11", "blend": "additive"}, {"name": "baodian_par12", "bone": "baodian_par12", "blend": "additive"}, {"name": "baodian_star1", "bone": "baodian_star1", "blend": "additive"}, {"name": "baodian_star2", "bone": "baodian_star2", "blend": "additive"}, {"name": "baodian_star3", "bone": "baodian_star3", "blend": "additive"}, {"name": "baodian_star4", "bone": "baodian_star4", "blend": "additive"}, {"name": "baodian_star5", "bone": "baodian_star5", "blend": "additive"}, {"name": "baodian_star6", "bone": "baodian_star6", "blend": "additive"}, {"name": "baodian_star7", "bone": "baodian_star7", "blend": "additive"}, {"name": "baodian_star8", "bone": "baodian_star8", "blend": "additive"}, {"name": "baodian_star9", "bone": "baodian_star9", "blend": "additive"}, {"name": "baodian_star10", "bone": "baodian_star10", "blend": "additive"}], "skins": {"default": {"baodian_par1": {"par": {"width": 32, "height": 32}}, "baodian_par10": {"par": {"width": 32, "height": 32}}, "baodian_par11": {"par": {"width": 32, "height": 32}}, "baodian_par12": {"par": {"width": 32, "height": 32}}, "baodian_par2": {"par": {"width": 32, "height": 32}}, "baodian_par3": {"par": {"width": 32, "height": 32}}, "baodian_par4": {"par": {"width": 32, "height": 32}}, "baodian_par5": {"par": {"width": 32, "height": 32}}, "baodian_par6": {"par": {"width": 32, "height": 32}}, "baodian_par7": {"par": {"width": 32, "height": 32}}, "baodian_par8": {"par": {"width": 32, "height": 32}}, "baodian_par9": {"par": {"width": 32, "height": 32}}, "baodian_star1": {"star": {"width": 64, "height": 64}}, "baodian_star10": {"star": {"width": 64, "height": 64}}, "baodian_star2": {"star": {"width": 64, "height": 64}}, "baodian_star3": {"star": {"width": 64, "height": 64}}, "baodian_star4": {"star": {"width": 64, "height": 64}}, "baodian_star5": {"star": {"width": 64, "height": 64}}, "baodian_star6": {"star": {"width": 64, "height": 64}}, "baodian_star7": {"star": {"width": 64, "height": 64}}, "baodian_star8": {"star": {"width": 64, "height": 64}}, "baodian_star9": {"star": {"width": 64, "height": 64}}, "baodian_xulie": {"baodian_00000": {"width": 300, "height": 300}, "baodian_00001": {"width": 300, "height": 300}, "baodian_00002": {"width": 300, "height": 300}, "baodian_00003": {"width": 300, "height": 300}, "baodian_00004": {"width": 300, "height": 300}, "baodian_00005": {"width": 300, "height": 300}, "baodian_00006": {"width": 300, "height": 300}, "baodian_00007": {"width": 300, "height": 300}, "baodian_00008": {"width": 300, "height": 300}, "baodian_00009": {"width": 300, "height": 300}, "baodian_00010": {"width": 300, "height": 300}, "baodian_00011": {"width": 300, "height": 300}, "baodian_00012": {"width": 300, "height": 300}, "baodian_00013": {"width": 300, "height": 300}}, "caidai1": {"caidai1": {"x": 1.34, "y": 3.72, "width": 31, "height": 23}}, "caidai2": {"caidai2": {"x": 1.36, "y": 0.91, "width": 37, "height": 39}}, "caidai3": {"caidai3": {"x": -1.53, "y": -0.58, "width": 33, "height": 36}}, "caidai4": {"caidai4": {"x": 2, "y": -1.69, "width": 34, "height": 30}}, "caidai5": {"caidai5": {"x": -1.79, "y": 0.99, "width": 17, "height": 37}}, "glow": {"glow": {"width": 256, "height": 97}}, "glow2": {"glow": {"width": 256, "height": 97}}, "maishui_l1": {"maishui_l1": {"type": "mesh", "hull": 109, "width": 152, "height": 155, "uvs": [0.80545, 0.03904, 0.83387, 0.08239, 0.84492, 0.13091, 0.87334, 0.09271, 0.91281, 0.13813, 0.91124, 0.19181, 0.88913, 0.24239, 0.87597, 0.27491, 0.91597, 0.24033, 0.9665, 0.22536, 0.96545, 0.28471, 0.9386, 0.32446, 0.9086, 0.35447, 0.88229, 0.37718, 0.87913, 0.40711, 0.92071, 0.37873, 0.97281, 0.35911, 0.98398, 0.41556, 0.95642, 0.46428, 0.91101, 0.50273, 0.86171, 0.51833, 0.84463, 0.54346, 0.86637, 0.5328, 0.8996, 0.52905, 0.9525, 0.53138, 0.99, 0.54841, 0.95526, 0.60066, 0.90908, 0.62118, 0.85539, 0.62699, 0.80289, 0.62466, 0.78355, 0.65757, 0.8325, 0.6537, 0.88105, 0.66066, 0.92751, 0.676, 0.90949, 0.71045, 0.87189, 0.74036, 0.81531, 0.7494, 0.74768, 0.74552, 0.69953, 0.72875, 0.65005, 0.75713, 0.6911, 0.75249, 0.74374, 0.76565, 0.78426, 0.79197, 0.83874, 0.8392, 0.78716, 0.86165, 0.73558, 0.857, 0.6861, 0.84358, 0.63661, 0.81295, 0.59938, 0.78753, 0.5639, 0.7958, 0.62903, 0.81496, 0.68476, 0.85186, 0.72022, 0.89232, 0.62542, 0.91503, 0.54509, 0.89728, 0.47851, 0.86322, 0.43871, 0.81283, 0.41555, 0.74541, 0.43581, 0.67799, 0.49588, 0.62477, 0.56535, 0.58361, 0.57621, 0.62335, 0.57259, 0.67515, 0.55015, 0.73406, 0.50601, 0.77451, 0.56035, 0.75278, 0.59096, 0.74384, 0.58184, 0.68763, 0.5877, 0.63717, 0.6131, 0.59182, 0.6587, 0.54711, 0.67954, 0.5931, 0.68605, 0.64548, 0.66586, 0.70871, 0.69162, 0.68421, 0.71731, 0.65825, 0.69582, 0.61354, 0.6854, 0.56564, 0.68801, 0.51007, 0.72318, 0.45833, 0.76291, 0.50241, 0.77379, 0.55596, 0.76913, 0.60773, 0.79669, 0.56434, 0.80601, 0.54835, 0.77069, 0.50191, 0.74701, 0.45243, 0.74623, 0.40408, 0.76215, 0.36602, 0.80135, 0.39914, 0.8258, 0.43606, 0.83939, 0.46841, 0.84521, 0.42807, 0.80593, 0.39717, 0.76909, 0.34504, 0.75492, 0.29813, 0.76229, 0.24342, 0.81229, 0.27491, 0.8465, 0.31826, 0.83913, 0.27284, 0.7965, 0.24136, 0.76176, 0.19233, 0.75229, 0.13658, 0.7586, 0.11749, 0.80597, 0.14639, 0.75281, 0.09684, 0.7165, 0.03852, 0.71334, 0, 0.75702, 0, 0.83966, 0.17788, 0.74097, 0.69094, 0.82637, 0.57563, 0.86597, 0.4499, 0.85762, 0.37712, 0.8758, 0.33687, 0.85539, 0.22589], "triangles": [102, 103, 104, 2, 104, 1, 1, 104, 105, 105, 0, 1, 0, 106, 108, 0, 105, 106, 106, 107, 108, 7, 8, 11, 11, 8, 10, 98, 99, 7, 10, 8, 9, 6, 7, 115, 7, 99, 115, 99, 100, 115, 100, 109, 115, 109, 101, 104, 109, 100, 101, 6, 115, 5, 115, 109, 5, 101, 102, 104, 5, 109, 4, 4, 109, 2, 109, 104, 2, 2, 3, 4, 91, 84, 85, 20, 112, 19, 20, 91, 112, 19, 112, 18, 85, 90, 91, 85, 86, 90, 91, 92, 112, 112, 14, 18, 14, 15, 18, 18, 15, 17, 86, 89, 90, 86, 87, 89, 112, 92, 14, 92, 113, 14, 92, 93, 113, 15, 16, 17, 14, 113, 13, 87, 88, 89, 98, 113, 93, 12, 13, 114, 93, 94, 98, 13, 113, 114, 113, 98, 114, 12, 114, 11, 94, 97, 98, 94, 95, 97, 11, 114, 7, 114, 98, 7, 95, 96, 97, 110, 75, 30, 75, 82, 30, 75, 76, 82, 30, 82, 29, 28, 22, 27, 82, 83, 29, 28, 29, 111, 29, 83, 111, 28, 111, 21, 28, 21, 22, 22, 23, 27, 26, 23, 24, 26, 27, 23, 82, 76, 77, 82, 77, 81, 81, 77, 78, 81, 78, 80, 26, 24, 25, 83, 84, 111, 111, 84, 21, 20, 21, 84, 91, 20, 84, 78, 79, 80, 53, 51, 52, 53, 50, 51, 53, 54, 50, 54, 49, 50, 54, 55, 49, 55, 56, 49, 43, 44, 42, 44, 45, 42, 45, 41, 42, 45, 46, 41, 46, 47, 40, 46, 40, 41, 40, 47, 39, 47, 48, 39, 56, 64, 49, 56, 57, 64, 64, 65, 49, 49, 65, 48, 65, 66, 48, 48, 66, 39, 63, 64, 58, 39, 66, 73, 38, 39, 73, 36, 110, 30, 36, 31, 35, 36, 30, 31, 38, 110, 37, 36, 37, 110, 64, 57, 58, 66, 67, 73, 35, 32, 34, 35, 31, 32, 58, 59, 63, 63, 59, 62, 73, 74, 38, 38, 74, 110, 34, 32, 33, 67, 68, 73, 73, 68, 72, 72, 69, 71, 69, 72, 68, 74, 75, 110, 62, 59, 61, 59, 60, 61, 69, 70, 71], "vertices": [1, 31, 15.4, -6.72, 1, 1, 31, 7.44, -6.05, 1, 2, 30, 26.33, -2.34, 0.44306, 31, 0.46, -2.79, 0.55694, 2, 30, 31.13, -7.88, 0.74235, 31, 2.51, -9.82, 0.25765, 2, 30, 22.93, -12.15, 0.90591, 31, -6.73, -10.28, 0.09409, 1, 30, 14.87, -10.05, 1, 1, 30, 7.99, -5.02, 1, 2, 29, 36.72, -0.72, 0.03081, 30, 3.52, -1.94, 0.96919, 2, 29, 42.66, -6.23, 0.03877, 30, 7.38, -9.07, 0.96123, 2, 29, 45.73, -13.64, 0.01039, 30, 7.93, -17.07, 0.98961, 2, 29, 36.56, -14.4, 0.16615, 30, -1.01, -14.86, 0.83385, 2, 29, 30.03, -10.95, 0.5268, 30, -6.1, -9.5, 0.4732, 2, 29, 24.94, -6.88, 0.92026, 30, -9.61, -4.02, 0.07974, 1, 29, 21.04, -3.25, 1, 1, 29, 16.38, -3.23, 1, 1, 29, 21.38, -9.08, 1, 1, 29, 25.2, -16.66, 1, 1, 29, 16.66, -19.22, 1, 1, 29, 8.73, -15.8, 1, 2, 28, 40.7, -7.2, 0.0423, 29, 2.11, -9.53, 0.9577, 2, 28, 34.36, -2.52, 0.47741, 29, -1.04, -2.31, 0.52259, 1, 28, 29.68, -2.68, 1, 1, 28, 32.95, -4.41, 1, 1, 28, 36.37, -8.17, 1, 1, 28, 40.76, -14.91, 1, 1, 28, 41.94, -21.08, 1, 1, 28, 32.28, -21.52, 1, 1, 28, 25.61, -17.67, 1, 1, 28, 20.12, -11.56, 1, 2, 27, 56.62, 3.21, 0.02669, 28, 15.76, -4.87, 0.97331, 2, 27, 51.69, 0, 0.39589, 28, 9.9, -5.45, 0.60411, 2, 27, 58.59, -2.84, 0.86253, 28, 14.73, -11.15, 0.13747, 2, 27, 64.68, -7.15, 0.97943, 28, 18.15, -17.77, 0.02057, 1, 27, 69.89, -12.47, 1, 1, 27, 65.03, -15.98, 1, 1, 27, 57.83, -17.52, 1, 1, 27, 49.53, -14.87, 1, 1, 27, 40.65, -9.67, 1, 1, 27, 35.3, -4.04, 1, 1, 27, 26.6, -4.55, 1, 1, 27, 32.49, -6.74, 1, 1, 27, 38.7, -12.18, 1, 1, 27, 42.33, -18.61, 1, 1, 27, 46.39, -28.89, 1, 1, 27, 37.83, -28.44, 1, 1, 27, 31.17, -24.24, 1, 1, 27, 25.41, -18.98, 1, 1, 27, 20.86, -11.33, 1, 1, 27, 17.6, -5.25, 1, 1, 27, 12.21, -3.95, 1, 1, 27, 19.69, -11.09, 1, 1, 27, 24.64, -20.03, 1, 1, 27, 26.6, -28.06, 1, 1, 27, 12.17, -24.66, 1, 1, 27, 2.53, -16.67, 1, 1, 27, -4.09, -7.38, 1, 1, 27, -5.94, 2.33, 1, 1, 27, -4.34, 13.24, 1, 1, 27, 3.14, 21.15, 1, 1, 27, 15.02, 24.37, 1, 1, 27, 27.33, 25.26, 1, 1, 27, 26, 19.02, 1, 1, 27, 21.87, 12.12, 1, 1, 27, 14.69, 5.53, 1, 1, 27, 5.87, 2.98, 1, 1, 27, 14.76, 2.24, 1, 1, 27, 19.53, 1.36, 1, 1, 27, 22.25, 9.76, 1, 1, 27, 26.59, 16.32, 1, 1, 27, 33.22, 20.84, 1, 1, 27, 42.54, 23.87, 1, 1, 27, 42.13, 16.08, 1, 1, 27, 39.33, 8.39, 1, 1, 27, 32.15, 1.05, 1, 2, 27, 37.36, 2.66, 0.51987, 28, -1.6, 3.49, 0.48013, 2, 28, 3.95, 2.66, 1, 29, -24.8, 17.37, 0, 2, 28, 7.67, 9.36, 1, 29, -18.23, 21.31, 0, 2, 28, 12.78, 14.97, 0.99999, 29, -11, 23.62, 1e-05, 2, 28, 20.01, 19.67, 0.99999, 29, -2.39, 24.09, 1e-05, 2, 28, 29.64, 20.01, 0.99999, 29, 6.12, 19.56, 1e-05, 2, 28, 27.61, 11.12, 0.99999, 29, -0.08, 12.88, 1e-05, 2, 28, 21.83, 4.93, 0.99999, 29, -8.17, 10.4, 1e-05, 1, 28, 14.9, 0.83, 1, 1, 28, 22.81, 1.35, 1, 2, 28, 25.65, 1.64, 0.98668, 29, -6.51, 5.65, 0.01332, 2, 28, 28.36, 10.2, 0.46603, 29, 0.12, 11.71, 0.53397, 2, 28, 32.5, 17.6, 0.12633, 29, 7.39, 16.05, 0.87367, 2, 28, 38.51, 22.06, 0.01723, 29, 14.83, 16.91, 0.98277, 1, 29, 20.95, 15.09, 1, 2, 28, 44.02, 15.7, 0.00573, 29, 16.43, 8.65, 0.99427, 2, 28, 41.54, 9.35, 0.01252, 29, 11.11, 4.39, 0.98748, 2, 28, 38.67, 4.74, 0.0394, 29, 6.32, 1.83, 0.9606, 1, 29, 12.63, 1.57, 1, 1, 29, 16.8, 7.99, 1, 1, 29, 24.29, 14.37, 1, 1, 29, 31.31, 17.23, 1, 1, 29, 39.86, 16.96, 1, 1, 29, 35.76, 8.92, 1, 2, 29, 29.59, 3.07, 0.92674, 30, -2.03, 3.93, 0.07326, 1, 30, 5.09, 3.44, 1, 2, 30, 11.29, 8.67, 0.98838, 31, -8.61, 13.49, 0.01162, 2, 30, 19.88, 12.11, 0.77909, 31, 0.63, 13.04, 0.22091, 2, 30, 28.63, 11.58, 0.54329, 31, 8.35, 8.9, 0.45671, 2, 30, 31.3, 9.98, 0.5306, 31, 10.11, 6.34, 0.4694, 2, 30, 25.32, 3.97, 0.42304, 31, 2.17, 3.37, 0.57696, 1, 31, 13.18, 5.08, 1, 1, 31, 23.71, 3.94, 1, 1, 31, 28.74, 0.67, 1, 1, 31, 24.68, -4.59, 1, 1, 30, 19.41, 0.07, 1, 1, 27, 43.57, -1.67, 1, 1, 28, 24.01, -3.34, 1, 1, 29, 9.58, -1.9, 1, 1, 29, 20.68, 0.48, 1, 2, 29, 27.16, -1.64, 0.99908, 30, -5.83, 0.23, 0.00092, 1, 30, 11.63, -0.6, 1], "edges": [128, 126, 126, 124, 124, 122, 122, 120, 120, 118, 118, 116, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 218, 208, 210, 210, 212, 212, 214, 214, 216, 216, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 112, 128, 112, 98, 96, 78, 132, 146, 146, 148, 148, 150, 76, 220, 220, 60, 58, 222, 222, 42, 40, 224, 224, 28, 196, 226, 226, 184, 14, 228, 228, 26, 218, 230, 230, 198, 150, 164, 168, 182]}}, "maishui_l2": {"maishui_l2": {"type": "mesh", "hull": 89, "width": 63, "height": 86, "uvs": [0.93643, 0.07487, 0.94215, 0.14938, 0.92786, 0.23226, 0.90672, 0.25277, 0.95643, 0.22598, 1, 0.21761, 1, 0.28668, 0.97872, 0.34654, 0.93872, 0.39333, 0.85758, 0.42305, 0.80101, 0.44021, 0.88672, 0.44273, 0.96043, 0.46575, 0.96215, 0.50635, 0.91186, 0.54403, 0.84443, 0.56203, 0.75586, 0.56579, 0.70934, 0.5624, 0.67506, 0.59296, 0.7722, 0.60887, 0.85506, 0.63608, 0.8362, 0.67919, 0.76249, 0.729, 0.68363, 0.74951, 0.58592, 0.74617, 0.50021, 0.72691, 0.44421, 0.74324, 0.51735, 0.75789, 0.58306, 0.79389, 0.64477, 0.84077, 0.64649, 0.88271, 0.53963, 0.89987, 0.43449, 0.88396, 0.34421, 0.83959, 0.29621, 0.80276, 0.22364, 0.81657, 0.16948, 0.82357, 0.24948, 0.83822, 0.32319, 0.87883, 0.37119, 0.91776, 0.41119, 0.97301, 0.3552, 0.99185, 0.2632, 0.99352, 0.17577, 0.97678, 0.10148, 0.93827, 0.03234, 0.88971, 0.07748, 0.8219, 0.07653, 0.79573, 0.0752, 0.75953, 0.09291, 0.69255, 0.15805, 0.63353, 0.21924, 0.58758, 0.30724, 0.54572, 0.35181, 0.55158, 0.34838, 0.62148, 0.32038, 0.68846, 0.27124, 0.73953, 0.23581, 0.75753, 0.31712, 0.73816, 0.38169, 0.71933, 0.36969, 0.66784, 0.36912, 0.6084, 0.38854, 0.5431, 0.43856, 0.492, 0.50313, 0.45935, 0.53228, 0.51586, 0.53742, 0.582, 0.51399, 0.65358, 0.57056, 0.61591, 0.55627, 0.56778, 0.5397, 0.50456, 0.5397, 0.4288, 0.56816, 0.37383, 0.64073, 0.31983, 0.6933, 0.37969, 0.70472, 0.43788, 0.69444, 0.4856, 0.74072, 0.42323, 0.70472, 0.34997, 0.69101, 0.26583, 0.72158, 0.20547, 0.76044, 0.16989, 0.80558, 0.21133, 0.82329, 0.2444, 0.80101, 0.18496, 0.78844, 0.12259, 0.80272, 0.05771, 0.85015, 0, 0.89243, 0, 0.17913, 0.78093, 0.37089, 0.77272, 0.46206, 0.69581, 0.57205, 0.67599, 0.62269, 0.62227, 0.62967, 0.55448, 0.68816, 0.55384, 0.73793, 0.49564], "triangles": [1, 2, 84, 0, 85, 86, 1, 85, 0, 86, 87, 0, 87, 88, 0, 1, 84, 85, 9, 78, 83, 83, 80, 82, 82, 80, 81, 83, 79, 80, 9, 3, 8, 9, 83, 3, 8, 3, 7, 83, 78, 79, 7, 3, 6, 6, 3, 4, 4, 5, 6, 3, 83, 2, 2, 83, 84, 18, 95, 17, 15, 16, 96, 16, 17, 96, 17, 95, 96, 96, 10, 15, 15, 11, 14, 15, 10, 11, 95, 94, 76, 76, 94, 70, 76, 70, 71, 76, 71, 72, 95, 76, 96, 75, 76, 73, 73, 76, 72, 13, 11, 12, 13, 14, 11, 96, 77, 10, 96, 76, 77, 75, 73, 74, 9, 10, 78, 78, 10, 77, 23, 92, 93, 93, 18, 23, 23, 18, 22, 25, 92, 24, 23, 24, 92, 22, 19, 21, 22, 18, 19, 25, 67, 92, 25, 91, 67, 67, 91, 61, 21, 19, 20, 67, 68, 92, 92, 68, 93, 91, 60, 61, 61, 62, 67, 67, 62, 66, 66, 62, 63, 65, 63, 64, 63, 65, 66, 18, 93, 95, 93, 68, 94, 68, 69, 94, 95, 93, 94, 69, 70, 94, 41, 42, 38, 38, 42, 37, 41, 39, 40, 41, 38, 39, 44, 36, 43, 42, 43, 37, 43, 36, 37, 45, 46, 44, 44, 46, 36, 32, 27, 31, 31, 29, 30, 31, 28, 29, 31, 27, 28, 33, 90, 32, 32, 26, 27, 32, 90, 26, 33, 34, 90, 36, 89, 35, 89, 36, 47, 36, 46, 47, 35, 57, 34, 35, 89, 57, 34, 58, 90, 34, 57, 58, 47, 48, 89, 50, 57, 89, 90, 59, 26, 90, 58, 59, 89, 48, 49, 89, 49, 50, 57, 50, 56, 26, 91, 25, 26, 59, 91, 51, 55, 56, 51, 56, 50, 59, 60, 91, 55, 51, 54, 54, 51, 52, 54, 52, 53], "vertices": [1, 36, 11.95, -4.44, 1, 1, 36, 5.53, -4.45, 1, 2, 35, 13.03, -3.46, 0.6492, 36, -1.54, -3.15, 0.3508, 2, 35, 10.99, -2.6, 0.96345, 36, -3.22, -1.72, 0.03655, 1, 35, 13.99, -5.08, 1, 1, 35, 15.35, -7.57, 1, 1, 35, 9.59, -9.01, 1, 2, 34, 25.68, -7.27, 0.04497, 35, 4.27, -8.96, 0.95503, 2, 34, 20.93, -7.22, 0.23909, 35, -0.25, -7.49, 0.76091, 2, 34, 16.09, -4.19, 0.86364, 35, -3.97, -3.15, 0.13636, 1, 34, 12.97, -1.92, 1, 1, 34, 15.6, -6.64, 1, 1, 34, 16.33, -11.63, 1, 1, 34, 13.41, -13.54, 1, 1, 34, 8.99, -12.53, 1, 1, 34, 5.46, -9.71, 1, 2, 33, 20.94, -4.57, 0.00597, 34, 2.27, -5.12, 0.99403, 2, 33, 19.17, -2.22, 0.1624, 34, 0.99, -2.46, 0.8376, 2, 33, 15.77, -2.42, 0.97149, 34, -2.37, -1.99, 0.02851, 1, 33, 18.92, -7.84, 1, 1, 33, 20.74, -13.26, 1, 1, 33, 17.21, -14.91, 1, 1, 33, 10.91, -14.4, 1, 1, 33, 6.24, -11.95, 1, 2, 32, 28.75, -5.57, 0.00136, 33, 2.28, -7.23, 0.99864, 2, 32, 24.37, -2, 0.35269, 33, -0.17, -2.14, 0.64731, 1, 32, 20.57, -1.98, 1, 1, 32, 24.37, -4.88, 1, 1, 32, 27.04, -9.3, 1, 1, 32, 29.13, -14.5, 1, 1, 32, 27.88, -17.88, 1, 1, 32, 21.08, -16.72, 1, 1, 32, 15.46, -12.97, 1, 1, 32, 11.62, -7.29, 1, 1, 32, 10.01, -3.22, 1, 1, 32, 5.32, -2.6, 1, 1, 32, 1.94, -1.88, 1, 1, 32, 6.13, -4.94, 1, 1, 32, 9.13, -9.92, 1, 1, 32, 10.67, -14.16, 1, 1, 32, 11.22, -19.51, 1, 1, 32, 7.34, -19.69, 1, 1, 32, 1.92, -17.64, 1, 1, 32, -2.65, -14.24, 1, 1, 32, -5.74, -9.41, 1, 1, 32, -8.21, -3.91, 1, 1, 32, -3.38, 0.43, 1, 1, 32, -2.59, 2.54, 1, 1, 32, -1.5, 5.45, 1, 1, 32, 1.7, 10.37, 1, 1, 32, 7.41, 13.54, 1, 1, 32, 12.46, 15.75, 1, 1, 32, 18.95, 17.01, 1, 1, 32, 21.37, 15.48, 1, 1, 32, 18.91, 9.99, 1, 1, 32, 15.11, 5.32, 1, 1, 32, 10.59, 2.41, 1, 1, 32, 7.94, 1.81, 1, 1, 32, 13.31, 1.43, 1, 2, 32, 17.69, 1.41, 0.97268, 33, -4.75, 3.79, 0.02732, 2, 32, 18.65, 5.79, 0.60772, 33, -2.01, 7.35, 0.39228, 2, 32, 20.54, 10.54, 0.25801, 33, 1.72, 10.85, 0.74199, 2, 32, 23.78, 15.29, 0.07201, 33, 6.68, 13.76, 0.92799, 2, 32, 28.36, 18.18, 0.00983, 33, 12.04, 14.42, 0.99017, 1, 33, 16.87, 13.34, 1, 2, 32, 33.06, 14.06, 0.00112, 33, 14.54, 8.69, 0.99888, 2, 32, 31.22, 8.67, 0.02015, 33, 10.58, 4.59, 0.97985, 2, 32, 27.54, 3.52, 0.03117, 33, 5.06, 1.5, 0.96883, 2, 33, 9.86, 1.08, 0.996, 34, -7.49, 2.6, 0.004, 2, 33, 12.29, 4.55, 0.84184, 34, -4.43, 5.53, 0.15816, 2, 33, 15.57, 9.01, 0.38016, 34, -0.33, 9.25, 0.61984, 2, 33, 20.36, 13.43, 0.08598, 34, 5.23, 12.65, 0.91402, 2, 33, 25.05, 15.31, 0.01651, 34, 10.2, 13.58, 0.98349, 1, 34, 16.55, 12.1, 1, 1, 34, 13.88, 6.59, 1, 2, 33, 26.84, 5.26, 0.00076, 34, 9.98, 3.37, 0.99924, 2, 33, 23.38, 2.95, 0.0134, 34, 6.14, 1.78, 0.9866, 2, 34, 12.24, 2.09, 0.99087, 35, -5.77, 3.99, 0.00913, 2, 34, 16.43, 7.31, 0.43898, 35, -0.2, 7.72, 0.56102, 2, 34, 22.16, 11.81, 0.04829, 35, 6.61, 10.31, 0.95171, 2, 34, 27.59, 12.88, 0.00131, 35, 12.11, 9.7, 0.99869, 1, 35, 15.67, 8.07, 1, 1, 35, 12.9, 4.45, 1, 2, 35, 10.42, 2.67, 0.95575, 36, -2.21, 3.48, 0.04425, 2, 35, 15.04, 5.28, 0.09271, 36, 2.97, 4.6, 0.90729, 1, 36, 8.37, 5.1, 1, 1, 36, 13.89, 3.89, 1, 1, 36, 18.68, 0.63, 1, 1, 36, 18.53, -2.03, 1, 1, 32, 3.88, 1.29, 1, 1, 32, 15.34, -2.59, 1, 2, 32, 23.14, 1.38, 0.55219, 33, 0.17, 1.44, 0.44781, 1, 33, 6.12, -2.49, 1, 1, 33, 11.68, -1.7, 1, 2, 33, 16.26, 1.93, 0.76919, 34, -1.04, 2.18, 0.23081, 2, 33, 18.8, -0.74, 0.22013, 34, 0.93, -0.94, 0.77987, 1, 34, 6.83, -1.01, 1], "edges": [72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 92, 72, 92, 94, 94, 96, 94, 178, 178, 114, 52, 180, 180, 68, 118, 182, 182, 134, 50, 184, 184, 186, 152, 188, 188, 136, 186, 190, 190, 192, 192, 20]}}, "maishui_r1": {"maishui_r1": {"type": "mesh", "hull": 121, "width": 149, "height": 155, "uvs": [0.26469, 0.03651, 0.24375, 0.08412, 0.20993, 0.12051, 0.17812, 0.14644, 0.22523, 0.1236, 0.22926, 0.16464, 0.21234, 0.20876, 0.17973, 0.24753, 0.13382, 0.27591, 0.1314, 0.3363, 0.14308, 0.30843, 0.17691, 0.27552, 0.21798, 0.24765, 0.22845, 0.30456, 0.21154, 0.35836, 0.17409, 0.39978, 0.13382, 0.43005, 0.14308, 0.47496, 0.15637, 0.43509, 0.18255, 0.40141, 0.21798, 0.36386, 0.23409, 0.40102, 0.23545, 0.4558, 0.21129, 0.50999, 0.17465, 0.55219, 0.21572, 0.6087, 0.20887, 0.57193, 0.21934, 0.51232, 0.258, 0.45309, 0.28659, 0.48406, 0.30552, 0.55103, 0.28901, 0.60793, 0.26001, 0.66057, 0.31599, 0.70083, 0.29988, 0.65593, 0.3023, 0.60561, 0.32364, 0.54677, 0.35988, 0.56961, 0.39451, 0.61528, 0.40069, 0.65889, 0.40177, 0.69941, 0.39307, 0.74336, 0.44325, 0.75936, 0.4877, 0.7671, 0.45652, 0.74936, 0.42964, 0.71032, 0.41172, 0.65921, 0.41709, 0.59663, 0.47742, 0.6127, 0.52342, 0.6529, 0.55627, 0.70515, 0.56224, 0.76888, 0.54541, 0.81709, 0.49467, 0.86471, 0.42058, 0.89838, 0.33481, 0.9069, 0.26957, 0.88483, 0.32353, 0.84225, 0.38836, 0.80703, 0.42984, 0.79193, 0.39118, 0.78883, 0.32514, 0.8349, 0.26071, 0.85929, 0.19637, 0.85878, 0.14402, 0.83904, 0.19758, 0.79104, 0.24711, 0.76627, 0.29946, 0.75698, 0.35704, 0.76433, 0.32281, 0.74962, 0.28577, 0.7295, 0.24389, 0.74459, 0.18469, 0.75079, 0.13396, 0.74575, 0.08201, 0.72175, 0.05261, 0.68227, 0.08684, 0.66717, 0.13275, 0.6575, 0.17422, 0.65517, 0.21749, 0.66331, 0.20058, 0.64551, 0.17763, 0.61918, 0.1426, 0.62499, 0.08823, 0.62538, 0.03548, 0.6068, 0, 0.56925, 0, 0.54246, 0.04976, 0.532, 0.1005, 0.53046, 0.13674, 0.54169, 0.11348, 0.51627, 0.05952, 0.49459, 0.01765, 0.45124, 0, 0.40982, 0, 0.3595, 0.03697, 0.36956, 0.07563, 0.38427, 0.10825, 0.41446, 0.10301, 0.39162, 0.09864, 0.36419, 0.05838, 0.33864, 0.03583, 0.31348, 0.01771, 0.27864, 0.0173, 0.23451, 0.04589, 0.23838, 0.07811, 0.25077, 0.1067, 0.27554, 0.1075, 0.25696, 0.09663, 0.24419, 0.07503, 0.21054, 0.06899, 0.17647, 0.08268, 0.13002, 0.10724, 0.08473, 0.1302, 0.12693, 0.13785, 0.17648, 0.13986, 0.13699, 0.14832, 0.09015, 0.17047, 0.05261, 0.20429, 0.01893, 0.24657, 0, 0.26228, 0], "triangles": [7, 114, 6, 114, 111, 113, 110, 111, 114, 114, 109, 110, 114, 3, 6, 6, 3, 5, 114, 115, 3, 3, 4, 5, 2, 3, 116, 3, 115, 116, 111, 112, 113, 116, 117, 2, 2, 117, 1, 0, 118, 119, 119, 120, 0, 117, 118, 1, 0, 1, 118, 15, 9, 14, 98, 99, 9, 99, 100, 9, 9, 10, 14, 14, 10, 13, 13, 10, 11, 9, 100, 106, 100, 101, 106, 8, 9, 106, 101, 105, 106, 105, 102, 104, 105, 101, 102, 11, 12, 13, 102, 103, 104, 8, 106, 107, 7, 8, 107, 7, 107, 114, 107, 108, 114, 108, 109, 114, 23, 24, 17, 24, 89, 17, 89, 90, 17, 90, 91, 17, 17, 18, 23, 23, 18, 22, 22, 19, 21, 19, 22, 18, 17, 97, 16, 17, 91, 97, 91, 92, 97, 92, 96, 97, 96, 93, 95, 96, 92, 93, 15, 16, 98, 16, 97, 98, 93, 94, 95, 19, 20, 21, 98, 9, 15, 73, 78, 72, 72, 79, 71, 72, 78, 79, 73, 74, 77, 74, 76, 77, 73, 77, 78, 71, 32, 70, 71, 79, 32, 70, 32, 33, 74, 75, 76, 32, 79, 25, 79, 80, 25, 32, 25, 31, 80, 81, 25, 84, 87, 83, 83, 89, 82, 83, 88, 89, 83, 87, 88, 82, 24, 81, 82, 89, 24, 81, 24, 25, 25, 26, 31, 84, 85, 87, 30, 31, 26, 30, 26, 27, 30, 27, 29, 27, 28, 29, 85, 86, 87, 56, 57, 55, 55, 57, 54, 54, 59, 53, 59, 43, 53, 59, 42, 43, 57, 58, 54, 54, 58, 59, 53, 43, 52, 63, 65, 62, 65, 66, 62, 61, 62, 66, 63, 64, 65, 67, 61, 66, 61, 67, 68, 52, 43, 51, 60, 61, 68, 42, 59, 41, 59, 60, 41, 60, 68, 41, 43, 50, 51, 43, 44, 50, 68, 69, 41, 70, 33, 69, 69, 33, 41, 44, 49, 50, 44, 45, 49, 41, 33, 40, 45, 48, 49, 45, 46, 48, 33, 34, 39, 33, 39, 40, 38, 34, 35, 38, 35, 37, 39, 34, 38, 48, 46, 47, 35, 36, 37], "vertices": [1, 41, 30.93, -3.95, 1, 1, 41, 23.02, -5.23, 1, 1, 41, 15.56, -3.95, 1, 3, 39, 59.19, -16.46, 0, 40, 34.86, -6.13, 0.00353, 41, 9.64, -2.08, 0.99647, 2, 39, 61.2, -24.06, 0, 41, 16.37, -6.14, 1, 3, 39, 54.86, -23.33, 0, 40, 32.83, -14, 0.03289, 41, 11.3, -10.03, 0.96711, 3, 39, 48.69, -19.45, 0, 40, 25.77, -12.18, 0.28636, 41, 4.17, -11.53, 0.71364, 3, 39, 43.81, -13.46, 0, 40, 19.3, -7.96, 0.80919, 41, -3.5, -10.61, 0.19081, 2, 40, 14.23, -1.6, 0.99722, 41, -10.87, -7.15, 0.00278, 2, 39, 31.83, -3.57, 0.01117, 40, 4.88, -2.19, 0.98883, 2, 39, 35.7, -6.17, 0.01479, 40, 9.36, -3.48, 0.98521, 1, 40, 14.94, -7.98, 1, 1, 40, 19.86, -13.63, 1, 2, 39, 33.66, -18.74, 0.04038, 40, 11.24, -16.08, 0.95962, 2, 39, 26.02, -14.55, 0.27753, 40, 2.69, -14.41, 0.72247, 2, 39, 20.89, -7.76, 0.74584, 40, -4.26, -9.51, 0.25416, 2, 39, 17.54, -0.92, 0.99749, 40, -9.54, -4.02, 0.00251, 1, 39, 10.45, -0.84, 1, 1, 39, 16.08, -4.05, 1, 1, 39, 20.39, -8.94, 1, 1, 39, 24.99, -15.31, 1, 1, 39, 18.86, -16.47, 1, 1, 39, 10.51, -14.92, 1, 2, 38, 34.57, -9.96, 0.0587, 39, 3.03, -9.66, 0.9413, 2, 38, 33.03, -1.58, 0.89393, 39, -2.24, -2.97, 0.10607, 2, 37, 54.22, -12.4, 0, 38, 22.38, -0.69, 1, 2, 37, 56.99, -17.49, 0, 38, 27.42, -3.55, 1, 2, 37, 58.43, -26.75, 0, 38, 33.53, -10.65, 1, 2, 37, 55.87, -37.28, 0, 38, 36.91, -20.95, 1, 2, 37, 50.31, -34.07, 0, 38, 30.5, -21.16, 1, 2, 37, 44.35, -25.12, 0, 38, 20.71, -16.7, 1, 2, 37, 43.9, -15.97, 0, 38, 15.5, -9.17, 1, 2, 37, 45.42, -6.86, 0, 38, 11.98, -0.63, 1, 2, 37, 35.53, -3.58, 0.53909, 38, 1.85, -3.06, 0.46091, 2, 37, 40.01, -9.42, 0.81277, 38, 8.74, -5.66, 0.18723, 2, 37, 42.13, -16.94, 0.89866, 38, 14.51, -10.92, 0.10134, 2, 37, 42, -26.6, 0.92322, 38, 19.49, -19.2, 0.07678, 2, 37, 35.76, -24.94, 0.92407, 38, 13.32, -21.09, 0.07593, 2, 37, 28.62, -19.86, 0.93706, 38, 4.58, -20.54, 0.06294, 2, 37, 25.61, -13.74, 0.95936, 38, -1.21, -16.93, 0.04064, 2, 37, 23.47, -7.83, 0.99178, 38, -6.14, -13.04, 0.00822, 1, 37, 22.55, -0.96, 1, 1, 37, 14.67, -0.97, 1, 1, 37, 8.01, -1.93, 1, 1, 37, 13.29, -3.07, 1, 1, 37, 19, -7.54, 1, 1, 37, 24.04, -14.21, 1, 1, 37, 26.35, -23.67, 1, 1, 37, 17.03, -24.15, 1, 1, 37, 8.56, -20.4, 1, 1, 37, 1.35, -14.27, 1, 1, 37, -2.61, -5.18, 1, 1, 37, -2.6, 2.71, 1, 1, 37, 2.24, 12.1, 1, 1, 37, 11.06, 20.54, 1, 1, 37, 22.77, 25.83, 1, 1, 37, 33.07, 25.66, 1, 1, 37, 27.53, 16.86, 1, 1, 37, 20.09, 8.63, 1, 1, 37, 14.97, 4.45, 1, 1, 37, 20.59, 5.82, 1, 1, 37, 27.66, 15.7, 1, 1, 37, 35.58, 22.32, 1, 1, 37, 44.7, 25.28, 1, 1, 37, 53.06, 24.85, 1, 1, 37, 47.85, 15.26, 1, 1, 37, 42.06, 9.29, 1, 1, 37, 35.11, 5.45, 1, 1, 37, 26.61, 3.82, 1, 2, 37, 32.17, 3.27, 0.98202, 38, -4.62, 0.98, 0.01798, 2, 37, 38.4, 2.06, 0.12631, 38, 1.31, 3.24, 0.87369, 1, 38, 3.5, 9.53, 1, 1, 38, 8.39, 16.93, 1, 1, 38, 13.82, 22.25, 1, 1, 38, 21.63, 25.83, 1, 1, 38, 29.13, 25.29, 1, 1, 38, 27.68, 19.87, 1, 1, 38, 24.46, 13.65, 1, 1, 38, 20.79, 8.66, 1, 1, 38, 15.7, 4.51, 1, 1, 38, 19.43, 4.69, 1, 1, 38, 24.76, 4.71, 1, 1, 38, 27.4, 9.3, 1, 2, 38, 32.53, 15.57, 0.98065, 39, -10.68, 11.97, 0.01935, 2, 38, 39.76, 19.78, 0.95407, 39, -6.24, 19.07, 0.04593, 2, 38, 47.62, 20.13, 0.94398, 39, 0.55, 23.04, 0.05602, 2, 38, 50.81, 17.48, 0.94048, 39, 4.61, 22.18, 0.05952, 2, 38, 47.32, 10.74, 0.91173, 39, 4.66, 14.59, 0.08827, 2, 38, 42.68, 4.77, 0.78819, 39, 3.34, 7.15, 0.21181, 2, 38, 37.89, 1.72, 0.47831, 39, 0.52, 2.22, 0.52169, 1, 39, 5.09, 4.8, 1, 1, 39, 10.04, 11.97, 1, 1, 39, 17.9, 16.69, 1, 1, 39, 24.73, 17.94, 1, 1, 39, 32.36, 16.33, 1, 1, 39, 29.69, 11.26, 1, 1, 39, 26.27, 6.09, 1, 1, 39, 20.69, 2.31, 1, 1, 39, 24.32, 2.34, 1, 2, 39, 28.61, 2.1, 0.35973, 40, 0.09, 2.23, 0.64027, 2, 39, 33.72, 7.15, 0.00197, 40, 3.42, 8.6, 0.99803, 1, 40, 6.96, 12.34, 1, 1, 40, 12.06, 15.57, 1, 1, 40, 18.85, 16.32, 1, 1, 40, 18.69, 12.02, 1, 1, 40, 17.27, 7.05, 1, 1, 40, 13.88, 2.43, 1, 1, 40, 16.75, 2.6, 1, 2, 40, 18.56, 4.41, 0.99346, 41, -9.65, 0.16, 0.00654, 2, 40, 23.42, 8.14, 0.83422, 41, -6.94, 5.65, 0.16578, 2, 40, 28.58, 9.57, 0.51222, 41, -2.94, 9.22, 0.48778, 2, 40, 35.95, 8.27, 0.09817, 41, 4.24, 11.32, 0.90183, 1, 41, 12.13, 11.95, 1, 2, 40, 37.15, 1.28, 0.02086, 41, 8.41, 5.58, 0.97914, 2, 40, 29.62, -0.63, 0.09738, 41, 2.51, 0.53, 0.90262, 2, 40, 35.74, -0.31, 0.0003, 41, 7.85, 3.53, 0.9997, 2, 40, 43.09, -0.83, 3e-05, 41, 14.67, 6.32, 0.99997, 2, 40, 49.22, -3.52, 1e-05, 41, 21.35, 6.62, 0.99999, 2, 40, 54.92, -8.01, 0, 41, 28.46, 5.12, 1, 1, 41, 34.29, 1.35, 1, 1, 41, 35.53, -0.63, 1], "edges": [102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 240, 238, 240, 238, 236, 236, 234, 234, 232, 232, 230, 230, 228, 228, 226, 226, 224, 224, 222, 222, 220, 220, 218, 218, 216, 216, 214, 214, 212, 212, 210, 210, 208, 208, 206, 206, 204, 204, 202, 202, 200, 200, 198, 198, 196, 196, 194, 194, 192, 192, 190, 190, 188, 186, 188, 186, 184, 184, 182, 182, 180, 180, 178, 178, 176, 176, 174, 174, 172, 170, 172, 170, 168, 168, 166, 166, 164, 164, 162, 162, 160, 160, 158, 158, 156, 156, 154, 154, 152, 152, 150, 150, 148, 148, 146, 146, 144, 144, 142, 142, 140, 140, 138, 138, 136, 136, 134, 134, 132, 132, 130, 130, 128, 128, 126, 126, 124, 124, 122, 122, 120, 120, 118, 118, 116, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102]}}, "maishui_r2": {"maishui_r2": {"type": "mesh", "hull": 85, "width": 64, "height": 86, "uvs": [0.16333, 0.00998, 0.20253, 0.07062, 0.20431, 0.13337, 0.18531, 0.20406, 0.1586, 0.27918, 0.19422, 0.2129, 0.23816, 0.16783, 0.27437, 0.21202, 0.29815, 0.27257, 0.28925, 0.35034, 0.24828, 0.42501, 0.29756, 0.48112, 0.3035, 0.42722, 0.31537, 0.36138, 0.35753, 0.31764, 0.41453, 0.36801, 0.44898, 0.42638, 0.45313, 0.49001, 0.43829, 0.55275, 0.41989, 0.61417, 0.47451, 0.64863, 0.45729, 0.58854, 0.46738, 0.51829, 0.48342, 0.4582, 0.54932, 0.49531, 0.61133, 0.55157, 0.62142, 0.61652, 0.62558, 0.66557, 0.60895, 0.72478, 0.68317, 0.7451, 0.7556, 0.76013, 0.71328, 0.74016, 0.66818, 0.69481, 0.63406, 0.62951, 0.64381, 0.54879, 0.71206, 0.55786, 0.79981, 0.58598, 0.85952, 0.68121, 0.86684, 0.78551, 0.92221, 0.8238, 0.94715, 0.89759, 0.88933, 0.94158, 0.80502, 0.98444, 0.71002, 1, 0.62927, 1, 0.57406, 0.96633, 0.62077, 0.91793, 0.67065, 0.8764, 0.74546, 0.8353, 0.82145, 0.82337, 0.76802, 0.82293, 0.70033, 0.81012, 0.62849, 0.85519, 0.54359, 0.887, 0.42009, 0.89319, 0.32628, 0.86402, 0.38862, 0.80923, 0.45345, 0.76966, 0.53955, 0.74403, 0.4867, 0.72724, 0.35905, 0.74624, 0.27179, 0.73506, 0.1756, 0.69573, 0.1572, 0.6299, 0.24685, 0.60692, 0.34304, 0.60206, 0.29791, 0.56848, 0.22192, 0.57201, 0.13202, 0.55923, 0.05068, 0.51682, 0.04236, 0.46158, 0.11064, 0.44788, 0.19614, 0.4417, 0.15458, 0.41784, 0.0869, 0.39972, 0.03364, 0.35807, 0, 0.29488, 0.01405, 0.21712, 0.06689, 0.24012, 0.12211, 0.28428, 0.0903, 0.2561, 0.06358, 0.18849, 0.06834, 0.11205, 0.08793, 0.04533, 0.13365, 0], "triangles": [81, 82, 3, 3, 82, 2, 1, 82, 83, 82, 1, 2, 83, 0, 1, 83, 84, 0, 9, 4, 8, 8, 4, 5, 74, 79, 73, 73, 79, 4, 74, 75, 79, 75, 78, 79, 75, 76, 78, 8, 5, 7, 76, 77, 78, 79, 80, 4, 4, 80, 3, 3, 80, 81, 5, 6, 7, 68, 72, 67, 67, 11, 66, 11, 72, 10, 11, 67, 72, 12, 17, 11, 12, 13, 15, 13, 14, 15, 69, 71, 68, 68, 71, 72, 16, 12, 15, 12, 16, 17, 69, 70, 71, 72, 73, 10, 10, 73, 9, 9, 73, 4, 61, 65, 60, 60, 20, 59, 60, 19, 20, 60, 65, 19, 62, 64, 61, 61, 64, 65, 58, 59, 20, 27, 58, 20, 62, 63, 64, 20, 26, 27, 26, 20, 21, 26, 21, 25, 24, 21, 22, 21, 24, 25, 19, 65, 18, 65, 66, 18, 18, 66, 17, 17, 66, 11, 22, 23, 24, 48, 42, 43, 45, 46, 44, 47, 43, 46, 43, 44, 46, 43, 47, 48, 42, 49, 41, 42, 48, 49, 41, 49, 39, 41, 39, 40, 39, 49, 38, 55, 56, 54, 54, 57, 53, 54, 56, 57, 57, 58, 53, 53, 58, 52, 58, 28, 52, 52, 29, 51, 52, 28, 29, 38, 49, 30, 51, 30, 50, 49, 50, 30, 51, 29, 30, 30, 37, 38, 30, 31, 37, 27, 28, 58, 37, 31, 36, 31, 32, 36, 32, 35, 36, 32, 33, 35, 33, 34, 35], "vertices": [1, 46, 15.97, -1.44, 1, 2, 44, 37.4, -12.89, 0, 46, 11.03, -4.45, 1, 3, 44, 32.57, -10.47, 1e-05, 45, 17.32, -6.77, 0.0153, 46, 5.68, -5.1, 0.98469, 3, 44, 27.76, -6.56, 9e-05, 45, 11.75, -4.04, 0.61951, 46, -0.5, -4.5, 0.38039, 1, 45, 5.95, -0.73, 1, 2, 45, 10.87, -4.39, 0.99955, 46, -1.19, -5.14, 0.00045, 2, 45, 13.9, -8.1, 0.99949, 46, 2.94, -7.55, 0.00051, 3, 44, 24.5, -11.29, 0.00755, 45, 9.63, -9.37, 0.99198, 46, -0.61, -10.24, 0.00047, 3, 44, 19.18, -10.2, 0.12584, 45, 4.2, -9.5, 0.87385, 46, -5.64, -12.27, 0.00032, 3, 44, 13.53, -6.58, 0.66181, 45, -2.11, -7.24, 0.3381, 46, -12.35, -12.37, 0.0001, 3, 44, 9.07, -1.27, 0.99913, 45, -7.65, -3.05, 0.00086, 46, -19, -10.4, 0, 3, 42, 43.89, -14.21, 0, 43, 25.45, -2.51, 0.05316, 44, 3.33, -1.81, 0.94684, 3, 42, 45.12, -18.69, 0, 43, 28.65, -5.88, 0.03858, 44, 7.25, -4.3, 0.96142, 2, 43, 32.36, -10.23, 0.0018, 44, 11.91, -7.62, 0.9982, 1, 44, 13.98, -11.76, 1, 3, 42, 40.19, -25.91, 0, 43, 27.71, -14.57, 0.0485, 44, 8.45, -12.96, 0.9515, 3, 42, 36.4, -21.95, 0, 43, 22.5, -12.87, 0.2349, 44, 2.98, -12.57, 0.7651, 3, 42, 34.27, -16.9, 0, 43, 18.24, -9.41, 0.58067, 44, -1.99, -10.26, 0.41933, 3, 42, 33.31, -11.51, 0, 43, 14.86, -5.11, 0.9242, 44, -6.32, -6.9, 0.0758, 2, 42, 32.61, -6.14, 0, 43, 11.71, -0.71, 1, 2, 42, 28.31, -4.55, 0.00769, 43, 7.17, -1.33, 0.99231, 2, 42, 31.12, -9.03, 0.00099, 43, 11.75, -3.96, 0.99901, 1, 43, 15.82, -8.47, 1, 1, 43, 18.99, -12.68, 1, 2, 42, 28.33, -18.58, 0.00241, 43, 13.8, -13.7, 0.99759, 2, 42, 22.95, -15.4, 0.04043, 43, 7.54, -13.42, 0.95957, 2, 42, 20.43, -10.37, 0.17183, 43, 2.95, -10.18, 0.82817, 2, 42, 18.73, -6.5, 0.41437, 43, -0.37, -7.56, 0.58563, 2, 42, 17.98, -1.35, 0.94894, 43, -3.45, -3.37, 0.05106, 1, 42, 12.92, -1.33, 1, 1, 42, 8.13, -1.71, 1, 1, 42, 11.26, -2.39, 1, 1, 42, 15.31, -5.07, 1, 1, 42, 19.28, -9.6, 1, 1, 42, 21.07, -16.33, 1, 1, 42, 16.7, -17.1, 1, 1, 42, 10.6, -16.75, 1, 1, 42, 4.2, -10.36, 1, 1, 42, 0.69, -2.1, 1, 1, 42, -3.77, -0.22, 1, 1, 42, -7.44, 5.2, 1, 1, 42, -5.26, 10.02, 1, 1, 42, -1.46, 15.33, 1, 1, 42, 3.8, 18.67, 1, 1, 42, 8.65, 20.44, 1, 1, 42, 12.97, 18.93, 1, 1, 42, 11.58, 14, 1, 1, 42, 9.81, 9.55, 1, 1, 42, 6.52, 4.59, 1, 1, 42, 2.3, 1.96, 1, 1, 42, 5.53, 3.09, 1, 1, 42, 9.98, 3.54, 1, 1, 42, 12.97, 8.76, 1, 1, 42, 17.13, 13.19, 1, 1, 42, 24.38, 16.4, 1, 1, 42, 30.88, 16.1, 1, 1, 42, 28.74, 10.31, 1, 1, 42, 26.01, 5.69, 1, 2, 42, 21.59, 1.73, 0.91572, 43, -1.72, 1.04, 0.08428, 2, 42, 25.26, 1.53, 0.04608, 43, 1.61, 2.6, 0.95392, 1, 43, 5.84, 9.77, 1, 1, 43, 10.29, 13.29, 1, 1, 43, 16.91, 15.62, 1, 1, 43, 21.92, 12.72, 1, 1, 43, 19.56, 7.13, 1, 1, 43, 15.76, 2.26, 1, 2, 43, 19.84, 2.49, 0.90628, 44, -3.33, 1.68, 0.09372, 2, 43, 22.86, 6.31, 0.25985, 44, -1.33, 6.12, 0.74015, 2, 43, 27.52, 9.87, 0.03187, 44, 2.33, 10.7, 0.96813, 1, 44, 7.98, 13.6, 1, 1, 44, 12.43, 11.86, 1, 1, 44, 11.44, 7.44, 1, 1, 44, 9.36, 2.35, 1, 2, 44, 12.41, 3.75, 0.94882, 45, -5.51, 2.58, 0.05118, 2, 44, 15.81, 6.86, 0.52425, 45, -2.9, 6.37, 0.47575, 2, 44, 20.57, 8.2, 0.15207, 45, 1.44, 8.75, 0.84793, 2, 44, 26.38, 7.57, 0.00422, 45, 7.25, 9.43, 0.99578, 2, 44, 31.88, 3.66, 0, 45, 13.48, 6.85, 1, 1, 45, 10.7, 4.09, 1, 1, 45, 6.12, 1.64, 1, 2, 45, 8.99, 2.99, 0.99367, 46, -5.55, 1.11, 0.00633, 2, 45, 15.04, 3.15, 0.35347, 46, 0.06, 3.39, 0.64653, 1, 46, 6.63, 3.74, 1, 1, 46, 12.47, 3.06, 1, 1, 46, 16.64, 0.54, 1], "edges": [76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 168, 168, 166, 166, 164, 164, 162, 162, 160, 160, 158, 158, 156, 156, 154, 154, 152, 152, 150, 150, 148, 148, 146, 146, 144, 144, 142, 142, 140, 140, 138, 138, 136, 136, 134, 134, 132, 132, 130, 130, 128, 128, 126, 126, 124, 124, 122, 122, 120, 120, 118, 118, 116, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 90, 88, 86, 88, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76]}}, "par": {"par": {"width": 32, "height": 32}}, "par10": {"par": {"width": 32, "height": 32}}, "par11": {"par": {"width": 32, "height": 32}}, "par12": {"par": {"width": 32, "height": 32}}, "par13": {"par": {"width": 32, "height": 32}}, "par14": {"par": {"width": 32, "height": 32}}, "par15": {"par": {"width": 32, "height": 32}}, "par16": {"par": {"width": 32, "height": 32}}, "par17": {"par": {"width": 32, "height": 32}}, "par18": {"par": {"width": 32, "height": 32}}, "par19": {"par": {"width": 32, "height": 32}}, "par2": {"par": {"width": 32, "height": 32}}, "par20": {"par": {"width": 32, "height": 32}}, "par21": {"par": {"width": 32, "height": 32}}, "par22": {"par": {"width": 32, "height": 32}}, "par23": {"par": {"width": 32, "height": 32}}, "par24": {"par": {"width": 32, "height": 32}}, "par25": {"par": {"width": 32, "height": 32}}, "par26": {"par": {"width": 32, "height": 32}}, "par27": {"par": {"width": 32, "height": 32}}, "par28": {"par": {"width": 32, "height": 32}}, "par29": {"par": {"width": 32, "height": 32}}, "par3": {"par": {"width": 32, "height": 32}}, "par30": {"par": {"width": 32, "height": 32}}, "par31": {"par": {"width": 32, "height": 32}}, "par32": {"par": {"width": 32, "height": 32}}, "par33": {"par": {"width": 32, "height": 32}}, "par34": {"par": {"width": 32, "height": 32}}, "par35": {"par": {"width": 32, "height": 32}}, "par36": {"par": {"width": 32, "height": 32}}, "par37": {"par": {"width": 32, "height": 32}}, "par38": {"par": {"width": 32, "height": 32}}, "par39": {"par": {"width": 32, "height": 32}}, "par4": {"par": {"width": 32, "height": 32}}, "par40": {"par": {"width": 32, "height": 32}}, "par5": {"par": {"width": 32, "height": 32}}, "par6": {"par": {"width": 32, "height": 32}}, "par7": {"par": {"width": 32, "height": 32}}, "par8": {"par": {"width": 32, "height": 32}}, "par9": {"par": {"width": 32, "height": 32}}}}, "animations": {"idle_down": {"slots": {"baodian_par1": {"attachment": [{"time": 0, "name": null}]}, "baodian_par2": {"attachment": [{"time": 0, "name": null}]}, "baodian_par3": {"attachment": [{"time": 0, "name": null}]}, "baodian_par4": {"attachment": [{"time": 0, "name": null}]}, "baodian_par5": {"attachment": [{"time": 0, "name": null}]}, "baodian_par6": {"attachment": [{"time": 0, "name": null}]}, "baodian_par7": {"attachment": [{"time": 0, "name": null}]}, "baodian_par8": {"attachment": [{"time": 0, "name": null}]}, "baodian_par9": {"attachment": [{"time": 0, "name": null}]}, "baodian_par10": {"attachment": [{"time": 0, "name": null}]}, "baodian_par11": {"attachment": [{"time": 0, "name": null}]}, "baodian_par12": {"attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": null}]}, "baodian_star1": {"attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": null}]}, "baodian_star2": {"attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": null}]}, "baodian_star3": {"attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": null}]}, "baodian_star4": {"attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": null}]}, "baodian_star5": {"attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": null}]}, "baodian_star6": {"attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": null}]}, "baodian_star7": {"attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": null}]}, "baodian_star8": {"attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": null}]}, "baodian_star9": {"attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": null}]}, "baodian_star10": {"attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": null}]}, "caidai1": {"color": [{"time": 1.1667, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}]}, "caidai2": {"color": [{"time": 0.9, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}]}, "caidai3": {"color": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}]}, "caidai4": {"color": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}]}, "caidai5": {"color": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}]}, "par": {"attachment": [{"time": 0, "name": null}]}, "par2": {"attachment": [{"time": 0, "name": null}]}, "par3": {"attachment": [{"time": 0, "name": null}]}, "par4": {"attachment": [{"time": 0, "name": null}]}, "par5": {"attachment": [{"time": 0, "name": null}]}, "par6": {"attachment": [{"time": 0, "name": null}]}, "par7": {"attachment": [{"time": 0, "name": null}]}, "par8": {"attachment": [{"time": 0, "name": null}]}, "par9": {"attachment": [{"time": 0, "name": null}]}, "par10": {"attachment": [{"time": 0, "name": null}]}, "par11": {"attachment": [{"time": 0, "name": null}]}, "par12": {"attachment": [{"time": 0, "name": null}]}, "par13": {"attachment": [{"time": 0, "name": null}]}, "par14": {"attachment": [{"time": 0, "name": null}]}, "par15": {"attachment": [{"time": 0, "name": null}]}, "par16": {"attachment": [{"time": 0, "name": null}]}, "par17": {"attachment": [{"time": 0, "name": null}]}, "par18": {"attachment": [{"time": 0, "name": null}]}, "par19": {"attachment": [{"time": 0, "name": null}]}, "par20": {"attachment": [{"time": 0, "name": null}]}, "par21": {"attachment": [{"time": 0, "name": null}]}, "par22": {"attachment": [{"time": 0, "name": null}]}, "par23": {"attachment": [{"time": 0, "name": null}]}, "par24": {"attachment": [{"time": 0, "name": null}]}, "par25": {"attachment": [{"time": 0, "name": null}]}, "par26": {"attachment": [{"time": 0, "name": null}]}, "par27": {"attachment": [{"time": 0, "name": null}]}, "par28": {"attachment": [{"time": 0, "name": null}]}, "par29": {"attachment": [{"time": 0, "name": null}]}, "par30": {"attachment": [{"time": 0, "name": null}]}, "par31": {"attachment": [{"time": 0, "name": null}]}, "par32": {"attachment": [{"time": 0, "name": null}]}, "par33": {"attachment": [{"time": 0, "name": null}]}, "par34": {"attachment": [{"time": 0, "name": null}]}, "par35": {"attachment": [{"time": 0, "name": null}]}, "par36": {"attachment": [{"time": 0, "name": null}]}, "par37": {"attachment": [{"time": 0, "name": null}]}, "par38": {"attachment": [{"time": 0, "name": null}]}, "par39": {"attachment": [{"time": 0, "name": null}]}, "par40": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"par1": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.3333, "x": 53.26, "y": -34.64}, {"time": 2, "x": 0, "y": 0}, {"time": 3.3333, "x": 53.26, "y": -34.64}, {"time": 4, "x": 0, "y": 0}, {"time": 5.3333, "x": 53.26, "y": -34.64}, {"time": 6, "x": 0, "y": 0}, {"time": 7.3333, "x": 53.26, "y": -34.64}, {"time": 8, "x": 0, "y": 0}, {"time": 9.3333, "x": 53.26, "y": -34.64}, {"time": 10, "x": 0, "y": 0}, {"time": 11.3333, "x": 53.26, "y": -34.64}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.3333, "x": 0, "y": 0}]}, "par2": {"translate": [{"time": 0.0667, "x": 0, "y": 0}, {"time": 1.4, "x": -8.23, "y": 45.9}, {"time": 2, "x": -0.82, "y": 4.59}, {"time": 2.0667, "x": 0, "y": 0}, {"time": 3.4, "x": -8.23, "y": 45.9}, {"time": 4, "x": -0.82, "y": 4.59}, {"time": 4.0667, "x": 0, "y": 0}, {"time": 5.4, "x": -8.23, "y": 45.9}, {"time": 6, "x": -0.82, "y": 4.59}, {"time": 6.0667, "x": 0, "y": 0}, {"time": 7.4, "x": -8.23, "y": 45.9}, {"time": 8, "x": -0.82, "y": 4.59}, {"time": 8.0667, "x": 0, "y": 0}, {"time": 9.4, "x": -8.23, "y": 45.9}, {"time": 10, "x": -0.82, "y": 4.59}, {"time": 10.0667, "x": 0, "y": 0}, {"time": 11.4, "x": -8.23, "y": 45.9}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.4, "x": 0, "y": 0}]}, "par3": {"translate": [{"time": 0.1333, "x": 18.39, "y": 27.05}, {"time": 1.4667, "x": -101.1, "y": 72.17}, {"time": 2, "x": -5.5, "y": 36.07}, {"time": 2.1333, "x": 18.39, "y": 27.05}, {"time": 3.4667, "x": -101.1, "y": 72.17}, {"time": 4, "x": -5.5, "y": 36.07}, {"time": 4.1333, "x": 18.39, "y": 27.05}, {"time": 5.4667, "x": -101.1, "y": 72.17}, {"time": 6, "x": -5.5, "y": 36.07}, {"time": 6.1333, "x": 18.39, "y": 27.05}, {"time": 7.4667, "x": -101.1, "y": 72.17}, {"time": 8, "x": -5.5, "y": 36.07}, {"time": 8.1333, "x": 18.39, "y": 27.05}, {"time": 9.4667, "x": -101.1, "y": 72.17}, {"time": 10, "x": -5.5, "y": 36.07}, {"time": 10.1333, "x": 18.39, "y": 27.05}, {"time": 11.4667, "x": -101.1, "y": 72.17}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.4667, "x": 0, "y": 0}]}, "par4": {"translate": [{"time": 0.2, "x": 10.62, "y": 24.96}, {"time": 1.5333, "x": 62.12, "y": 94.63}, {"time": 2, "x": 26.07, "y": 45.86}, {"time": 2.2, "x": 10.62, "y": 24.96}, {"time": 3.5333, "x": 62.12, "y": 94.63}, {"time": 4, "x": 26.07, "y": 45.86}, {"time": 4.2, "x": 10.62, "y": 24.96}, {"time": 5.5333, "x": 62.12, "y": 94.63}, {"time": 6, "x": 26.07, "y": 45.86}, {"time": 6.2, "x": 10.62, "y": 24.96}, {"time": 7.5333, "x": 62.12, "y": 94.63}, {"time": 8, "x": 26.07, "y": 45.86}, {"time": 8.2, "x": 10.62, "y": 24.96}, {"time": 9.5333, "x": 62.12, "y": 94.63}, {"time": 10, "x": 26.07, "y": 45.86}, {"time": 10.2, "x": 10.62, "y": 24.96}, {"time": 11.5333, "x": 62.12, "y": 94.63}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.8667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.5333, "x": 0, "y": 0}]}, "par5": {"translate": [{"time": 0.2667, "x": 0, "y": 0}, {"time": 1.6, "x": -88.33, "y": 15.59}, {"time": 2, "x": -35.33, "y": 6.24}, {"time": 2.2667, "x": 0, "y": 0}, {"time": 3.6, "x": -88.33, "y": 15.59}, {"time": 4, "x": -35.33, "y": 6.24}, {"time": 4.2667, "x": 0, "y": 0}, {"time": 5.6, "x": -88.33, "y": 15.59}, {"time": 6, "x": -35.33, "y": 6.24}, {"time": 6.2667, "x": 0, "y": 0}, {"time": 7.6, "x": -88.33, "y": 15.59}, {"time": 8, "x": -35.33, "y": 6.24}, {"time": 8.2667, "x": 0, "y": 0}, {"time": 9.6, "x": -88.33, "y": 15.59}, {"time": 10, "x": -35.33, "y": 6.24}, {"time": 10.2667, "x": 0, "y": 0}, {"time": 11.6, "x": -88.33, "y": 15.59}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.6, "x": 0, "y": 0}]}, "par6": {"translate": [{"time": 0.3333, "x": 0, "y": 0}, {"time": 1.6667, "x": 54.12, "y": 15.15}, {"time": 2, "x": 27.06, "y": 7.58}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 3.6667, "x": 54.12, "y": 15.15}, {"time": 4, "x": 27.06, "y": 7.58}, {"time": 4.3333, "x": 0, "y": 0}, {"time": 5.6667, "x": 54.12, "y": 15.15}, {"time": 6, "x": 27.06, "y": 7.58}, {"time": 6.3333, "x": 0, "y": 0}, {"time": 7.6667, "x": 54.12, "y": 15.15}, {"time": 8, "x": 27.06, "y": 7.58}, {"time": 8.3333, "x": 0, "y": 0}, {"time": 9.6667, "x": 54.12, "y": 15.15}, {"time": 10, "x": 27.06, "y": 7.58}, {"time": 10.3333, "x": 0, "y": 0}, {"time": 11.6667, "x": 54.12, "y": 15.15}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.6667, "x": 0, "y": 0}]}, "par7": {"translate": [{"time": 0.4, "x": 5.78, "y": 66.47}, {"time": 1.7333, "x": -26.01, "y": 128.89}, {"time": 2, "x": -13.29, "y": 103.92}, {"time": 2.4, "x": 5.78, "y": 66.47}, {"time": 3.7333, "x": -26.01, "y": 128.89}, {"time": 4, "x": -13.29, "y": 103.92}, {"time": 4.4, "x": 5.78, "y": 66.47}, {"time": 5.7333, "x": -26.01, "y": 128.89}, {"time": 6, "x": -13.29, "y": 103.92}, {"time": 6.4, "x": 5.78, "y": 66.47}, {"time": 7.7333, "x": -26.01, "y": 128.89}, {"time": 8, "x": -13.29, "y": 103.92}, {"time": 8.4, "x": 5.78, "y": 66.47}, {"time": 9.7333, "x": -26.01, "y": 128.89}, {"time": 10, "x": -13.29, "y": 103.92}, {"time": 10.4, "x": 5.78, "y": 66.47}, {"time": 11.7333, "x": -26.01, "y": 128.89}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.7333, "x": 0, "y": 0}]}, "par8": {"translate": [{"time": 0.4667, "x": 0, "y": 0}, {"time": 1.8, "x": 38.54, "y": 0.43}, {"time": 2, "x": 26.98, "y": 0.3}, {"time": 2.4667, "x": 0, "y": 0}, {"time": 3.8, "x": 38.54, "y": 0.43}, {"time": 4, "x": 26.98, "y": 0.3}, {"time": 4.4667, "x": 0, "y": 0}, {"time": 5.8, "x": 38.54, "y": 0.43}, {"time": 6, "x": 26.98, "y": 0.3}, {"time": 6.4667, "x": 0, "y": 0}, {"time": 7.8, "x": 38.54, "y": 0.43}, {"time": 8, "x": 26.98, "y": 0.3}, {"time": 8.4667, "x": 0, "y": 0}, {"time": 9.8, "x": 38.54, "y": 0.43}, {"time": 10, "x": 26.98, "y": 0.3}, {"time": 10.4667, "x": 0, "y": 0}, {"time": 11.8, "x": 38.54, "y": 0.43}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.8, "x": 0, "y": 0}]}, "par9": {"translate": [{"time": 0.5333, "x": 78.15, "y": 25.87}, {"time": 2, "x": 174, "y": 85.13}, {"time": 2.5333, "x": 78.15, "y": 25.87}, {"time": 4, "x": 174, "y": 85.13}, {"time": 4.5333, "x": 78.15, "y": 25.87}, {"time": 6, "x": 174, "y": 85.13}, {"time": 6.5333, "x": 78.15, "y": 25.87}, {"time": 8, "x": 174, "y": 85.13}, {"time": 8.5333, "x": 78.15, "y": 25.87}, {"time": 10, "x": 174, "y": 85.13}, {"time": 10.5333, "x": 78.15, "y": 25.87}, {"time": 12, "x": 174, "y": 85.13}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 12, "x": 0, "y": 0}]}, "par10": {"translate": [{"time": 0.6, "x": 0, "y": 0}, {"time": 1.9333, "x": 20.35, "y": 28.58}, {"time": 2, "x": 18.32, "y": 25.72}, {"time": 2.6, "x": 0, "y": 0}, {"time": 3.9333, "x": 20.35, "y": 28.58}, {"time": 4, "x": 18.32, "y": 25.72}, {"time": 4.6, "x": 0, "y": 0}, {"time": 5.9333, "x": 20.35, "y": 28.58}, {"time": 6, "x": 18.32, "y": 25.72}, {"time": 6.6, "x": 0, "y": 0}, {"time": 7.9333, "x": 20.35, "y": 28.58}, {"time": 8, "x": 18.32, "y": 25.72}, {"time": 8.6, "x": 0, "y": 0}, {"time": 9.9333, "x": 20.35, "y": 28.58}, {"time": 10, "x": 18.32, "y": 25.72}, {"time": 10.6, "x": 0, "y": 0}, {"time": 11.9333, "x": 20.35, "y": 28.58}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.2667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.9333, "x": 0, "y": 0}]}, "par11": {"translate": [{"time": 0.6667, "x": 0, "y": 0}, {"time": 2, "x": -37.67, "y": 53.69}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 4, "x": -37.67, "y": 53.69}, {"time": 4.6667, "x": 0, "y": 0}, {"time": 6, "x": -37.67, "y": 53.69}, {"time": 6.6667, "x": 0, "y": 0}, {"time": 8, "x": -37.67, "y": 53.69}, {"time": 8.6667, "x": 0, "y": 0}, {"time": 10, "x": -37.67, "y": 53.69}, {"time": 10.6667, "x": 0, "y": 0}, {"time": 12, "x": -37.67, "y": 53.69}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 12, "x": 0, "y": 0}]}, "par12": {"translate": [{"time": 0, "x": 78.57, "y": 12.75}, {"time": 0.0667, "x": 82.7, "y": 13.42}, {"time": 0.7333, "x": 0, "y": 0}, {"time": 2, "x": 78.57, "y": 12.75}, {"time": 2.0667, "x": 82.7, "y": 13.42}, {"time": 2.7333, "x": 0, "y": 0}, {"time": 4, "x": 78.57, "y": 12.75}, {"time": 4.0667, "x": 82.7, "y": 13.42}, {"time": 4.7333, "x": 0, "y": 0}, {"time": 6, "x": 78.57, "y": 12.75}, {"time": 6.0667, "x": 82.7, "y": 13.42}, {"time": 6.7333, "x": 0, "y": 0}, {"time": 8, "x": 78.57, "y": 12.75}, {"time": 8.0667, "x": 82.7, "y": 13.42}, {"time": 8.7333, "x": 0, "y": 0}, {"time": 10, "x": 78.57, "y": 12.75}, {"time": 10.0667, "x": 82.7, "y": 13.42}, {"time": 10.7333, "x": 0, "y": 0}, {"time": 12, "x": 78.57, "y": 12.75}], "scale": [{"time": 0, "x": 0.043, "y": 0.043, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 1, "y": 1, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 2, "x": 0.043, "y": 0.043, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 2.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4, "x": 1, "y": 1, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 4, "x": 0.043, "y": 0.043, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 4.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4, "x": 1, "y": 1, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 6, "x": 0.043, "y": 0.043, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 6.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4, "x": 1, "y": 1, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 8, "x": 0.043, "y": 0.043, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 8.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4, "x": 1, "y": 1, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 10, "x": 0.043, "y": 0.043, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 10.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.4, "x": 1, "y": 1, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 12, "x": 0.043, "y": 0.043}]}, "par13": {"translate": [{"time": 0, "x": -72.09, "y": 10.13}, {"time": 0.1333, "x": -80.1, "y": 11.26}, {"time": 0.8, "x": 0, "y": 0}, {"time": 2, "x": -72.09, "y": 10.13}, {"time": 2.1333, "x": -80.1, "y": 11.26}, {"time": 2.8, "x": 0, "y": 0}, {"time": 4, "x": -72.09, "y": 10.13}, {"time": 4.1333, "x": -80.1, "y": 11.26}, {"time": 4.8, "x": 0, "y": 0}, {"time": 6, "x": -72.09, "y": 10.13}, {"time": 6.1333, "x": -80.1, "y": 11.26}, {"time": 6.8, "x": 0, "y": 0}, {"time": 8, "x": -72.09, "y": 10.13}, {"time": 8.1333, "x": -80.1, "y": 11.26}, {"time": 8.8, "x": 0, "y": 0}, {"time": 10, "x": -72.09, "y": 10.13}, {"time": 10.1333, "x": -80.1, "y": 11.26}, {"time": 10.8, "x": 0, "y": 0}, {"time": 12, "x": -72.09, "y": 10.13}], "scale": [{"time": 0, "x": 0.13, "y": 0.13, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 0.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "x": 1, "y": 1, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 2, "x": 0.13, "y": 0.13, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4667, "x": 1, "y": 1, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 4, "x": 0.13, "y": 0.13, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 4.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4667, "x": 1, "y": 1, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 6, "x": 0.13, "y": 0.13, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 6.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4667, "x": 1, "y": 1, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 8, "x": 0.13, "y": 0.13, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 8.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4667, "x": 1, "y": 1, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 10, "x": 0.13, "y": 0.13, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 10.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.4667, "x": 1, "y": 1, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 12, "x": 0.13, "y": 0.13}]}, "par14": {"translate": [{"time": 0, "x": -22.82, "y": 24.66}, {"time": 0.2, "x": -26.85, "y": 29.01}, {"time": 0.8667, "x": 0, "y": 0}, {"time": 2, "x": -22.82, "y": 24.66}, {"time": 2.2, "x": -26.85, "y": 29.01}, {"time": 2.8667, "x": 0, "y": 0}, {"time": 4, "x": -22.82, "y": 24.66}, {"time": 4.2, "x": -26.85, "y": 29.01}, {"time": 4.8667, "x": 0, "y": 0}, {"time": 6, "x": -22.82, "y": 24.66}, {"time": 6.2, "x": -26.85, "y": 29.01}, {"time": 6.8667, "x": 0, "y": 0}, {"time": 8, "x": -22.82, "y": 24.66}, {"time": 8.2, "x": -26.85, "y": 29.01}, {"time": 8.8667, "x": 0, "y": 0}, {"time": 10, "x": -22.82, "y": 24.66}, {"time": 10.2, "x": -26.85, "y": 29.01}, {"time": 10.8667, "x": 0, "y": 0}, {"time": 12, "x": -22.82, "y": 24.66}], "scale": [{"time": 0, "x": 0.242, "y": 0.242, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 0.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 1, "y": 1, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 2, "x": 0.242, "y": 0.242, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 2.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5333, "x": 1, "y": 1, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 4, "x": 0.242, "y": 0.242, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 4.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5333, "x": 1, "y": 1, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 6, "x": 0.242, "y": 0.242, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 6.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5333, "x": 1, "y": 1, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 8, "x": 0.242, "y": 0.242, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 8.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.5333, "x": 1, "y": 1, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 10, "x": 0.242, "y": 0.242, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 10.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.5333, "x": 1, "y": 1, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 12, "x": 0.242, "y": 0.242}]}, "par15": {"translate": [{"time": 0, "x": 14.55, "y": 37.06}, {"time": 0.2667, "x": 18.19, "y": 46.33}, {"time": 0.9333, "x": 0, "y": 0}, {"time": 2, "x": 14.55, "y": 37.06}, {"time": 2.2667, "x": 18.19, "y": 46.33}, {"time": 2.9333, "x": 0, "y": 0}, {"time": 4, "x": 14.55, "y": 37.06}, {"time": 4.2667, "x": 18.19, "y": 46.33}, {"time": 4.9333, "x": 0, "y": 0}, {"time": 6, "x": 14.55, "y": 37.06}, {"time": 6.2667, "x": 18.19, "y": 46.33}, {"time": 6.9333, "x": 0, "y": 0}, {"time": 8, "x": 14.55, "y": 37.06}, {"time": 8.2667, "x": 18.19, "y": 46.33}, {"time": 8.9333, "x": 0, "y": 0}, {"time": 10, "x": 14.55, "y": 37.06}, {"time": 10.2667, "x": 18.19, "y": 46.33}, {"time": 10.9333, "x": 0, "y": 0}, {"time": 12, "x": 14.55, "y": 37.06}], "scale": [{"time": 0, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 2, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 2.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.6, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 4, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 4.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 6, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 6.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 8, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 8.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.6, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 10, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 10.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.6, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 12, "x": 0.368, "y": 0.368}]}, "par16": {"translate": [{"time": 0, "x": -66.9, "y": 38.65}, {"time": 0.3333, "x": -89.2, "y": 51.53}, {"time": 1, "x": 0, "y": 0}, {"time": 2, "x": -66.9, "y": 38.65}, {"time": 2.3333, "x": -89.2, "y": 51.53}, {"time": 3, "x": 0, "y": 0}, {"time": 4, "x": -66.9, "y": 38.65}, {"time": 4.3333, "x": -89.2, "y": 51.53}, {"time": 5, "x": 0, "y": 0}, {"time": 6, "x": -66.9, "y": 38.65}, {"time": 6.3333, "x": -89.2, "y": 51.53}, {"time": 7, "x": 0, "y": 0}, {"time": 8, "x": -66.9, "y": 38.65}, {"time": 8.3333, "x": -89.2, "y": 51.53}, {"time": 9, "x": 0, "y": 0}, {"time": 10, "x": -66.9, "y": 38.65}, {"time": 10.3333, "x": -89.2, "y": 51.53}, {"time": 11, "x": 0, "y": 0}, {"time": 12, "x": -66.9, "y": 38.65}], "scale": [{"time": 0, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 4, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 4.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 6, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 6.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 8, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 8.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 10, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 10.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 12, "x": 0.5, "y": 0.5}]}, "par17": {"translate": [{"time": 0, "x": 30.01, "y": -27.58}, {"time": 0.4, "x": 42.87, "y": -39.4}, {"time": 1.0667, "x": 0, "y": 0}, {"time": 2, "x": 30.01, "y": -27.58}, {"time": 2.4, "x": 42.87, "y": -39.4}, {"time": 3.0667, "x": 0, "y": 0}, {"time": 4, "x": 30.01, "y": -27.58}, {"time": 4.4, "x": 42.87, "y": -39.4}, {"time": 5.0667, "x": 0, "y": 0}, {"time": 6, "x": 30.01, "y": -27.58}, {"time": 6.4, "x": 42.87, "y": -39.4}, {"time": 7.0667, "x": 0, "y": 0}, {"time": 8, "x": 30.01, "y": -27.58}, {"time": 8.4, "x": 42.87, "y": -39.4}, {"time": 9.0667, "x": 0, "y": 0}, {"time": 10, "x": 30.01, "y": -27.58}, {"time": 10.4, "x": 42.87, "y": -39.4}, {"time": 11.0667, "x": 0, "y": 0}, {"time": 12, "x": 30.01, "y": -27.58}], "scale": [{"time": 0, "x": 0.632, "y": 0.632, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 1, "y": 1, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 2, "x": 0.632, "y": 0.632, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 2.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7333, "x": 1, "y": 1, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 4, "x": 0.632, "y": 0.632, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 4.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7333, "x": 1, "y": 1, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 6, "x": 0.632, "y": 0.632, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 6.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.7333, "x": 1, "y": 1, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 8, "x": 0.632, "y": 0.632, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 8.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.7333, "x": 1, "y": 1, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 10, "x": 0.632, "y": 0.632, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 10.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.7333, "x": 1, "y": 1, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 12, "x": 0.632, "y": 0.632}]}, "par18": {"translate": [{"time": 0, "x": -15.48, "y": -27.58}, {"time": 0.4667, "x": -23.82, "y": -42.43}, {"time": 1.1333, "x": 0, "y": 0}, {"time": 2, "x": -15.48, "y": -27.58}, {"time": 2.4667, "x": -23.82, "y": -42.43}, {"time": 3.1333, "x": 0, "y": 0}, {"time": 4, "x": -15.48, "y": -27.58}, {"time": 4.4667, "x": -23.82, "y": -42.43}, {"time": 5.1333, "x": 0, "y": 0}, {"time": 6, "x": -15.48, "y": -27.58}, {"time": 6.4667, "x": -23.82, "y": -42.43}, {"time": 7.1333, "x": 0, "y": 0}, {"time": 8, "x": -15.48, "y": -27.58}, {"time": 8.4667, "x": -23.82, "y": -42.43}, {"time": 9.1333, "x": 0, "y": 0}, {"time": 10, "x": -15.48, "y": -27.58}, {"time": 10.4667, "x": -23.82, "y": -42.43}, {"time": 11.1333, "x": 0, "y": 0}, {"time": 12, "x": -15.48, "y": -27.58}], "scale": [{"time": 0, "x": 0.758, "y": 0.758, "curve": [0.345, 0.37, 0.757, 1]}, {"time": 0.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 1, "y": 1, "curve": [0.269, 0, 0.618, 0.42]}, {"time": 2, "x": 0.758, "y": 0.758, "curve": [0.345, 0.37, 0.757, 1]}, {"time": 2.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8, "x": 1, "y": 1, "curve": [0.269, 0, 0.618, 0.42]}, {"time": 4, "x": 0.758, "y": 0.758, "curve": [0.345, 0.37, 0.757, 1]}, {"time": 4.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8, "x": 1, "y": 1, "curve": [0.269, 0, 0.618, 0.42]}, {"time": 6, "x": 0.758, "y": 0.758, "curve": [0.345, 0.37, 0.757, 1]}, {"time": 6.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.8, "x": 1, "y": 1, "curve": [0.269, 0, 0.618, 0.42]}, {"time": 8, "x": 0.758, "y": 0.758, "curve": [0.345, 0.37, 0.757, 1]}, {"time": 8.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8, "x": 1, "y": 1, "curve": [0.269, 0, 0.618, 0.42]}, {"time": 10, "x": 0.758, "y": 0.758, "curve": [0.345, 0.37, 0.757, 1]}, {"time": 10.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.8, "x": 1, "y": 1, "curve": [0.269, 0, 0.618, 0.42]}, {"time": 12, "x": 0.758, "y": 0.758}]}, "par19": {"translate": [{"time": 0, "x": 25.46, "y": -11.17}, {"time": 0.5333, "x": 42.43, "y": -18.62}, {"time": 1.2, "x": 0, "y": 0}, {"time": 2, "x": 25.46, "y": -11.17}, {"time": 2.5333, "x": 42.43, "y": -18.62}, {"time": 3.2, "x": 0, "y": 0}, {"time": 4, "x": 25.46, "y": -11.17}, {"time": 4.5333, "x": 42.43, "y": -18.62}, {"time": 5.2, "x": 0, "y": 0}, {"time": 6, "x": 25.46, "y": -11.17}, {"time": 6.5333, "x": 42.43, "y": -18.62}, {"time": 7.2, "x": 0, "y": 0}, {"time": 8, "x": 25.46, "y": -11.17}, {"time": 8.5333, "x": 42.43, "y": -18.62}, {"time": 9.2, "x": 0, "y": 0}, {"time": 10, "x": 25.46, "y": -11.17}, {"time": 10.5333, "x": 42.43, "y": -18.62}, {"time": 11.2, "x": 0, "y": 0}, {"time": 12, "x": 25.46, "y": -11.17}], "scale": [{"time": 0, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 2, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 2.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 4, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 4.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 6, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 6.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 8, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 8.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 10, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 10.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 12, "x": 0.87, "y": 0.87}]}, "par20": {"translate": [{"time": 0, "x": -39.49, "y": 8.57}, {"time": 0.5333, "x": -65.82, "y": 14.29}, {"time": 1.2, "x": 0, "y": 0}, {"time": 2, "x": -39.49, "y": 8.57}, {"time": 2.5333, "x": -65.82, "y": 14.29}, {"time": 3.2, "x": 0, "y": 0}, {"time": 4, "x": -39.49, "y": 8.57}, {"time": 4.5333, "x": -65.82, "y": 14.29}, {"time": 5.2, "x": 0, "y": 0}, {"time": 6, "x": -39.49, "y": 8.57}, {"time": 6.5333, "x": -65.82, "y": 14.29}, {"time": 7.2, "x": 0, "y": 0}, {"time": 8, "x": -39.49, "y": 8.57}, {"time": 8.5333, "x": -65.82, "y": 14.29}, {"time": 9.2, "x": 0, "y": 0}, {"time": 10, "x": -39.49, "y": 8.57}, {"time": 10.5333, "x": -65.82, "y": 14.29}, {"time": 11.2, "x": 0, "y": 0}, {"time": 12, "x": -39.49, "y": 8.57}], "scale": [{"time": 0, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 2, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 2.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 4, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 4.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 6, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 6.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 8, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 8.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 10, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 10.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 12, "x": 0.87, "y": 0.87}]}, "par21": {"translate": [{"time": 0, "x": 26.43, "y": 8.57}, {"time": 0.6, "x": 48.06, "y": 15.59}, {"time": 1.2667, "x": 0, "y": 0}, {"time": 2, "x": 26.43, "y": 8.57}, {"time": 2.6, "x": 48.06, "y": 15.59}, {"time": 3.2667, "x": 0, "y": 0}, {"time": 4, "x": 26.43, "y": 8.57}, {"time": 4.6, "x": 48.06, "y": 15.59}, {"time": 5.2667, "x": 0, "y": 0}, {"time": 6, "x": 26.43, "y": 8.57}, {"time": 6.6, "x": 48.06, "y": 15.59}, {"time": 7.2667, "x": 0, "y": 0}, {"time": 8, "x": 26.43, "y": 8.57}, {"time": 8.6, "x": 48.06, "y": 15.59}, {"time": 9.2667, "x": 0, "y": 0}, {"time": 10, "x": 26.43, "y": 8.57}, {"time": 10.6, "x": 48.06, "y": 15.59}, {"time": 11.2667, "x": 0, "y": 0}, {"time": 12, "x": 26.43, "y": 8.57}], "scale": [{"time": 0, "x": 0.957, "y": 0.957, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "x": 1, "y": 1, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 2, "x": 0.957, "y": 0.957, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 2.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9333, "x": 1, "y": 1, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 4, "x": 0.957, "y": 0.957, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 4.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.9333, "x": 1, "y": 1, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 6, "x": 0.957, "y": 0.957, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 6.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.9333, "x": 1, "y": 1, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 8, "x": 0.957, "y": 0.957, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 8.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.9333, "x": 1, "y": 1, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 10, "x": 0.957, "y": 0.957, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 10.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.9333, "x": 1, "y": 1, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 12, "x": 0.957, "y": 0.957}]}, "par22": {"translate": [{"time": 0, "x": -25.33, "y": 23.17}, {"time": 0.6667, "x": -50.66, "y": 46.33}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 2, "x": -25.33, "y": 23.17}, {"time": 2.6667, "x": -50.66, "y": 46.33}, {"time": 3.3333, "x": 0, "y": 0}, {"time": 4, "x": -25.33, "y": 23.17}, {"time": 4.6667, "x": -50.66, "y": 46.33}, {"time": 5.3333, "x": 0, "y": 0}, {"time": 6, "x": -25.33, "y": 23.17}, {"time": 6.6667, "x": -50.66, "y": 46.33}, {"time": 7.3333, "x": 0, "y": 0}, {"time": 8, "x": -25.33, "y": 23.17}, {"time": 8.6667, "x": -50.66, "y": 46.33}, {"time": 9.3333, "x": 0, "y": 0}, {"time": 10, "x": -25.33, "y": 23.17}, {"time": 10.6667, "x": -50.66, "y": 46.33}, {"time": 11.3333, "x": 0, "y": 0}, {"time": 12, "x": -25.33, "y": 23.17}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 12, "x": 1, "y": 1}]}, "par23": {"translate": [{"time": 0, "x": -3.12, "y": 23.19}, {"time": 0.7333, "x": -6.93, "y": 51.53}, {"time": 1.4, "x": 0, "y": 0}, {"time": 2, "x": -3.12, "y": 23.19}, {"time": 2.7333, "x": -6.93, "y": 51.53}, {"time": 3.4, "x": 0, "y": 0}, {"time": 4, "x": -3.12, "y": 23.19}, {"time": 4.7333, "x": -6.93, "y": 51.53}, {"time": 5.4, "x": 0, "y": 0}, {"time": 6, "x": -3.12, "y": 23.19}, {"time": 6.7333, "x": -6.93, "y": 51.53}, {"time": 7.4, "x": 0, "y": 0}, {"time": 8, "x": -3.12, "y": 23.19}, {"time": 8.7333, "x": -6.93, "y": 51.53}, {"time": 9.4, "x": 0, "y": 0}, {"time": 10, "x": -3.12, "y": 23.19}, {"time": 10.7333, "x": -6.93, "y": 51.53}, {"time": 11.4, "x": 0, "y": 0}, {"time": 12, "x": -3.12, "y": 23.19}], "scale": [{"time": 0, "x": 0.957, "y": 0.957, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 0.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 2, "x": 0.957, "y": 0.957, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 2.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.4, "x": 0, "y": 0, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 4, "x": 0.957, "y": 0.957, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 4.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.4, "x": 0, "y": 0, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 6, "x": 0.957, "y": 0.957, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 6.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.4, "x": 0, "y": 0, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 8, "x": 0.957, "y": 0.957, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 8.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.4, "x": 0, "y": 0, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 10, "x": 0.957, "y": 0.957, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 10.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.4, "x": 0, "y": 0, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 12, "x": 0.957, "y": 0.957}]}, "par24": {"translate": [{"time": 0, "x": -27.3, "y": -11.76}, {"time": 0.8, "x": -68.25, "y": -29.4}, {"time": 1.4667, "x": 0, "y": 0}, {"time": 2, "x": -27.3, "y": -11.76}, {"time": 2.8, "x": -68.25, "y": -29.4}, {"time": 3.4667, "x": 0, "y": 0}, {"time": 4, "x": -27.3, "y": -11.76}, {"time": 4.8, "x": -68.25, "y": -29.4}, {"time": 5.4667, "x": 0, "y": 0}, {"time": 6, "x": -27.3, "y": -11.76}, {"time": 6.8, "x": -68.25, "y": -29.4}, {"time": 7.4667, "x": 0, "y": 0}, {"time": 8, "x": -27.3, "y": -11.76}, {"time": 8.8, "x": -68.25, "y": -29.4}, {"time": 9.4667, "x": 0, "y": 0}, {"time": 10, "x": -27.3, "y": -11.76}, {"time": 10.8, "x": -68.25, "y": -29.4}, {"time": 11.4667, "x": 0, "y": 0}, {"time": 12, "x": -27.3, "y": -11.76}], "scale": [{"time": 0, "x": 0.87, "y": 0.87, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 0.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4667, "x": 0, "y": 0, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 2, "x": 0.87, "y": 0.87, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 2.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.4667, "x": 0, "y": 0, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 4, "x": 0.87, "y": 0.87, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 4.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.4667, "x": 0, "y": 0, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 6, "x": 0.87, "y": 0.87, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 6.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.4667, "x": 0, "y": 0, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 8, "x": 0.87, "y": 0.87, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 8.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.4667, "x": 0, "y": 0, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 10, "x": 0.87, "y": 0.87, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 10.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.4667, "x": 0, "y": 0, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 12, "x": 0.87, "y": 0.87}]}, "par25": {"translate": [{"time": 0, "x": 8.64, "y": -12.73}, {"time": 0.8667, "x": 24.68, "y": -36.37}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2, "x": 8.64, "y": -12.73}, {"time": 2.8667, "x": 24.68, "y": -36.37}, {"time": 3.5333, "x": 0, "y": 0}, {"time": 4, "x": 8.64, "y": -12.73}, {"time": 4.8667, "x": 24.68, "y": -36.37}, {"time": 5.5333, "x": 0, "y": 0}, {"time": 6, "x": 8.64, "y": -12.73}, {"time": 6.8667, "x": 24.68, "y": -36.37}, {"time": 7.5333, "x": 0, "y": 0}, {"time": 8, "x": 8.64, "y": -12.73}, {"time": 8.8667, "x": 24.68, "y": -36.37}, {"time": 9.5333, "x": 0, "y": 0}, {"time": 10, "x": 8.64, "y": -12.73}, {"time": 10.8667, "x": 24.68, "y": -36.37}, {"time": 11.5333, "x": 0, "y": 0}, {"time": 12, "x": 8.64, "y": -12.73}], "scale": [{"time": 0, "x": 0.758, "y": 0.758, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 0.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5333, "x": 0, "y": 0, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 2, "x": 0.758, "y": 0.758, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 2.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5333, "x": 0, "y": 0, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 4, "x": 0.758, "y": 0.758, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 4.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.5333, "x": 0, "y": 0, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 6, "x": 0.758, "y": 0.758, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 6.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 8, "x": 0.758, "y": 0.758, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 8.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.5333, "x": 0, "y": 0, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 10, "x": 0.758, "y": 0.758, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 10.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.5333, "x": 0, "y": 0, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 12, "x": 0.758, "y": 0.758}]}, "par26": {"translate": [{"time": 0, "x": 12.23, "y": 6.28}, {"time": 1, "x": 48.93, "y": 25.11}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 2, "x": 12.23, "y": 6.28}, {"time": 3, "x": 48.93, "y": 25.11}, {"time": 3.6667, "x": 0, "y": 0}, {"time": 4, "x": 12.23, "y": 6.28}, {"time": 5, "x": 48.93, "y": 25.11}, {"time": 5.6667, "x": 0, "y": 0}, {"time": 6, "x": 12.23, "y": 6.28}, {"time": 7, "x": 48.93, "y": 25.11}, {"time": 7.6667, "x": 0, "y": 0}, {"time": 8, "x": 12.23, "y": 6.28}, {"time": 9, "x": 48.93, "y": 25.11}, {"time": 9.6667, "x": 0, "y": 0}, {"time": 10, "x": 12.23, "y": 6.28}, {"time": 11, "x": 48.93, "y": 25.11}, {"time": 11.6667, "x": 0, "y": 0}, {"time": 12, "x": 12.23, "y": 6.28}], "scale": [{"time": 0, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 4, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 4.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 6, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 6.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 8, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 8.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 10, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 10.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 12, "x": 0.5, "y": 0.5}]}, "par27": {"translate": [{"time": 0, "x": -5.72, "y": -3.03}, {"time": 1.0667, "x": -28.58, "y": -15.15}, {"time": 1.7333, "x": 0, "y": 0}, {"time": 2, "x": -5.72, "y": -3.03}, {"time": 3.0667, "x": -28.58, "y": -15.15}, {"time": 3.7333, "x": 0, "y": 0}, {"time": 4, "x": -5.72, "y": -3.03}, {"time": 5.0667, "x": -28.58, "y": -15.15}, {"time": 5.7333, "x": 0, "y": 0}, {"time": 6, "x": -5.72, "y": -3.03}, {"time": 7.0667, "x": -28.58, "y": -15.15}, {"time": 7.7333, "x": 0, "y": 0}, {"time": 8, "x": -5.72, "y": -3.03}, {"time": 9.0667, "x": -28.58, "y": -15.15}, {"time": 9.7333, "x": 0, "y": 0}, {"time": 10, "x": -5.72, "y": -3.03}, {"time": 11.0667, "x": -28.58, "y": -15.15}, {"time": 11.7333, "x": 0, "y": 0}, {"time": 12, "x": -5.72, "y": -3.03}], "scale": [{"time": 0, "x": 0.368, "y": 0.368, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 0.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7333, "x": 0, "y": 0, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 2, "x": 0.368, "y": 0.368, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 2.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.7333, "x": 0, "y": 0, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 4, "x": 0.368, "y": 0.368, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 4.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 6, "x": 0.368, "y": 0.368, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 6.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.7333, "x": 0, "y": 0, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 8, "x": 0.368, "y": 0.368, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 8.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.7333, "x": 0, "y": 0, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 10, "x": 0.368, "y": 0.368, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 10.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.7333, "x": 0, "y": 0, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 12, "x": 0.368, "y": 0.368}]}, "par28": {"translate": [{"time": 0, "x": 5.54, "y": -3.33}, {"time": 1.2, "x": 55.42, "y": -33.34}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": 5.54, "y": -3.33}, {"time": 3.2, "x": 55.42, "y": -33.34}, {"time": 3.8667, "x": 0, "y": 0}, {"time": 4, "x": 5.54, "y": -3.33}, {"time": 5.2, "x": 55.42, "y": -33.34}, {"time": 5.8667, "x": 0, "y": 0}, {"time": 6, "x": 5.54, "y": -3.33}, {"time": 7.2, "x": 55.42, "y": -33.34}, {"time": 7.8667, "x": 0, "y": 0}, {"time": 8, "x": 5.54, "y": -3.33}, {"time": 9.2, "x": 55.42, "y": -33.34}, {"time": 9.8667, "x": 0, "y": 0}, {"time": 10, "x": 5.54, "y": -3.33}, {"time": 11.2, "x": 55.42, "y": -33.34}, {"time": 11.8667, "x": 0, "y": 0}, {"time": 12, "x": 5.54, "y": -3.33}], "scale": [{"time": 0, "x": 0.13, "y": 0.13, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 0.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8667, "x": 0, "y": 0, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 2, "x": 0.13, "y": 0.13, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 2.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.8667, "x": 0, "y": 0, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 4, "x": 0.13, "y": 0.13, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 4.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.8667, "x": 0, "y": 0, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 6, "x": 0.13, "y": 0.13, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 6.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.8667, "x": 0, "y": 0, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 8, "x": 0.13, "y": 0.13, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 8.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.8667, "x": 0, "y": 0, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 10, "x": 0.13, "y": 0.13, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 10.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.8667, "x": 0, "y": 0, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 12, "x": 0.13, "y": 0.13}]}, "par29": {"translate": [{"time": 0, "x": 0.28, "y": -0.8}, {"time": 1.2667, "x": 5.63, "y": -16.02}, {"time": 1.9333, "x": 0, "y": 0}, {"time": 2, "x": 0.28, "y": -0.8}, {"time": 3.2667, "x": 5.63, "y": -16.02}, {"time": 3.9333, "x": 0, "y": 0}, {"time": 4, "x": 0.28, "y": -0.8}, {"time": 5.2667, "x": 5.63, "y": -16.02}, {"time": 5.9333, "x": 0, "y": 0}, {"time": 6, "x": 0.28, "y": -0.8}, {"time": 7.2667, "x": 5.63, "y": -16.02}, {"time": 7.9333, "x": 0, "y": 0}, {"time": 8, "x": 0.28, "y": -0.8}, {"time": 9.2667, "x": 5.63, "y": -16.02}, {"time": 9.9333, "x": 0, "y": 0}, {"time": 10, "x": 0.28, "y": -0.8}, {"time": 11.2667, "x": 5.63, "y": -16.02}, {"time": 11.9333, "x": 0, "y": 0}, {"time": 12, "x": 0.28, "y": -0.8}], "scale": [{"time": 0, "x": 0.043, "y": 0.043, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 0.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9333, "x": 0, "y": 0, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 2, "x": 0.043, "y": 0.043, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 2.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 4, "x": 0.043, "y": 0.043, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 4.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.9333, "x": 0, "y": 0, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 6, "x": 0.043, "y": 0.043, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 6.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.9333, "x": 0, "y": 0, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 8, "x": 0.043, "y": 0.043, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 8.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.9333, "x": 0, "y": 0, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 10, "x": 0.043, "y": 0.043, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 10.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.9333, "x": 0, "y": 0, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 12, "x": 0.043, "y": 0.043}]}, "par30": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.3333, "x": -40.7, "y": 22.52}, {"time": 2, "x": 0, "y": 0}, {"time": 3.3333, "x": -40.7, "y": 22.52}, {"time": 4, "x": 0, "y": 0}, {"time": 5.3333, "x": -40.7, "y": 22.52}, {"time": 6, "x": 0, "y": 0}, {"time": 7.3333, "x": -40.7, "y": 22.52}, {"time": 8, "x": 0, "y": 0}, {"time": 9.3333, "x": -40.7, "y": 22.52}, {"time": 10, "x": 0, "y": 0}, {"time": 11.3333, "x": -40.7, "y": 22.52}, {"time": 12, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0}]}, "par31": {"translate": [{"time": 0, "x": 54.56, "y": 17.75}, {"time": 0.1, "x": 0, "y": 0}, {"time": 1.4333, "x": 54.56, "y": 17.75, "curve": "stepped"}, {"time": 2, "x": 54.56, "y": 17.75}, {"time": 2.1, "x": 0, "y": 0}, {"time": 3.4333, "x": 54.56, "y": 17.75, "curve": "stepped"}, {"time": 4, "x": 54.56, "y": 17.75}, {"time": 4.1, "x": 0, "y": 0}, {"time": 5.4333, "x": 54.56, "y": 17.75, "curve": "stepped"}, {"time": 6, "x": 54.56, "y": 17.75}, {"time": 6.1, "x": 0, "y": 0}, {"time": 7.4333, "x": 54.56, "y": 17.75, "curve": "stepped"}, {"time": 8, "x": 54.56, "y": 17.75}, {"time": 8.1, "x": 0, "y": 0}, {"time": 9.4333, "x": 54.56, "y": 17.75, "curve": "stepped"}, {"time": 10, "x": 54.56, "y": 17.75}, {"time": 10.1, "x": 0, "y": 0}, {"time": 11.4333, "x": 54.56, "y": 17.75, "curve": "stepped"}, {"time": 12, "x": 54.56, "y": 17.75}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.7667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.7667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.7667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.7667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0}]}, "par32": {"translate": [{"time": 0, "x": 48.5, "y": 8.66}, {"time": 0.3, "x": 0, "y": 0}, {"time": 1.6333, "x": 48.5, "y": 8.66, "curve": "stepped"}, {"time": 2, "x": 48.5, "y": 8.66}, {"time": 2.3, "x": 0, "y": 0}, {"time": 3.6333, "x": 48.5, "y": 8.66, "curve": "stepped"}, {"time": 4, "x": 48.5, "y": 8.66}, {"time": 4.3, "x": 0, "y": 0}, {"time": 5.6333, "x": 48.5, "y": 8.66, "curve": "stepped"}, {"time": 6, "x": 48.5, "y": 8.66}, {"time": 6.3, "x": 0, "y": 0}, {"time": 7.6333, "x": 48.5, "y": 8.66, "curve": "stepped"}, {"time": 8, "x": 48.5, "y": 8.66}, {"time": 8.3, "x": 0, "y": 0}, {"time": 9.6333, "x": 48.5, "y": 8.66, "curve": "stepped"}, {"time": 10, "x": 48.5, "y": 8.66}, {"time": 10.3, "x": 0, "y": 0}, {"time": 11.6333, "x": 48.5, "y": 8.66, "curve": "stepped"}, {"time": 12, "x": 48.5, "y": 8.66}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0}]}, "par33": {"translate": [{"time": 0, "x": -69.28, "y": -17.75}, {"time": 0.5, "x": 0, "y": 0}, {"time": 1.8333, "x": -69.28, "y": -17.75, "curve": "stepped"}, {"time": 2, "x": -69.28, "y": -17.75}, {"time": 2.5, "x": 0, "y": 0}, {"time": 3.8333, "x": -69.28, "y": -17.75, "curve": "stepped"}, {"time": 4, "x": -69.28, "y": -17.75}, {"time": 4.5, "x": 0, "y": 0}, {"time": 5.8333, "x": -69.28, "y": -17.75, "curve": "stepped"}, {"time": 6, "x": -69.28, "y": -17.75}, {"time": 6.5, "x": 0, "y": 0}, {"time": 7.8333, "x": -69.28, "y": -17.75, "curve": "stepped"}, {"time": 8, "x": -69.28, "y": -17.75}, {"time": 8.5, "x": 0, "y": 0}, {"time": 9.8333, "x": -69.28, "y": -17.75, "curve": "stepped"}, {"time": 10, "x": -69.28, "y": -17.75}, {"time": 10.5, "x": 0, "y": 0}, {"time": 11.8333, "x": -69.28, "y": -17.75, "curve": "stepped"}, {"time": 12, "x": -69.28, "y": -17.75}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0}]}, "par34": {"translate": [{"time": 0, "x": 96.26, "y": -20.26}, {"time": 0.0333, "x": 98.72, "y": -20.78}, {"time": 0.7, "x": 0, "y": 0}, {"time": 2, "x": 96.26, "y": -20.26}, {"time": 2.0333, "x": 98.72, "y": -20.78}, {"time": 2.7, "x": 0, "y": 0}, {"time": 4, "x": 96.26, "y": -20.26}, {"time": 4.0333, "x": 98.72, "y": -20.78}, {"time": 4.7, "x": 0, "y": 0}, {"time": 6, "x": 96.26, "y": -20.26}, {"time": 6.0333, "x": 98.72, "y": -20.78}, {"time": 6.7, "x": 0, "y": 0}, {"time": 8, "x": 96.26, "y": -20.26}, {"time": 8.0333, "x": 98.72, "y": -20.78}, {"time": 8.7, "x": 0, "y": 0}, {"time": 10, "x": 96.26, "y": -20.26}, {"time": 10.0333, "x": 98.72, "y": -20.78}, {"time": 10.7, "x": 0, "y": 0}, {"time": 12, "x": 96.26, "y": -20.26}], "scale": [{"time": 0, "x": 0.017, "y": 0.017, "curve": [0.348, 0.66, 0.682, 1]}, {"time": 0.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1, "y": 1, "curve": [0.247, 0, 0.729, 0.91]}, {"time": 2, "x": 0.017, "y": 0.017, "curve": [0.348, 0.66, 0.682, 1]}, {"time": 2.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3667, "x": 1, "y": 1, "curve": [0.247, 0, 0.729, 0.91]}, {"time": 4, "x": 0.017, "y": 0.017, "curve": [0.348, 0.66, 0.682, 1]}, {"time": 4.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3667, "x": 1, "y": 1, "curve": [0.247, 0, 0.729, 0.91]}, {"time": 6, "x": 0.017, "y": 0.017, "curve": [0.348, 0.66, 0.682, 1]}, {"time": 6.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3667, "x": 1, "y": 1, "curve": [0.247, 0, 0.729, 0.91]}, {"time": 8, "x": 0.017, "y": 0.017, "curve": [0.348, 0.66, 0.682, 1]}, {"time": 8.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3667, "x": 1, "y": 1, "curve": [0.247, 0, 0.729, 0.91]}, {"time": 10, "x": 0.017, "y": 0.017, "curve": [0.348, 0.66, 0.682, 1]}, {"time": 10.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.3667, "x": 1, "y": 1, "curve": [0.247, 0, 0.729, 0.91]}, {"time": 12, "x": 0.017, "y": 0.017}]}, "par35": {"translate": [{"time": 0, "x": -10.36, "y": 27.15}, {"time": 0.2333, "x": -12.56, "y": 32.91}, {"time": 0.9, "x": 0, "y": 0}, {"time": 2, "x": -10.36, "y": 27.15}, {"time": 2.2333, "x": -12.56, "y": 32.91}, {"time": 2.9, "x": 0, "y": 0}, {"time": 4, "x": -10.36, "y": 27.15}, {"time": 4.2333, "x": -12.56, "y": 32.91}, {"time": 4.9, "x": 0, "y": 0}, {"time": 6, "x": -10.36, "y": 27.15}, {"time": 6.2333, "x": -12.56, "y": 32.91}, {"time": 6.9, "x": 0, "y": 0}, {"time": 8, "x": -10.36, "y": 27.15}, {"time": 8.2333, "x": -12.56, "y": 32.91}, {"time": 8.9, "x": 0, "y": 0}, {"time": 10, "x": -10.36, "y": 27.15}, {"time": 10.2333, "x": -12.56, "y": 32.91}, {"time": 10.9, "x": 0, "y": 0}, {"time": 12, "x": -10.36, "y": 27.15}], "scale": [{"time": 0, "x": 0.305, "y": 0.305, "curve": [0.382, 0.57, 0.737, 1]}, {"time": 0.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "x": 1, "y": 1, "curve": [0.244, 0, 0.646, 0.59]}, {"time": 2, "x": 0.305, "y": 0.305, "curve": [0.382, 0.57, 0.737, 1]}, {"time": 2.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5667, "x": 1, "y": 1, "curve": [0.244, 0, 0.646, 0.59]}, {"time": 4, "x": 0.305, "y": 0.305, "curve": [0.382, 0.57, 0.737, 1]}, {"time": 4.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5667, "x": 1, "y": 1, "curve": [0.244, 0, 0.646, 0.59]}, {"time": 6, "x": 0.305, "y": 0.305, "curve": [0.382, 0.57, 0.737, 1]}, {"time": 6.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5667, "x": 1, "y": 1, "curve": [0.244, 0, 0.646, 0.59]}, {"time": 8, "x": 0.305, "y": 0.305, "curve": [0.382, 0.57, 0.737, 1]}, {"time": 8.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.5667, "x": 1, "y": 1, "curve": [0.244, 0, 0.646, 0.59]}, {"time": 10, "x": 0.305, "y": 0.305, "curve": [0.382, 0.57, 0.737, 1]}, {"time": 10.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.5667, "x": 1, "y": 1, "curve": [0.244, 0, 0.646, 0.59]}, {"time": 12, "x": 0.305, "y": 0.305}]}, "par36": {"translate": [{"time": 0, "x": 29.81, "y": 21.04}, {"time": 0.4333, "x": 44.17, "y": 31.18}, {"time": 1.1, "x": 0, "y": 0}, {"time": 2, "x": 29.81, "y": 21.04}, {"time": 2.4333, "x": 44.17, "y": 31.18}, {"time": 3.1, "x": 0, "y": 0}, {"time": 4, "x": 29.81, "y": 21.04}, {"time": 4.4333, "x": 44.17, "y": 31.18}, {"time": 5.1, "x": 0, "y": 0}, {"time": 6, "x": 29.81, "y": 21.04}, {"time": 6.4333, "x": 44.17, "y": 31.18}, {"time": 7.1, "x": 0, "y": 0}, {"time": 8, "x": 29.81, "y": 21.04}, {"time": 8.4333, "x": 44.17, "y": 31.18}, {"time": 9.1, "x": 0, "y": 0}, {"time": 10, "x": 29.81, "y": 21.04}, {"time": 10.4333, "x": 44.17, "y": 31.18}, {"time": 11.1, "x": 0, "y": 0}, {"time": 12, "x": 29.81, "y": 21.04}], "scale": [{"time": 0, "x": 0.695, "y": 0.695, "curve": [0.354, 0.41, 0.756, 1]}, {"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "x": 1, "y": 1, "curve": [0.263, 0, 0.618, 0.43]}, {"time": 2, "x": 0.695, "y": 0.695, "curve": [0.354, 0.41, 0.756, 1]}, {"time": 2.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7667, "x": 1, "y": 1, "curve": [0.263, 0, 0.618, 0.43]}, {"time": 4, "x": 0.695, "y": 0.695, "curve": [0.354, 0.41, 0.756, 1]}, {"time": 4.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7667, "x": 1, "y": 1, "curve": [0.263, 0, 0.618, 0.43]}, {"time": 6, "x": 0.695, "y": 0.695, "curve": [0.354, 0.41, 0.756, 1]}, {"time": 6.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.7667, "x": 1, "y": 1, "curve": [0.263, 0, 0.618, 0.43]}, {"time": 8, "x": 0.695, "y": 0.695, "curve": [0.354, 0.41, 0.756, 1]}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.7667, "x": 1, "y": 1, "curve": [0.263, 0, 0.618, 0.43]}, {"time": 10, "x": 0.695, "y": 0.695, "curve": [0.354, 0.41, 0.756, 1]}, {"time": 10.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.7667, "x": 1, "y": 1, "curve": [0.263, 0, 0.618, 0.43]}, {"time": 12, "x": 0.695, "y": 0.695}]}, "par37": {"translate": [{"time": 0, "x": -18.87, "y": 13.87}, {"time": 0.6333, "x": -35.94, "y": 26.41}, {"time": 1.3, "x": 0, "y": 0}, {"time": 2, "x": -18.87, "y": 13.87}, {"time": 2.6333, "x": -35.94, "y": 26.41}, {"time": 3.3, "x": 0, "y": 0}, {"time": 4, "x": -18.87, "y": 13.87}, {"time": 4.6333, "x": -35.94, "y": 26.41}, {"time": 5.3, "x": 0, "y": 0}, {"time": 6, "x": -18.87, "y": 13.87}, {"time": 6.6333, "x": -35.94, "y": 26.41}, {"time": 7.3, "x": 0, "y": 0}, {"time": 8, "x": -18.87, "y": 13.87}, {"time": 8.6333, "x": -35.94, "y": 26.41}, {"time": 9.3, "x": 0, "y": 0}, {"time": 10, "x": -18.87, "y": 13.87}, {"time": 10.6333, "x": -35.94, "y": 26.41}, {"time": 11.3, "x": 0, "y": 0}, {"time": 12, "x": -18.87, "y": 13.87}], "scale": [{"time": 0, "x": 0.983, "y": 0.983, "curve": [0.271, 0.09, 0.753, 1]}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "x": 1, "y": 1, "curve": [0.318, 0, 0.652, 0.34]}, {"time": 2, "x": 0.983, "y": 0.983, "curve": [0.271, 0.09, 0.753, 1]}, {"time": 2.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9667, "x": 1, "y": 1, "curve": [0.318, 0, 0.652, 0.34]}, {"time": 4, "x": 0.983, "y": 0.983, "curve": [0.271, 0.09, 0.753, 1]}, {"time": 4.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.9667, "x": 1, "y": 1, "curve": [0.318, 0, 0.652, 0.34]}, {"time": 6, "x": 0.983, "y": 0.983, "curve": [0.271, 0.09, 0.753, 1]}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.9667, "x": 1, "y": 1, "curve": [0.318, 0, 0.652, 0.34]}, {"time": 8, "x": 0.983, "y": 0.983, "curve": [0.271, 0.09, 0.753, 1]}, {"time": 8.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.9667, "x": 1, "y": 1, "curve": [0.318, 0, 0.652, 0.34]}, {"time": 10, "x": 0.983, "y": 0.983, "curve": [0.271, 0.09, 0.753, 1]}, {"time": 10.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.9667, "x": 1, "y": 1, "curve": [0.318, 0, 0.652, 0.34]}, {"time": 12, "x": 0.983, "y": 0.983}]}, "par38": {"translate": [{"time": 0, "x": 24.52, "y": 8.44}, {"time": 0.8333, "x": 65.38, "y": 22.52}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 24.52, "y": 8.44}, {"time": 2.8333, "x": 65.38, "y": 22.52}, {"time": 3.5, "x": 0, "y": 0}, {"time": 4, "x": 24.52, "y": 8.44}, {"time": 4.8333, "x": 65.38, "y": 22.52}, {"time": 5.5, "x": 0, "y": 0}, {"time": 6, "x": 24.52, "y": 8.44}, {"time": 6.8333, "x": 65.38, "y": 22.52}, {"time": 7.5, "x": 0, "y": 0}, {"time": 8, "x": 24.52, "y": 8.44}, {"time": 8.8333, "x": 65.38, "y": 22.52}, {"time": 9.5, "x": 0, "y": 0}, {"time": 10, "x": 24.52, "y": 8.44}, {"time": 10.8333, "x": 65.38, "y": 22.52}, {"time": 11.5, "x": 0, "y": 0}, {"time": 12, "x": 24.52, "y": 8.44}], "scale": [{"time": 0, "x": 0.816, "y": 0.816, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": [0.242, 0, 0.667, 0.67]}, {"time": 2, "x": 0.816, "y": 0.816, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 2.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5, "x": 0, "y": 0, "curve": [0.242, 0, 0.667, 0.67]}, {"time": 4, "x": 0.816, "y": 0.816, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 4.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.5, "x": 0, "y": 0, "curve": [0.242, 0, 0.667, 0.67]}, {"time": 6, "x": 0.816, "y": 0.816, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 6.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5, "x": 0, "y": 0, "curve": [0.242, 0, 0.667, 0.67]}, {"time": 8, "x": 0.816, "y": 0.816, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 8.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.5, "x": 0, "y": 0, "curve": [0.242, 0, 0.667, 0.67]}, {"time": 10, "x": 0.816, "y": 0.816, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 10.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.5, "x": 0, "y": 0, "curve": [0.242, 0, 0.667, 0.67]}, {"time": 12, "x": 0.816, "y": 0.816}]}, "par39": {"translate": [{"time": 0, "x": -4.38, "y": -5.55}, {"time": 1.0333, "x": -19.48, "y": -24.68}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": -4.38, "y": -5.55}, {"time": 3.0333, "x": -19.48, "y": -24.68}, {"time": 3.7, "x": 0, "y": 0}, {"time": 4, "x": -4.38, "y": -5.55}, {"time": 5.0333, "x": -19.48, "y": -24.68}, {"time": 5.7, "x": 0, "y": 0}, {"time": 6, "x": -4.38, "y": -5.55}, {"time": 7.0333, "x": -19.48, "y": -24.68}, {"time": 7.7, "x": 0, "y": 0}, {"time": 8, "x": -4.38, "y": -5.55}, {"time": 9.0333, "x": -19.48, "y": -24.68}, {"time": 9.7, "x": 0, "y": 0}, {"time": 10, "x": -4.38, "y": -5.55}, {"time": 11.0333, "x": -19.48, "y": -24.68}, {"time": 11.7, "x": 0, "y": 0}, {"time": 12, "x": -4.38, "y": -5.55}], "scale": [{"time": 0, "x": 0.434, "y": 0.434, "curve": [0.37, 0.47, 0.753, 1]}, {"time": 0.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7, "x": 0, "y": 0, "curve": [0.253, 0, 0.621, 0.48]}, {"time": 2, "x": 0.434, "y": 0.434, "curve": [0.37, 0.47, 0.753, 1]}, {"time": 2.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.7, "x": 0, "y": 0, "curve": [0.253, 0, 0.621, 0.48]}, {"time": 4, "x": 0.434, "y": 0.434, "curve": [0.37, 0.47, 0.753, 1]}, {"time": 4.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7, "x": 0, "y": 0, "curve": [0.253, 0, 0.621, 0.48]}, {"time": 6, "x": 0.434, "y": 0.434, "curve": [0.37, 0.47, 0.753, 1]}, {"time": 6.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.7, "x": 0, "y": 0, "curve": [0.253, 0, 0.621, 0.48]}, {"time": 8, "x": 0.434, "y": 0.434, "curve": [0.37, 0.47, 0.753, 1]}, {"time": 8.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.7, "x": 0, "y": 0, "curve": [0.253, 0, 0.621, 0.48]}, {"time": 10, "x": 0.434, "y": 0.434, "curve": [0.37, 0.47, 0.753, 1]}, {"time": 10.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.7, "x": 0, "y": 0, "curve": [0.253, 0, 0.621, 0.48]}, {"time": 12, "x": 0.434, "y": 0.434}]}, "par40": {"translate": [{"time": 0, "x": 1.36, "y": -1.69}, {"time": 1.2333, "x": 18.19, "y": -22.52}, {"time": 1.9, "x": 0, "y": 0}, {"time": 2, "x": 1.36, "y": -1.69}, {"time": 3.2333, "x": 18.19, "y": -22.52}, {"time": 3.9, "x": 0, "y": 0}, {"time": 4, "x": 1.36, "y": -1.69}, {"time": 5.2333, "x": 18.19, "y": -22.52}, {"time": 5.9, "x": 0, "y": 0}, {"time": 6, "x": 1.36, "y": -1.69}, {"time": 7.2333, "x": 18.19, "y": -22.52}, {"time": 7.9, "x": 0, "y": 0}, {"time": 8, "x": 1.36, "y": -1.69}, {"time": 9.2333, "x": 18.19, "y": -22.52}, {"time": 9.9, "x": 0, "y": 0}, {"time": 10, "x": 1.36, "y": -1.69}, {"time": 11.2333, "x": 18.19, "y": -22.52}, {"time": 11.9, "x": 0, "y": 0}, {"time": 12, "x": 1.36, "y": -1.69}], "scale": [{"time": 0, "x": 0.083, "y": 0.083, "curve": [0.306, 0.23, 0.756, 1]}, {"time": 0.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9, "x": 0, "y": 0, "curve": [0.294, 0, 0.631, 0.37]}, {"time": 2, "x": 0.083, "y": 0.083, "curve": [0.306, 0.23, 0.756, 1]}, {"time": 2.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9, "x": 0, "y": 0, "curve": [0.294, 0, 0.631, 0.37]}, {"time": 4, "x": 0.083, "y": 0.083, "curve": [0.306, 0.23, 0.756, 1]}, {"time": 4.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.9, "x": 0, "y": 0, "curve": [0.294, 0, 0.631, 0.37]}, {"time": 6, "x": 0.083, "y": 0.083, "curve": [0.306, 0.23, 0.756, 1]}, {"time": 6.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.9, "x": 0, "y": 0, "curve": [0.294, 0, 0.631, 0.37]}, {"time": 8, "x": 0.083, "y": 0.083, "curve": [0.306, 0.23, 0.756, 1]}, {"time": 8.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.9, "x": 0, "y": 0, "curve": [0.294, 0, 0.631, 0.37]}, {"time": 10, "x": 0.083, "y": 0.083, "curve": [0.306, 0.23, 0.756, 1]}, {"time": 10.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.9, "x": 0, "y": 0, "curve": [0.294, 0, 0.631, 0.37]}, {"time": 12, "x": 0.083, "y": 0.083}]}, "bone11": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -30.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 9.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -8.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0, 0.62, 0.59, 1]}, {"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1}]}, "bone16": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -64.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 12.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -12.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0, 0.62, 0.59, 1]}, {"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1}]}, "bone": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": 28.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -9.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 9.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0, 0.62, 0.59, 1]}, {"time": 0.3333, "x": 1, "y": 1}]}, "bone6": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": 61.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -17.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 9.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0, 0.62, 0.59, 1]}, {"time": 0.3333, "x": 1, "y": 1}]}, "you win": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 2.56, "y": 2.56, "curve": [0.255, 0, 1, 0.61]}, {"time": 0.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1}]}, "par": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2667, "x": 0, "y": 0, "curve": [0, 0.51, 0.383, 1]}, {"time": 0.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1}]}, "glow_1": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2667, "x": 0, "y": 0, "curve": [0, 0.6, 0.543, 1]}, {"time": 0.5333, "x": 1, "y": 1}]}, "xinxilan1": {"scale": [{"time": 0, "x": 0, "y": 1, "curve": "stepped"}, {"time": 0.2667, "x": 0, "y": 1, "curve": [0, 0.52, 0.571, 1]}, {"time": 0.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1}]}, "caidai1": {"rotate": [{"time": 0.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 170.41}], "translate": [{"time": 0.1333, "x": 184.1, "y": 381.04, "curve": [0, 0.79, 0.75, 1]}, {"time": 0.7333, "x": 83.95, "y": 555.84, "curve": [0.25, 0, 1, 0.41]}, {"time": 1.4, "x": 7.29, "y": 122.76}], "scale": [{"time": 0.1333, "x": 1, "y": 1}]}, "caidai2": {"rotate": [{"time": 0.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -168.8}], "translate": [{"time": 0.1333, "x": 273.91, "y": -109.01, "curve": [0, 0.81, 0.75, 1]}, {"time": 0.5333, "x": 86.79, "y": 19.26, "curve": [0.25, 0, 1, 0.24]}, {"time": 1.1, "x": -8.78, "y": -212.12}], "scale": [{"time": 0.7333, "x": 1.012, "y": 1.012}]}, "caidai3": {"rotate": [{"time": 0.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 180}], "translate": [{"time": 0.1333, "x": 118.51, "y": -80.5, "curve": [0, 0.81, 0.75, 1]}, {"time": 0.6333, "x": -40.62, "y": 43.97, "curve": [0.25, 0, 1, 0.24]}, {"time": 1.2, "x": -47.78, "y": -176.15}]}, "caidai4": {"rotate": [{"time": 0.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 180}], "translate": [{"time": 0.1333, "x": -293.48, "y": -168.26, "curve": [0, 0.81, 0.75, 1]}, {"time": 0.6333, "x": -117.61, "y": -56.76, "curve": [0.25, 0, 1, 0.24]}, {"time": 1.2, "x": 43.56, "y": -288.95}]}, "caidai5": {"rotate": [{"time": 0.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 180}], "translate": [{"time": 0.1333, "x": -263.29, "y": 12.3, "curve": [0, 0.81, 0.75, 1]}, {"time": 0.6333, "x": -74.28, "y": 32.83, "curve": [0.25, 0, 1, 0.24]}, {"time": 1.2, "x": 46.03, "y": -235.34}]}, "baodian_star1": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": -75.12}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.9333, "x": 83.65, "y": -133.65}], "scale": [{"time": 0.6667, "x": 1, "y": 1}, {"time": 0.9333, "x": 0, "y": 0}]}, "baodian_par1": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": 159.53, "y": -43.5}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par2": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": 65.4, "y": -146.5}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par3": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": 165.01, "y": -133.77}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par4": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": -35.24, "y": -157.8}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par5": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": -124.26, "y": 39.89}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par6": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": 10.41, "y": 104.13}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par7": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": -217.87, "y": -83.3}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par8": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": 349.89, "y": 32.7}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par9": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": 168.08, "y": 109.87}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par10": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": -214.51, "y": 1.96}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_star2": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": 83.77}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.8333, "x": 146.52, "y": -139.59}], "scale": [{"time": 0.5667, "x": 1, "y": 1}, {"time": 0.8333, "x": 0, "y": 0}]}, "baodian_star3": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": -45.67}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.9333, "x": -84.15, "y": -96.52}], "scale": [{"time": 0.6667, "x": 1, "y": 1}, {"time": 0.9333, "x": 0, "y": 0}]}, "baodian_star4": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": 51.43}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.8667, "x": -191.05, "y": 73.78}], "scale": [{"time": 0.6, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_star5": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": 29.53}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.9333, "x": -24.25, "y": 125.23}], "scale": [{"time": 0.6667, "x": 1, "y": 1}, {"time": 0.9333, "x": 0, "y": 0}]}, "baodian_star6": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": -41.44}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.8667, "x": 76.23, "y": 84.06}], "scale": [{"time": 0.6, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_star7": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": 44.61}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.8333, "x": -111.65, "y": 97.82}], "scale": [{"time": 0.5667, "x": 1, "y": 1}, {"time": 0.8333, "x": 0, "y": 0}]}, "baodian_star8": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": -81.99, "curve": "stepped"}, {"time": 2, "angle": -81.99, "curve": "stepped"}, {"time": 4, "angle": -81.99, "curve": "stepped"}, {"time": 6, "angle": -81.99, "curve": "stepped"}, {"time": 8, "angle": -81.99, "curve": "stepped"}, {"time": 10, "angle": -81.99}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.9, "x": 167.47, "y": 91.65, "curve": "stepped"}, {"time": 2, "x": 167.47, "y": 91.65, "curve": "stepped"}, {"time": 4, "x": 167.47, "y": 91.65, "curve": "stepped"}, {"time": 6, "x": 167.47, "y": 91.65, "curve": "stepped"}, {"time": 8, "x": 167.47, "y": 91.65, "curve": "stepped"}, {"time": 10, "x": 167.47, "y": 91.65}], "scale": [{"time": 0.6333, "x": 1, "y": 1}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}, "baodian_star9": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": -98.68, "curve": "stepped"}, {"time": 2, "angle": -98.68, "curve": "stepped"}, {"time": 4, "angle": -98.68, "curve": "stepped"}, {"time": 6, "angle": -98.68, "curve": "stepped"}, {"time": 8, "angle": -98.68, "curve": "stepped"}, {"time": 10, "angle": -98.68}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.8333, "x": 77.68, "y": 121.41, "curve": "stepped"}, {"time": 2, "x": 77.68, "y": 121.41, "curve": "stepped"}, {"time": 4, "x": 77.68, "y": 121.41, "curve": "stepped"}, {"time": 6, "x": 77.68, "y": 121.41, "curve": "stepped"}, {"time": 8, "x": 77.68, "y": 121.41, "curve": "stepped"}, {"time": 10, "x": 77.68, "y": 121.41}], "scale": [{"time": 0.5667, "x": 1, "y": 1}, {"time": 0.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}, "baodian_star10": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": -52.71, "curve": "stepped"}, {"time": 2, "angle": -52.71, "curve": "stepped"}, {"time": 4, "angle": -52.71, "curve": "stepped"}, {"time": 6, "angle": -52.71, "curve": "stepped"}, {"time": 8, "angle": -52.71, "curve": "stepped"}, {"time": 10, "angle": -52.71}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.8667, "x": 210.37, "y": -57.42, "curve": "stepped"}, {"time": 2, "x": 210.37, "y": -57.42, "curve": "stepped"}, {"time": 4, "x": 210.37, "y": -57.42, "curve": "stepped"}, {"time": 6, "x": 210.37, "y": -57.42, "curve": "stepped"}, {"time": 8, "x": 210.37, "y": -57.42, "curve": "stepped"}, {"time": 10, "x": 210.37, "y": -57.42}], "scale": [{"time": 0.6, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}, "baodian_par11": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": -140.15, "y": 87.49, "curve": "stepped"}, {"time": 2, "x": -140.15, "y": 87.49, "curve": "stepped"}, {"time": 4, "x": -140.15, "y": 87.49, "curve": "stepped"}, {"time": 6, "x": -140.15, "y": 87.49, "curve": "stepped"}, {"time": 8, "x": -140.15, "y": 87.49, "curve": "stepped"}, {"time": 10, "x": -140.15, "y": 87.49}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}, "baodian_par12": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": 96.14, "y": 125.57, "curve": "stepped"}, {"time": 2, "x": 96.14, "y": 125.57, "curve": "stepped"}, {"time": 4, "x": 96.14, "y": 125.57, "curve": "stepped"}, {"time": 6, "x": 96.14, "y": 125.57, "curve": "stepped"}, {"time": 8, "x": 96.14, "y": 125.57, "curve": "stepped"}, {"time": 10, "x": 96.14, "y": 125.57}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}}}, "idle_up": {"slots": {"baodian_par1": {"attachment": [{"time": 0.1333, "name": "par"}]}, "baodian_par2": {"attachment": [{"time": 0.1333, "name": "par"}]}, "baodian_par3": {"attachment": [{"time": 0.1333, "name": "par"}]}, "baodian_par4": {"attachment": [{"time": 0.1333, "name": "par"}]}, "baodian_par5": {"attachment": [{"time": 0.1333, "name": "par"}]}, "baodian_par6": {"attachment": [{"time": 0.1333, "name": "par"}]}, "baodian_par7": {"attachment": [{"time": 0.1333, "name": "par"}]}, "baodian_par8": {"attachment": [{"time": 0.1333, "name": "par"}]}, "baodian_par9": {"attachment": [{"time": 0.1333, "name": "par"}]}, "baodian_par10": {"attachment": [{"time": 0.1333, "name": "par"}]}, "baodian_par11": {"attachment": [{"time": 0.1333, "name": "par"}]}, "baodian_par12": {"attachment": [{"time": 0.1333, "name": "par"}]}, "baodian_star1": {"attachment": [{"time": 0.1333, "name": "star"}]}, "baodian_star2": {"attachment": [{"time": 0.1333, "name": "star"}]}, "baodian_star3": {"attachment": [{"time": 0.1333, "name": "star"}]}, "baodian_star4": {"attachment": [{"time": 0.1333, "name": "star"}]}, "baodian_star5": {"attachment": [{"time": 0.1333, "name": "star"}]}, "baodian_star6": {"attachment": [{"time": 0.1333, "name": "star"}]}, "baodian_star7": {"attachment": [{"time": 0.1333, "name": "star"}]}, "baodian_star8": {"attachment": [{"time": 0.1333, "name": "star"}]}, "baodian_star9": {"attachment": [{"time": 0.1333, "name": "star"}]}, "baodian_star10": {"attachment": [{"time": 0.1333, "name": "star"}]}, "baodian_xulie": {"attachment": [{"time": 0.1333, "name": "baodian_00000"}, {"time": 0.1667, "name": "baodian_00001"}, {"time": 0.2, "name": "baodian_00002"}, {"time": 0.2333, "name": "baodian_00003"}, {"time": 0.2667, "name": "baodian_00004"}, {"time": 0.3, "name": "baodian_00005"}, {"time": 0.3333, "name": "baodian_00006"}, {"time": 0.4, "name": "baodian_00007"}, {"time": 0.4333, "name": "baodian_00008"}, {"time": 0.5, "name": "baodian_00009"}, {"time": 0.5333, "name": "baodian_00010"}, {"time": 0.5667, "name": "baodian_00011"}, {"time": 0.6333, "name": "baodian_00012"}, {"time": 0.6667, "name": "baodian_00013"}, {"time": 0.7, "name": null}]}, "caidai1": {"color": [{"time": 1.1667, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": "caidai1"}]}, "caidai2": {"color": [{"time": 0.9, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": "caidai2"}]}, "caidai3": {"color": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": "caidai3"}]}, "caidai4": {"color": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": "caidai4"}]}, "caidai5": {"color": [{"time": 0.9333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": "caidai5"}]}, "glow": {"attachment": [{"time": 0, "name": null}]}, "glow2": {"attachment": [{"time": 0, "name": null}]}, "maishui_l1": {"attachment": [{"time": 0, "name": null}]}, "maishui_l2": {"attachment": [{"time": 0, "name": null}]}, "maishui_r1": {"attachment": [{"time": 0, "name": null}]}, "maishui_r2": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"par1": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.3333, "x": 53.26, "y": -34.64}, {"time": 2, "x": 0, "y": 0}, {"time": 3.3333, "x": 53.26, "y": -34.64}, {"time": 4, "x": 0, "y": 0}, {"time": 5.3333, "x": 53.26, "y": -34.64}, {"time": 6, "x": 0, "y": 0}, {"time": 7.3333, "x": 53.26, "y": -34.64}, {"time": 8, "x": 0, "y": 0}, {"time": 9.3333, "x": 53.26, "y": -34.64}, {"time": 10, "x": 0, "y": 0}, {"time": 11.3333, "x": 53.26, "y": -34.64}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.3333, "x": 0, "y": 0}]}, "par2": {"translate": [{"time": 0.0667, "x": 0, "y": 0}, {"time": 1.4, "x": -8.23, "y": 45.9}, {"time": 2, "x": -0.82, "y": 4.59}, {"time": 2.0667, "x": 0, "y": 0}, {"time": 3.4, "x": -8.23, "y": 45.9}, {"time": 4, "x": -0.82, "y": 4.59}, {"time": 4.0667, "x": 0, "y": 0}, {"time": 5.4, "x": -8.23, "y": 45.9}, {"time": 6, "x": -0.82, "y": 4.59}, {"time": 6.0667, "x": 0, "y": 0}, {"time": 7.4, "x": -8.23, "y": 45.9}, {"time": 8, "x": -0.82, "y": 4.59}, {"time": 8.0667, "x": 0, "y": 0}, {"time": 9.4, "x": -8.23, "y": 45.9}, {"time": 10, "x": -0.82, "y": 4.59}, {"time": 10.0667, "x": 0, "y": 0}, {"time": 11.4, "x": -8.23, "y": 45.9}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.4, "x": 0, "y": 0}]}, "par3": {"translate": [{"time": 0.1333, "x": 18.39, "y": 27.05}, {"time": 1.4667, "x": -101.1, "y": 72.17}, {"time": 2, "x": -5.5, "y": 36.07}, {"time": 2.1333, "x": 18.39, "y": 27.05}, {"time": 3.4667, "x": -101.1, "y": 72.17}, {"time": 4, "x": -5.5, "y": 36.07}, {"time": 4.1333, "x": 18.39, "y": 27.05}, {"time": 5.4667, "x": -101.1, "y": 72.17}, {"time": 6, "x": -5.5, "y": 36.07}, {"time": 6.1333, "x": 18.39, "y": 27.05}, {"time": 7.4667, "x": -101.1, "y": 72.17}, {"time": 8, "x": -5.5, "y": 36.07}, {"time": 8.1333, "x": 18.39, "y": 27.05}, {"time": 9.4667, "x": -101.1, "y": 72.17}, {"time": 10, "x": -5.5, "y": 36.07}, {"time": 10.1333, "x": 18.39, "y": 27.05}, {"time": 11.4667, "x": -101.1, "y": 72.17}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.4667, "x": 0, "y": 0}]}, "par4": {"translate": [{"time": 0.2, "x": 10.62, "y": 24.96}, {"time": 1.5333, "x": 62.12, "y": 94.63}, {"time": 2, "x": 26.07, "y": 45.86}, {"time": 2.2, "x": 10.62, "y": 24.96}, {"time": 3.5333, "x": 62.12, "y": 94.63}, {"time": 4, "x": 26.07, "y": 45.86}, {"time": 4.2, "x": 10.62, "y": 24.96}, {"time": 5.5333, "x": 62.12, "y": 94.63}, {"time": 6, "x": 26.07, "y": 45.86}, {"time": 6.2, "x": 10.62, "y": 24.96}, {"time": 7.5333, "x": 62.12, "y": 94.63}, {"time": 8, "x": 26.07, "y": 45.86}, {"time": 8.2, "x": 10.62, "y": 24.96}, {"time": 9.5333, "x": 62.12, "y": 94.63}, {"time": 10, "x": 26.07, "y": 45.86}, {"time": 10.2, "x": 10.62, "y": 24.96}, {"time": 11.5333, "x": 62.12, "y": 94.63}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.8667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.5333, "x": 0, "y": 0}]}, "par5": {"translate": [{"time": 0.2667, "x": 0, "y": 0}, {"time": 1.6, "x": -88.33, "y": 15.59}, {"time": 2, "x": -35.33, "y": 6.24}, {"time": 2.2667, "x": 0, "y": 0}, {"time": 3.6, "x": -88.33, "y": 15.59}, {"time": 4, "x": -35.33, "y": 6.24}, {"time": 4.2667, "x": 0, "y": 0}, {"time": 5.6, "x": -88.33, "y": 15.59}, {"time": 6, "x": -35.33, "y": 6.24}, {"time": 6.2667, "x": 0, "y": 0}, {"time": 7.6, "x": -88.33, "y": 15.59}, {"time": 8, "x": -35.33, "y": 6.24}, {"time": 8.2667, "x": 0, "y": 0}, {"time": 9.6, "x": -88.33, "y": 15.59}, {"time": 10, "x": -35.33, "y": 6.24}, {"time": 10.2667, "x": 0, "y": 0}, {"time": 11.6, "x": -88.33, "y": 15.59}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.6, "x": 0, "y": 0}]}, "par6": {"translate": [{"time": 0.3333, "x": 0, "y": 0}, {"time": 1.6667, "x": 54.12, "y": 15.15}, {"time": 2, "x": 27.06, "y": 7.58}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 3.6667, "x": 54.12, "y": 15.15}, {"time": 4, "x": 27.06, "y": 7.58}, {"time": 4.3333, "x": 0, "y": 0}, {"time": 5.6667, "x": 54.12, "y": 15.15}, {"time": 6, "x": 27.06, "y": 7.58}, {"time": 6.3333, "x": 0, "y": 0}, {"time": 7.6667, "x": 54.12, "y": 15.15}, {"time": 8, "x": 27.06, "y": 7.58}, {"time": 8.3333, "x": 0, "y": 0}, {"time": 9.6667, "x": 54.12, "y": 15.15}, {"time": 10, "x": 27.06, "y": 7.58}, {"time": 10.3333, "x": 0, "y": 0}, {"time": 11.6667, "x": 54.12, "y": 15.15}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.6667, "x": 0, "y": 0}]}, "par7": {"translate": [{"time": 0.4, "x": 5.78, "y": 66.47}, {"time": 1.7333, "x": -26.01, "y": 128.89}, {"time": 2, "x": -13.29, "y": 103.92}, {"time": 2.4, "x": 5.78, "y": 66.47}, {"time": 3.7333, "x": -26.01, "y": 128.89}, {"time": 4, "x": -13.29, "y": 103.92}, {"time": 4.4, "x": 5.78, "y": 66.47}, {"time": 5.7333, "x": -26.01, "y": 128.89}, {"time": 6, "x": -13.29, "y": 103.92}, {"time": 6.4, "x": 5.78, "y": 66.47}, {"time": 7.7333, "x": -26.01, "y": 128.89}, {"time": 8, "x": -13.29, "y": 103.92}, {"time": 8.4, "x": 5.78, "y": 66.47}, {"time": 9.7333, "x": -26.01, "y": 128.89}, {"time": 10, "x": -13.29, "y": 103.92}, {"time": 10.4, "x": 5.78, "y": 66.47}, {"time": 11.7333, "x": -26.01, "y": 128.89}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.7333, "x": 0, "y": 0}]}, "par8": {"translate": [{"time": 0.4667, "x": 0, "y": 0}, {"time": 1.8, "x": 38.54, "y": 0.43}, {"time": 2, "x": 26.98, "y": 0.3}, {"time": 2.4667, "x": 0, "y": 0}, {"time": 3.8, "x": 38.54, "y": 0.43}, {"time": 4, "x": 26.98, "y": 0.3}, {"time": 4.4667, "x": 0, "y": 0}, {"time": 5.8, "x": 38.54, "y": 0.43}, {"time": 6, "x": 26.98, "y": 0.3}, {"time": 6.4667, "x": 0, "y": 0}, {"time": 7.8, "x": 38.54, "y": 0.43}, {"time": 8, "x": 26.98, "y": 0.3}, {"time": 8.4667, "x": 0, "y": 0}, {"time": 9.8, "x": 38.54, "y": 0.43}, {"time": 10, "x": 26.98, "y": 0.3}, {"time": 10.4667, "x": 0, "y": 0}, {"time": 11.8, "x": 38.54, "y": 0.43}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.8, "x": 0, "y": 0}]}, "par9": {"translate": [{"time": 0.5333, "x": 78.15, "y": 25.87}, {"time": 2, "x": 174, "y": 85.13}, {"time": 2.5333, "x": 78.15, "y": 25.87}, {"time": 4, "x": 174, "y": 85.13}, {"time": 4.5333, "x": 78.15, "y": 25.87}, {"time": 6, "x": 174, "y": 85.13}, {"time": 6.5333, "x": 78.15, "y": 25.87}, {"time": 8, "x": 174, "y": 85.13}, {"time": 8.5333, "x": 78.15, "y": 25.87}, {"time": 10, "x": 174, "y": 85.13}, {"time": 10.5333, "x": 78.15, "y": 25.87}, {"time": 12, "x": 174, "y": 85.13}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 12, "x": 0, "y": 0}]}, "par10": {"translate": [{"time": 0.6, "x": 0, "y": 0}, {"time": 1.9333, "x": 20.35, "y": 28.58}, {"time": 2, "x": 18.32, "y": 25.72}, {"time": 2.6, "x": 0, "y": 0}, {"time": 3.9333, "x": 20.35, "y": 28.58}, {"time": 4, "x": 18.32, "y": 25.72}, {"time": 4.6, "x": 0, "y": 0}, {"time": 5.9333, "x": 20.35, "y": 28.58}, {"time": 6, "x": 18.32, "y": 25.72}, {"time": 6.6, "x": 0, "y": 0}, {"time": 7.9333, "x": 20.35, "y": 28.58}, {"time": 8, "x": 18.32, "y": 25.72}, {"time": 8.6, "x": 0, "y": 0}, {"time": 9.9333, "x": 20.35, "y": 28.58}, {"time": 10, "x": 18.32, "y": 25.72}, {"time": 10.6, "x": 0, "y": 0}, {"time": 11.9333, "x": 20.35, "y": 28.58}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.2667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.9333, "x": 0, "y": 0}]}, "par11": {"translate": [{"time": 0.6667, "x": 0, "y": 0}, {"time": 2, "x": -37.67, "y": 53.69}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 4, "x": -37.67, "y": 53.69}, {"time": 4.6667, "x": 0, "y": 0}, {"time": 6, "x": -37.67, "y": 53.69}, {"time": 6.6667, "x": 0, "y": 0}, {"time": 8, "x": -37.67, "y": 53.69}, {"time": 8.6667, "x": 0, "y": 0}, {"time": 10, "x": -37.67, "y": 53.69}, {"time": 10.6667, "x": 0, "y": 0}, {"time": 12, "x": -37.67, "y": 53.69}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 12, "x": 0, "y": 0}]}, "par12": {"translate": [{"time": 0, "x": 78.57, "y": 12.75}, {"time": 0.0667, "x": 82.7, "y": 13.42}, {"time": 0.7333, "x": 0, "y": 0}, {"time": 2, "x": 78.57, "y": 12.75}, {"time": 2.0667, "x": 82.7, "y": 13.42}, {"time": 2.7333, "x": 0, "y": 0}, {"time": 4, "x": 78.57, "y": 12.75}, {"time": 4.0667, "x": 82.7, "y": 13.42}, {"time": 4.7333, "x": 0, "y": 0}, {"time": 6, "x": 78.57, "y": 12.75}, {"time": 6.0667, "x": 82.7, "y": 13.42}, {"time": 6.7333, "x": 0, "y": 0}, {"time": 8, "x": 78.57, "y": 12.75}, {"time": 8.0667, "x": 82.7, "y": 13.42}, {"time": 8.7333, "x": 0, "y": 0}, {"time": 10, "x": 78.57, "y": 12.75}, {"time": 10.0667, "x": 82.7, "y": 13.42}, {"time": 10.7333, "x": 0, "y": 0}, {"time": 12, "x": 78.57, "y": 12.75}], "scale": [{"time": 0, "x": 0.043, "y": 0.043, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 1, "y": 1, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 2, "x": 0.043, "y": 0.043, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 2.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4, "x": 1, "y": 1, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 4, "x": 0.043, "y": 0.043, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 4.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4, "x": 1, "y": 1, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 6, "x": 0.043, "y": 0.043, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 6.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4, "x": 1, "y": 1, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 8, "x": 0.043, "y": 0.043, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 8.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4, "x": 1, "y": 1, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 10, "x": 0.043, "y": 0.043, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 10.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.4, "x": 1, "y": 1, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 12, "x": 0.043, "y": 0.043}]}, "par13": {"translate": [{"time": 0, "x": -72.09, "y": 10.13}, {"time": 0.1333, "x": -80.1, "y": 11.26}, {"time": 0.8, "x": 0, "y": 0}, {"time": 2, "x": -72.09, "y": 10.13}, {"time": 2.1333, "x": -80.1, "y": 11.26}, {"time": 2.8, "x": 0, "y": 0}, {"time": 4, "x": -72.09, "y": 10.13}, {"time": 4.1333, "x": -80.1, "y": 11.26}, {"time": 4.8, "x": 0, "y": 0}, {"time": 6, "x": -72.09, "y": 10.13}, {"time": 6.1333, "x": -80.1, "y": 11.26}, {"time": 6.8, "x": 0, "y": 0}, {"time": 8, "x": -72.09, "y": 10.13}, {"time": 8.1333, "x": -80.1, "y": 11.26}, {"time": 8.8, "x": 0, "y": 0}, {"time": 10, "x": -72.09, "y": 10.13}, {"time": 10.1333, "x": -80.1, "y": 11.26}, {"time": 10.8, "x": 0, "y": 0}, {"time": 12, "x": -72.09, "y": 10.13}], "scale": [{"time": 0, "x": 0.13, "y": 0.13, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 0.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "x": 1, "y": 1, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 2, "x": 0.13, "y": 0.13, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4667, "x": 1, "y": 1, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 4, "x": 0.13, "y": 0.13, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 4.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4667, "x": 1, "y": 1, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 6, "x": 0.13, "y": 0.13, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 6.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4667, "x": 1, "y": 1, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 8, "x": 0.13, "y": 0.13, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 8.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4667, "x": 1, "y": 1, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 10, "x": 0.13, "y": 0.13, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 10.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.4667, "x": 1, "y": 1, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 12, "x": 0.13, "y": 0.13}]}, "par14": {"translate": [{"time": 0, "x": -22.82, "y": 24.66}, {"time": 0.2, "x": -26.85, "y": 29.01}, {"time": 0.8667, "x": 0, "y": 0}, {"time": 2, "x": -22.82, "y": 24.66}, {"time": 2.2, "x": -26.85, "y": 29.01}, {"time": 2.8667, "x": 0, "y": 0}, {"time": 4, "x": -22.82, "y": 24.66}, {"time": 4.2, "x": -26.85, "y": 29.01}, {"time": 4.8667, "x": 0, "y": 0}, {"time": 6, "x": -22.82, "y": 24.66}, {"time": 6.2, "x": -26.85, "y": 29.01}, {"time": 6.8667, "x": 0, "y": 0}, {"time": 8, "x": -22.82, "y": 24.66}, {"time": 8.2, "x": -26.85, "y": 29.01}, {"time": 8.8667, "x": 0, "y": 0}, {"time": 10, "x": -22.82, "y": 24.66}, {"time": 10.2, "x": -26.85, "y": 29.01}, {"time": 10.8667, "x": 0, "y": 0}, {"time": 12, "x": -22.82, "y": 24.66}], "scale": [{"time": 0, "x": 0.242, "y": 0.242, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 0.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 1, "y": 1, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 2, "x": 0.242, "y": 0.242, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 2.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5333, "x": 1, "y": 1, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 4, "x": 0.242, "y": 0.242, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 4.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5333, "x": 1, "y": 1, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 6, "x": 0.242, "y": 0.242, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 6.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5333, "x": 1, "y": 1, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 8, "x": 0.242, "y": 0.242, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 8.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.5333, "x": 1, "y": 1, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 10, "x": 0.242, "y": 0.242, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 10.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.5333, "x": 1, "y": 1, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 12, "x": 0.242, "y": 0.242}]}, "par15": {"translate": [{"time": 0, "x": 14.55, "y": 37.06}, {"time": 0.2667, "x": 18.19, "y": 46.33}, {"time": 0.9333, "x": 0, "y": 0}, {"time": 2, "x": 14.55, "y": 37.06}, {"time": 2.2667, "x": 18.19, "y": 46.33}, {"time": 2.9333, "x": 0, "y": 0}, {"time": 4, "x": 14.55, "y": 37.06}, {"time": 4.2667, "x": 18.19, "y": 46.33}, {"time": 4.9333, "x": 0, "y": 0}, {"time": 6, "x": 14.55, "y": 37.06}, {"time": 6.2667, "x": 18.19, "y": 46.33}, {"time": 6.9333, "x": 0, "y": 0}, {"time": 8, "x": 14.55, "y": 37.06}, {"time": 8.2667, "x": 18.19, "y": 46.33}, {"time": 8.9333, "x": 0, "y": 0}, {"time": 10, "x": 14.55, "y": 37.06}, {"time": 10.2667, "x": 18.19, "y": 46.33}, {"time": 10.9333, "x": 0, "y": 0}, {"time": 12, "x": 14.55, "y": 37.06}], "scale": [{"time": 0, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 2, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 2.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.6, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 4, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 4.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 6, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 6.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 8, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 8.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.6, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 10, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 10.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.6, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 12, "x": 0.368, "y": 0.368}]}, "par16": {"translate": [{"time": 0, "x": -66.9, "y": 38.65}, {"time": 0.3333, "x": -89.2, "y": 51.53}, {"time": 1, "x": 0, "y": 0}, {"time": 2, "x": -66.9, "y": 38.65}, {"time": 2.3333, "x": -89.2, "y": 51.53}, {"time": 3, "x": 0, "y": 0}, {"time": 4, "x": -66.9, "y": 38.65}, {"time": 4.3333, "x": -89.2, "y": 51.53}, {"time": 5, "x": 0, "y": 0}, {"time": 6, "x": -66.9, "y": 38.65}, {"time": 6.3333, "x": -89.2, "y": 51.53}, {"time": 7, "x": 0, "y": 0}, {"time": 8, "x": -66.9, "y": 38.65}, {"time": 8.3333, "x": -89.2, "y": 51.53}, {"time": 9, "x": 0, "y": 0}, {"time": 10, "x": -66.9, "y": 38.65}, {"time": 10.3333, "x": -89.2, "y": 51.53}, {"time": 11, "x": 0, "y": 0}, {"time": 12, "x": -66.9, "y": 38.65}], "scale": [{"time": 0, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 4, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 4.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 6, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 6.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 8, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 8.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 10, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 10.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 12, "x": 0.5, "y": 0.5}]}, "par17": {"translate": [{"time": 0, "x": 30.01, "y": -27.58}, {"time": 0.4, "x": 42.87, "y": -39.4}, {"time": 1.0667, "x": 0, "y": 0}, {"time": 2, "x": 30.01, "y": -27.58}, {"time": 2.4, "x": 42.87, "y": -39.4}, {"time": 3.0667, "x": 0, "y": 0}, {"time": 4, "x": 30.01, "y": -27.58}, {"time": 4.4, "x": 42.87, "y": -39.4}, {"time": 5.0667, "x": 0, "y": 0}, {"time": 6, "x": 30.01, "y": -27.58}, {"time": 6.4, "x": 42.87, "y": -39.4}, {"time": 7.0667, "x": 0, "y": 0}, {"time": 8, "x": 30.01, "y": -27.58}, {"time": 8.4, "x": 42.87, "y": -39.4}, {"time": 9.0667, "x": 0, "y": 0}, {"time": 10, "x": 30.01, "y": -27.58}, {"time": 10.4, "x": 42.87, "y": -39.4}, {"time": 11.0667, "x": 0, "y": 0}, {"time": 12, "x": 30.01, "y": -27.58}], "scale": [{"time": 0, "x": 0.632, "y": 0.632, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 1, "y": 1, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 2, "x": 0.632, "y": 0.632, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 2.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7333, "x": 1, "y": 1, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 4, "x": 0.632, "y": 0.632, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 4.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7333, "x": 1, "y": 1, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 6, "x": 0.632, "y": 0.632, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 6.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.7333, "x": 1, "y": 1, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 8, "x": 0.632, "y": 0.632, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 8.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.7333, "x": 1, "y": 1, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 10, "x": 0.632, "y": 0.632, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 10.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.7333, "x": 1, "y": 1, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 12, "x": 0.632, "y": 0.632}]}, "par18": {"translate": [{"time": 0, "x": -15.48, "y": -27.58}, {"time": 0.4667, "x": -23.82, "y": -42.43}, {"time": 1.1333, "x": 0, "y": 0}, {"time": 2, "x": -15.48, "y": -27.58}, {"time": 2.4667, "x": -23.82, "y": -42.43}, {"time": 3.1333, "x": 0, "y": 0}, {"time": 4, "x": -15.48, "y": -27.58}, {"time": 4.4667, "x": -23.82, "y": -42.43}, {"time": 5.1333, "x": 0, "y": 0}, {"time": 6, "x": -15.48, "y": -27.58}, {"time": 6.4667, "x": -23.82, "y": -42.43}, {"time": 7.1333, "x": 0, "y": 0}, {"time": 8, "x": -15.48, "y": -27.58}, {"time": 8.4667, "x": -23.82, "y": -42.43}, {"time": 9.1333, "x": 0, "y": 0}, {"time": 10, "x": -15.48, "y": -27.58}, {"time": 10.4667, "x": -23.82, "y": -42.43}, {"time": 11.1333, "x": 0, "y": 0}, {"time": 12, "x": -15.48, "y": -27.58}], "scale": [{"time": 0, "x": 0.758, "y": 0.758, "curve": [0.345, 0.37, 0.757, 1]}, {"time": 0.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 1, "y": 1, "curve": [0.269, 0, 0.618, 0.42]}, {"time": 2, "x": 0.758, "y": 0.758, "curve": [0.345, 0.37, 0.757, 1]}, {"time": 2.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8, "x": 1, "y": 1, "curve": [0.269, 0, 0.618, 0.42]}, {"time": 4, "x": 0.758, "y": 0.758, "curve": [0.345, 0.37, 0.757, 1]}, {"time": 4.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8, "x": 1, "y": 1, "curve": [0.269, 0, 0.618, 0.42]}, {"time": 6, "x": 0.758, "y": 0.758, "curve": [0.345, 0.37, 0.757, 1]}, {"time": 6.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.8, "x": 1, "y": 1, "curve": [0.269, 0, 0.618, 0.42]}, {"time": 8, "x": 0.758, "y": 0.758, "curve": [0.345, 0.37, 0.757, 1]}, {"time": 8.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8, "x": 1, "y": 1, "curve": [0.269, 0, 0.618, 0.42]}, {"time": 10, "x": 0.758, "y": 0.758, "curve": [0.345, 0.37, 0.757, 1]}, {"time": 10.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.8, "x": 1, "y": 1, "curve": [0.269, 0, 0.618, 0.42]}, {"time": 12, "x": 0.758, "y": 0.758}]}, "par19": {"translate": [{"time": 0, "x": 25.46, "y": -11.17}, {"time": 0.5333, "x": 42.43, "y": -18.62}, {"time": 1.2, "x": 0, "y": 0}, {"time": 2, "x": 25.46, "y": -11.17}, {"time": 2.5333, "x": 42.43, "y": -18.62}, {"time": 3.2, "x": 0, "y": 0}, {"time": 4, "x": 25.46, "y": -11.17}, {"time": 4.5333, "x": 42.43, "y": -18.62}, {"time": 5.2, "x": 0, "y": 0}, {"time": 6, "x": 25.46, "y": -11.17}, {"time": 6.5333, "x": 42.43, "y": -18.62}, {"time": 7.2, "x": 0, "y": 0}, {"time": 8, "x": 25.46, "y": -11.17}, {"time": 8.5333, "x": 42.43, "y": -18.62}, {"time": 9.2, "x": 0, "y": 0}, {"time": 10, "x": 25.46, "y": -11.17}, {"time": 10.5333, "x": 42.43, "y": -18.62}, {"time": 11.2, "x": 0, "y": 0}, {"time": 12, "x": 25.46, "y": -11.17}], "scale": [{"time": 0, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 2, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 2.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 4, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 4.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 6, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 6.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 8, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 8.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 10, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 10.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 12, "x": 0.87, "y": 0.87}]}, "par20": {"translate": [{"time": 0, "x": -39.49, "y": 8.57}, {"time": 0.5333, "x": -65.82, "y": 14.29}, {"time": 1.2, "x": 0, "y": 0}, {"time": 2, "x": -39.49, "y": 8.57}, {"time": 2.5333, "x": -65.82, "y": 14.29}, {"time": 3.2, "x": 0, "y": 0}, {"time": 4, "x": -39.49, "y": 8.57}, {"time": 4.5333, "x": -65.82, "y": 14.29}, {"time": 5.2, "x": 0, "y": 0}, {"time": 6, "x": -39.49, "y": 8.57}, {"time": 6.5333, "x": -65.82, "y": 14.29}, {"time": 7.2, "x": 0, "y": 0}, {"time": 8, "x": -39.49, "y": 8.57}, {"time": 8.5333, "x": -65.82, "y": 14.29}, {"time": 9.2, "x": 0, "y": 0}, {"time": 10, "x": -39.49, "y": 8.57}, {"time": 10.5333, "x": -65.82, "y": 14.29}, {"time": 11.2, "x": 0, "y": 0}, {"time": 12, "x": -39.49, "y": 8.57}], "scale": [{"time": 0, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 2, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 2.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 4, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 4.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 6, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 6.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 8, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 8.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 10, "x": 0.87, "y": 0.87, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 10.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.8667, "x": 1, "y": 1, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 12, "x": 0.87, "y": 0.87}]}, "par21": {"translate": [{"time": 0, "x": 26.43, "y": 8.57}, {"time": 0.6, "x": 48.06, "y": 15.59}, {"time": 1.2667, "x": 0, "y": 0}, {"time": 2, "x": 26.43, "y": 8.57}, {"time": 2.6, "x": 48.06, "y": 15.59}, {"time": 3.2667, "x": 0, "y": 0}, {"time": 4, "x": 26.43, "y": 8.57}, {"time": 4.6, "x": 48.06, "y": 15.59}, {"time": 5.2667, "x": 0, "y": 0}, {"time": 6, "x": 26.43, "y": 8.57}, {"time": 6.6, "x": 48.06, "y": 15.59}, {"time": 7.2667, "x": 0, "y": 0}, {"time": 8, "x": 26.43, "y": 8.57}, {"time": 8.6, "x": 48.06, "y": 15.59}, {"time": 9.2667, "x": 0, "y": 0}, {"time": 10, "x": 26.43, "y": 8.57}, {"time": 10.6, "x": 48.06, "y": 15.59}, {"time": 11.2667, "x": 0, "y": 0}, {"time": 12, "x": 26.43, "y": 8.57}], "scale": [{"time": 0, "x": 0.957, "y": 0.957, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "x": 1, "y": 1, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 2, "x": 0.957, "y": 0.957, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 2.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9333, "x": 1, "y": 1, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 4, "x": 0.957, "y": 0.957, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 4.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.9333, "x": 1, "y": 1, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 6, "x": 0.957, "y": 0.957, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 6.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.9333, "x": 1, "y": 1, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 8, "x": 0.957, "y": 0.957, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 8.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.9333, "x": 1, "y": 1, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 10, "x": 0.957, "y": 0.957, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 10.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.9333, "x": 1, "y": 1, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 12, "x": 0.957, "y": 0.957}]}, "par22": {"translate": [{"time": 0, "x": -25.33, "y": 23.17}, {"time": 0.6667, "x": -50.66, "y": 46.33}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 2, "x": -25.33, "y": 23.17}, {"time": 2.6667, "x": -50.66, "y": 46.33}, {"time": 3.3333, "x": 0, "y": 0}, {"time": 4, "x": -25.33, "y": 23.17}, {"time": 4.6667, "x": -50.66, "y": 46.33}, {"time": 5.3333, "x": 0, "y": 0}, {"time": 6, "x": -25.33, "y": 23.17}, {"time": 6.6667, "x": -50.66, "y": 46.33}, {"time": 7.3333, "x": 0, "y": 0}, {"time": 8, "x": -25.33, "y": 23.17}, {"time": 8.6667, "x": -50.66, "y": 46.33}, {"time": 9.3333, "x": 0, "y": 0}, {"time": 10, "x": -25.33, "y": 23.17}, {"time": 10.6667, "x": -50.66, "y": 46.33}, {"time": 11.3333, "x": 0, "y": 0}, {"time": 12, "x": -25.33, "y": 23.17}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 12, "x": 1, "y": 1}]}, "par23": {"translate": [{"time": 0, "x": -3.12, "y": 23.19}, {"time": 0.7333, "x": -6.93, "y": 51.53}, {"time": 1.4, "x": 0, "y": 0}, {"time": 2, "x": -3.12, "y": 23.19}, {"time": 2.7333, "x": -6.93, "y": 51.53}, {"time": 3.4, "x": 0, "y": 0}, {"time": 4, "x": -3.12, "y": 23.19}, {"time": 4.7333, "x": -6.93, "y": 51.53}, {"time": 5.4, "x": 0, "y": 0}, {"time": 6, "x": -3.12, "y": 23.19}, {"time": 6.7333, "x": -6.93, "y": 51.53}, {"time": 7.4, "x": 0, "y": 0}, {"time": 8, "x": -3.12, "y": 23.19}, {"time": 8.7333, "x": -6.93, "y": 51.53}, {"time": 9.4, "x": 0, "y": 0}, {"time": 10, "x": -3.12, "y": 23.19}, {"time": 10.7333, "x": -6.93, "y": 51.53}, {"time": 11.4, "x": 0, "y": 0}, {"time": 12, "x": -3.12, "y": 23.19}], "scale": [{"time": 0, "x": 0.957, "y": 0.957, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 0.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 2, "x": 0.957, "y": 0.957, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 2.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.4, "x": 0, "y": 0, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 4, "x": 0.957, "y": 0.957, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 4.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.4, "x": 0, "y": 0, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 6, "x": 0.957, "y": 0.957, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 6.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.4, "x": 0, "y": 0, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 8, "x": 0.957, "y": 0.957, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 8.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.4, "x": 0, "y": 0, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 10, "x": 0.957, "y": 0.957, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 10.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.4, "x": 0, "y": 0, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 12, "x": 0.957, "y": 0.957}]}, "par24": {"translate": [{"time": 0, "x": -27.3, "y": -11.76}, {"time": 0.8, "x": -68.25, "y": -29.4}, {"time": 1.4667, "x": 0, "y": 0}, {"time": 2, "x": -27.3, "y": -11.76}, {"time": 2.8, "x": -68.25, "y": -29.4}, {"time": 3.4667, "x": 0, "y": 0}, {"time": 4, "x": -27.3, "y": -11.76}, {"time": 4.8, "x": -68.25, "y": -29.4}, {"time": 5.4667, "x": 0, "y": 0}, {"time": 6, "x": -27.3, "y": -11.76}, {"time": 6.8, "x": -68.25, "y": -29.4}, {"time": 7.4667, "x": 0, "y": 0}, {"time": 8, "x": -27.3, "y": -11.76}, {"time": 8.8, "x": -68.25, "y": -29.4}, {"time": 9.4667, "x": 0, "y": 0}, {"time": 10, "x": -27.3, "y": -11.76}, {"time": 10.8, "x": -68.25, "y": -29.4}, {"time": 11.4667, "x": 0, "y": 0}, {"time": 12, "x": -27.3, "y": -11.76}], "scale": [{"time": 0, "x": 0.87, "y": 0.87, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 0.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4667, "x": 0, "y": 0, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 2, "x": 0.87, "y": 0.87, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 2.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.4667, "x": 0, "y": 0, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 4, "x": 0.87, "y": 0.87, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 4.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.4667, "x": 0, "y": 0, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 6, "x": 0.87, "y": 0.87, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 6.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.4667, "x": 0, "y": 0, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 8, "x": 0.87, "y": 0.87, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 8.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.4667, "x": 0, "y": 0, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 10, "x": 0.87, "y": 0.87, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 10.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.4667, "x": 0, "y": 0, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 12, "x": 0.87, "y": 0.87}]}, "par25": {"translate": [{"time": 0, "x": 8.64, "y": -12.73}, {"time": 0.8667, "x": 24.68, "y": -36.37}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2, "x": 8.64, "y": -12.73}, {"time": 2.8667, "x": 24.68, "y": -36.37}, {"time": 3.5333, "x": 0, "y": 0}, {"time": 4, "x": 8.64, "y": -12.73}, {"time": 4.8667, "x": 24.68, "y": -36.37}, {"time": 5.5333, "x": 0, "y": 0}, {"time": 6, "x": 8.64, "y": -12.73}, {"time": 6.8667, "x": 24.68, "y": -36.37}, {"time": 7.5333, "x": 0, "y": 0}, {"time": 8, "x": 8.64, "y": -12.73}, {"time": 8.8667, "x": 24.68, "y": -36.37}, {"time": 9.5333, "x": 0, "y": 0}, {"time": 10, "x": 8.64, "y": -12.73}, {"time": 10.8667, "x": 24.68, "y": -36.37}, {"time": 11.5333, "x": 0, "y": 0}, {"time": 12, "x": 8.64, "y": -12.73}], "scale": [{"time": 0, "x": 0.758, "y": 0.758, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 0.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5333, "x": 0, "y": 0, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 2, "x": 0.758, "y": 0.758, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 2.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5333, "x": 0, "y": 0, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 4, "x": 0.758, "y": 0.758, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 4.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.5333, "x": 0, "y": 0, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 6, "x": 0.758, "y": 0.758, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 6.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 8, "x": 0.758, "y": 0.758, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 8.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.5333, "x": 0, "y": 0, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 10, "x": 0.758, "y": 0.758, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 10.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.5333, "x": 0, "y": 0, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 12, "x": 0.758, "y": 0.758}]}, "par26": {"translate": [{"time": 0, "x": 12.23, "y": 6.28}, {"time": 1, "x": 48.93, "y": 25.11}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 2, "x": 12.23, "y": 6.28}, {"time": 3, "x": 48.93, "y": 25.11}, {"time": 3.6667, "x": 0, "y": 0}, {"time": 4, "x": 12.23, "y": 6.28}, {"time": 5, "x": 48.93, "y": 25.11}, {"time": 5.6667, "x": 0, "y": 0}, {"time": 6, "x": 12.23, "y": 6.28}, {"time": 7, "x": 48.93, "y": 25.11}, {"time": 7.6667, "x": 0, "y": 0}, {"time": 8, "x": 12.23, "y": 6.28}, {"time": 9, "x": 48.93, "y": 25.11}, {"time": 9.6667, "x": 0, "y": 0}, {"time": 10, "x": 12.23, "y": 6.28}, {"time": 11, "x": 48.93, "y": 25.11}, {"time": 11.6667, "x": 0, "y": 0}, {"time": 12, "x": 12.23, "y": 6.28}], "scale": [{"time": 0, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 4, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 4.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 6, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 6.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 8, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 8.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 10, "x": 0.5, "y": 0.5, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 10.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 12, "x": 0.5, "y": 0.5}]}, "par27": {"translate": [{"time": 0, "x": -5.72, "y": -3.03}, {"time": 1.0667, "x": -28.58, "y": -15.15}, {"time": 1.7333, "x": 0, "y": 0}, {"time": 2, "x": -5.72, "y": -3.03}, {"time": 3.0667, "x": -28.58, "y": -15.15}, {"time": 3.7333, "x": 0, "y": 0}, {"time": 4, "x": -5.72, "y": -3.03}, {"time": 5.0667, "x": -28.58, "y": -15.15}, {"time": 5.7333, "x": 0, "y": 0}, {"time": 6, "x": -5.72, "y": -3.03}, {"time": 7.0667, "x": -28.58, "y": -15.15}, {"time": 7.7333, "x": 0, "y": 0}, {"time": 8, "x": -5.72, "y": -3.03}, {"time": 9.0667, "x": -28.58, "y": -15.15}, {"time": 9.7333, "x": 0, "y": 0}, {"time": 10, "x": -5.72, "y": -3.03}, {"time": 11.0667, "x": -28.58, "y": -15.15}, {"time": 11.7333, "x": 0, "y": 0}, {"time": 12, "x": -5.72, "y": -3.03}], "scale": [{"time": 0, "x": 0.368, "y": 0.368, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 0.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7333, "x": 0, "y": 0, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 2, "x": 0.368, "y": 0.368, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 2.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.7333, "x": 0, "y": 0, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 4, "x": 0.368, "y": 0.368, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 4.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 6, "x": 0.368, "y": 0.368, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 6.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.7333, "x": 0, "y": 0, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 8, "x": 0.368, "y": 0.368, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 8.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.7333, "x": 0, "y": 0, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 10, "x": 0.368, "y": 0.368, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 10.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.7333, "x": 0, "y": 0, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 12, "x": 0.368, "y": 0.368}]}, "par28": {"translate": [{"time": 0, "x": 5.54, "y": -3.33}, {"time": 1.2, "x": 55.42, "y": -33.34}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": 5.54, "y": -3.33}, {"time": 3.2, "x": 55.42, "y": -33.34}, {"time": 3.8667, "x": 0, "y": 0}, {"time": 4, "x": 5.54, "y": -3.33}, {"time": 5.2, "x": 55.42, "y": -33.34}, {"time": 5.8667, "x": 0, "y": 0}, {"time": 6, "x": 5.54, "y": -3.33}, {"time": 7.2, "x": 55.42, "y": -33.34}, {"time": 7.8667, "x": 0, "y": 0}, {"time": 8, "x": 5.54, "y": -3.33}, {"time": 9.2, "x": 55.42, "y": -33.34}, {"time": 9.8667, "x": 0, "y": 0}, {"time": 10, "x": 5.54, "y": -3.33}, {"time": 11.2, "x": 55.42, "y": -33.34}, {"time": 11.8667, "x": 0, "y": 0}, {"time": 12, "x": 5.54, "y": -3.33}], "scale": [{"time": 0, "x": 0.13, "y": 0.13, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 0.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8667, "x": 0, "y": 0, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 2, "x": 0.13, "y": 0.13, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 2.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.8667, "x": 0, "y": 0, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 4, "x": 0.13, "y": 0.13, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 4.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.8667, "x": 0, "y": 0, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 6, "x": 0.13, "y": 0.13, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 6.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.8667, "x": 0, "y": 0, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 8, "x": 0.13, "y": 0.13, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 8.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.8667, "x": 0, "y": 0, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 10, "x": 0.13, "y": 0.13, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 10.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.8667, "x": 0, "y": 0, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 12, "x": 0.13, "y": 0.13}]}, "par29": {"translate": [{"time": 0, "x": 0.28, "y": -0.8}, {"time": 1.2667, "x": 5.63, "y": -16.02}, {"time": 1.9333, "x": 0, "y": 0}, {"time": 2, "x": 0.28, "y": -0.8}, {"time": 3.2667, "x": 5.63, "y": -16.02}, {"time": 3.9333, "x": 0, "y": 0}, {"time": 4, "x": 0.28, "y": -0.8}, {"time": 5.2667, "x": 5.63, "y": -16.02}, {"time": 5.9333, "x": 0, "y": 0}, {"time": 6, "x": 0.28, "y": -0.8}, {"time": 7.2667, "x": 5.63, "y": -16.02}, {"time": 7.9333, "x": 0, "y": 0}, {"time": 8, "x": 0.28, "y": -0.8}, {"time": 9.2667, "x": 5.63, "y": -16.02}, {"time": 9.9333, "x": 0, "y": 0}, {"time": 10, "x": 0.28, "y": -0.8}, {"time": 11.2667, "x": 5.63, "y": -16.02}, {"time": 11.9333, "x": 0, "y": 0}, {"time": 12, "x": 0.28, "y": -0.8}], "scale": [{"time": 0, "x": 0.043, "y": 0.043, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 0.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9333, "x": 0, "y": 0, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 2, "x": 0.043, "y": 0.043, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 2.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 4, "x": 0.043, "y": 0.043, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 4.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.9333, "x": 0, "y": 0, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 6, "x": 0.043, "y": 0.043, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 6.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.9333, "x": 0, "y": 0, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 8, "x": 0.043, "y": 0.043, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 8.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.9333, "x": 0, "y": 0, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 10, "x": 0.043, "y": 0.043, "curve": [0.289, 0.17, 0.755, 1]}, {"time": 10.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.9333, "x": 0, "y": 0, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 12, "x": 0.043, "y": 0.043}]}, "par30": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.3333, "x": -40.7, "y": 22.52}, {"time": 2, "x": 0, "y": 0}, {"time": 3.3333, "x": -40.7, "y": 22.52}, {"time": 4, "x": 0, "y": 0}, {"time": 5.3333, "x": -40.7, "y": 22.52}, {"time": 6, "x": 0, "y": 0}, {"time": 7.3333, "x": -40.7, "y": 22.52}, {"time": 8, "x": 0, "y": 0}, {"time": 9.3333, "x": -40.7, "y": 22.52}, {"time": 10, "x": 0, "y": 0}, {"time": 11.3333, "x": -40.7, "y": 22.52}, {"time": 12, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0}]}, "par31": {"translate": [{"time": 0, "x": 54.56, "y": 17.75}, {"time": 0.1, "x": 0, "y": 0}, {"time": 1.4333, "x": 54.56, "y": 17.75, "curve": "stepped"}, {"time": 2, "x": 54.56, "y": 17.75}, {"time": 2.1, "x": 0, "y": 0}, {"time": 3.4333, "x": 54.56, "y": 17.75, "curve": "stepped"}, {"time": 4, "x": 54.56, "y": 17.75}, {"time": 4.1, "x": 0, "y": 0}, {"time": 5.4333, "x": 54.56, "y": 17.75, "curve": "stepped"}, {"time": 6, "x": 54.56, "y": 17.75}, {"time": 6.1, "x": 0, "y": 0}, {"time": 7.4333, "x": 54.56, "y": 17.75, "curve": "stepped"}, {"time": 8, "x": 54.56, "y": 17.75}, {"time": 8.1, "x": 0, "y": 0}, {"time": 9.4333, "x": 54.56, "y": 17.75, "curve": "stepped"}, {"time": 10, "x": 54.56, "y": 17.75}, {"time": 10.1, "x": 0, "y": 0}, {"time": 11.4333, "x": 54.56, "y": 17.75, "curve": "stepped"}, {"time": 12, "x": 54.56, "y": 17.75}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.7667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.7667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.7667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.7667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0}]}, "par32": {"translate": [{"time": 0, "x": 48.5, "y": 8.66}, {"time": 0.3, "x": 0, "y": 0}, {"time": 1.6333, "x": 48.5, "y": 8.66, "curve": "stepped"}, {"time": 2, "x": 48.5, "y": 8.66}, {"time": 2.3, "x": 0, "y": 0}, {"time": 3.6333, "x": 48.5, "y": 8.66, "curve": "stepped"}, {"time": 4, "x": 48.5, "y": 8.66}, {"time": 4.3, "x": 0, "y": 0}, {"time": 5.6333, "x": 48.5, "y": 8.66, "curve": "stepped"}, {"time": 6, "x": 48.5, "y": 8.66}, {"time": 6.3, "x": 0, "y": 0}, {"time": 7.6333, "x": 48.5, "y": 8.66, "curve": "stepped"}, {"time": 8, "x": 48.5, "y": 8.66}, {"time": 8.3, "x": 0, "y": 0}, {"time": 9.6333, "x": 48.5, "y": 8.66, "curve": "stepped"}, {"time": 10, "x": 48.5, "y": 8.66}, {"time": 10.3, "x": 0, "y": 0}, {"time": 11.6333, "x": 48.5, "y": 8.66, "curve": "stepped"}, {"time": 12, "x": 48.5, "y": 8.66}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0}]}, "par33": {"translate": [{"time": 0, "x": -69.28, "y": -17.75}, {"time": 0.5, "x": 0, "y": 0}, {"time": 1.8333, "x": -69.28, "y": -17.75, "curve": "stepped"}, {"time": 2, "x": -69.28, "y": -17.75}, {"time": 2.5, "x": 0, "y": 0}, {"time": 3.8333, "x": -69.28, "y": -17.75, "curve": "stepped"}, {"time": 4, "x": -69.28, "y": -17.75}, {"time": 4.5, "x": 0, "y": 0}, {"time": 5.8333, "x": -69.28, "y": -17.75, "curve": "stepped"}, {"time": 6, "x": -69.28, "y": -17.75}, {"time": 6.5, "x": 0, "y": 0}, {"time": 7.8333, "x": -69.28, "y": -17.75, "curve": "stepped"}, {"time": 8, "x": -69.28, "y": -17.75}, {"time": 8.5, "x": 0, "y": 0}, {"time": 9.8333, "x": -69.28, "y": -17.75, "curve": "stepped"}, {"time": 10, "x": -69.28, "y": -17.75}, {"time": 10.5, "x": 0, "y": 0}, {"time": 11.8333, "x": -69.28, "y": -17.75, "curve": "stepped"}, {"time": 12, "x": -69.28, "y": -17.75}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0}]}, "par34": {"translate": [{"time": 0, "x": 96.26, "y": -20.26}, {"time": 0.0333, "x": 98.72, "y": -20.78}, {"time": 0.7, "x": 0, "y": 0}, {"time": 2, "x": 96.26, "y": -20.26}, {"time": 2.0333, "x": 98.72, "y": -20.78}, {"time": 2.7, "x": 0, "y": 0}, {"time": 4, "x": 96.26, "y": -20.26}, {"time": 4.0333, "x": 98.72, "y": -20.78}, {"time": 4.7, "x": 0, "y": 0}, {"time": 6, "x": 96.26, "y": -20.26}, {"time": 6.0333, "x": 98.72, "y": -20.78}, {"time": 6.7, "x": 0, "y": 0}, {"time": 8, "x": 96.26, "y": -20.26}, {"time": 8.0333, "x": 98.72, "y": -20.78}, {"time": 8.7, "x": 0, "y": 0}, {"time": 10, "x": 96.26, "y": -20.26}, {"time": 10.0333, "x": 98.72, "y": -20.78}, {"time": 10.7, "x": 0, "y": 0}, {"time": 12, "x": 96.26, "y": -20.26}], "scale": [{"time": 0, "x": 0.017, "y": 0.017, "curve": [0.348, 0.66, 0.682, 1]}, {"time": 0.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1, "y": 1, "curve": [0.247, 0, 0.729, 0.91]}, {"time": 2, "x": 0.017, "y": 0.017, "curve": [0.348, 0.66, 0.682, 1]}, {"time": 2.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3667, "x": 1, "y": 1, "curve": [0.247, 0, 0.729, 0.91]}, {"time": 4, "x": 0.017, "y": 0.017, "curve": [0.348, 0.66, 0.682, 1]}, {"time": 4.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3667, "x": 1, "y": 1, "curve": [0.247, 0, 0.729, 0.91]}, {"time": 6, "x": 0.017, "y": 0.017, "curve": [0.348, 0.66, 0.682, 1]}, {"time": 6.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3667, "x": 1, "y": 1, "curve": [0.247, 0, 0.729, 0.91]}, {"time": 8, "x": 0.017, "y": 0.017, "curve": [0.348, 0.66, 0.682, 1]}, {"time": 8.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3667, "x": 1, "y": 1, "curve": [0.247, 0, 0.729, 0.91]}, {"time": 10, "x": 0.017, "y": 0.017, "curve": [0.348, 0.66, 0.682, 1]}, {"time": 10.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.3667, "x": 1, "y": 1, "curve": [0.247, 0, 0.729, 0.91]}, {"time": 12, "x": 0.017, "y": 0.017}]}, "par35": {"translate": [{"time": 0, "x": -10.36, "y": 27.15}, {"time": 0.2333, "x": -12.56, "y": 32.91}, {"time": 0.9, "x": 0, "y": 0}, {"time": 2, "x": -10.36, "y": 27.15}, {"time": 2.2333, "x": -12.56, "y": 32.91}, {"time": 2.9, "x": 0, "y": 0}, {"time": 4, "x": -10.36, "y": 27.15}, {"time": 4.2333, "x": -12.56, "y": 32.91}, {"time": 4.9, "x": 0, "y": 0}, {"time": 6, "x": -10.36, "y": 27.15}, {"time": 6.2333, "x": -12.56, "y": 32.91}, {"time": 6.9, "x": 0, "y": 0}, {"time": 8, "x": -10.36, "y": 27.15}, {"time": 8.2333, "x": -12.56, "y": 32.91}, {"time": 8.9, "x": 0, "y": 0}, {"time": 10, "x": -10.36, "y": 27.15}, {"time": 10.2333, "x": -12.56, "y": 32.91}, {"time": 10.9, "x": 0, "y": 0}, {"time": 12, "x": -10.36, "y": 27.15}], "scale": [{"time": 0, "x": 0.305, "y": 0.305, "curve": [0.382, 0.57, 0.737, 1]}, {"time": 0.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "x": 1, "y": 1, "curve": [0.244, 0, 0.646, 0.59]}, {"time": 2, "x": 0.305, "y": 0.305, "curve": [0.382, 0.57, 0.737, 1]}, {"time": 2.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5667, "x": 1, "y": 1, "curve": [0.244, 0, 0.646, 0.59]}, {"time": 4, "x": 0.305, "y": 0.305, "curve": [0.382, 0.57, 0.737, 1]}, {"time": 4.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5667, "x": 1, "y": 1, "curve": [0.244, 0, 0.646, 0.59]}, {"time": 6, "x": 0.305, "y": 0.305, "curve": [0.382, 0.57, 0.737, 1]}, {"time": 6.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5667, "x": 1, "y": 1, "curve": [0.244, 0, 0.646, 0.59]}, {"time": 8, "x": 0.305, "y": 0.305, "curve": [0.382, 0.57, 0.737, 1]}, {"time": 8.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.5667, "x": 1, "y": 1, "curve": [0.244, 0, 0.646, 0.59]}, {"time": 10, "x": 0.305, "y": 0.305, "curve": [0.382, 0.57, 0.737, 1]}, {"time": 10.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.5667, "x": 1, "y": 1, "curve": [0.244, 0, 0.646, 0.59]}, {"time": 12, "x": 0.305, "y": 0.305}]}, "par36": {"translate": [{"time": 0, "x": 29.81, "y": 21.04}, {"time": 0.4333, "x": 44.17, "y": 31.18}, {"time": 1.1, "x": 0, "y": 0}, {"time": 2, "x": 29.81, "y": 21.04}, {"time": 2.4333, "x": 44.17, "y": 31.18}, {"time": 3.1, "x": 0, "y": 0}, {"time": 4, "x": 29.81, "y": 21.04}, {"time": 4.4333, "x": 44.17, "y": 31.18}, {"time": 5.1, "x": 0, "y": 0}, {"time": 6, "x": 29.81, "y": 21.04}, {"time": 6.4333, "x": 44.17, "y": 31.18}, {"time": 7.1, "x": 0, "y": 0}, {"time": 8, "x": 29.81, "y": 21.04}, {"time": 8.4333, "x": 44.17, "y": 31.18}, {"time": 9.1, "x": 0, "y": 0}, {"time": 10, "x": 29.81, "y": 21.04}, {"time": 10.4333, "x": 44.17, "y": 31.18}, {"time": 11.1, "x": 0, "y": 0}, {"time": 12, "x": 29.81, "y": 21.04}], "scale": [{"time": 0, "x": 0.695, "y": 0.695, "curve": [0.354, 0.41, 0.756, 1]}, {"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "x": 1, "y": 1, "curve": [0.263, 0, 0.618, 0.43]}, {"time": 2, "x": 0.695, "y": 0.695, "curve": [0.354, 0.41, 0.756, 1]}, {"time": 2.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7667, "x": 1, "y": 1, "curve": [0.263, 0, 0.618, 0.43]}, {"time": 4, "x": 0.695, "y": 0.695, "curve": [0.354, 0.41, 0.756, 1]}, {"time": 4.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7667, "x": 1, "y": 1, "curve": [0.263, 0, 0.618, 0.43]}, {"time": 6, "x": 0.695, "y": 0.695, "curve": [0.354, 0.41, 0.756, 1]}, {"time": 6.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.7667, "x": 1, "y": 1, "curve": [0.263, 0, 0.618, 0.43]}, {"time": 8, "x": 0.695, "y": 0.695, "curve": [0.354, 0.41, 0.756, 1]}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.7667, "x": 1, "y": 1, "curve": [0.263, 0, 0.618, 0.43]}, {"time": 10, "x": 0.695, "y": 0.695, "curve": [0.354, 0.41, 0.756, 1]}, {"time": 10.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.7667, "x": 1, "y": 1, "curve": [0.263, 0, 0.618, 0.43]}, {"time": 12, "x": 0.695, "y": 0.695}]}, "par37": {"translate": [{"time": 0, "x": -18.87, "y": 13.87}, {"time": 0.6333, "x": -35.94, "y": 26.41}, {"time": 1.3, "x": 0, "y": 0}, {"time": 2, "x": -18.87, "y": 13.87}, {"time": 2.6333, "x": -35.94, "y": 26.41}, {"time": 3.3, "x": 0, "y": 0}, {"time": 4, "x": -18.87, "y": 13.87}, {"time": 4.6333, "x": -35.94, "y": 26.41}, {"time": 5.3, "x": 0, "y": 0}, {"time": 6, "x": -18.87, "y": 13.87}, {"time": 6.6333, "x": -35.94, "y": 26.41}, {"time": 7.3, "x": 0, "y": 0}, {"time": 8, "x": -18.87, "y": 13.87}, {"time": 8.6333, "x": -35.94, "y": 26.41}, {"time": 9.3, "x": 0, "y": 0}, {"time": 10, "x": -18.87, "y": 13.87}, {"time": 10.6333, "x": -35.94, "y": 26.41}, {"time": 11.3, "x": 0, "y": 0}, {"time": 12, "x": -18.87, "y": 13.87}], "scale": [{"time": 0, "x": 0.983, "y": 0.983, "curve": [0.271, 0.09, 0.753, 1]}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "x": 1, "y": 1, "curve": [0.318, 0, 0.652, 0.34]}, {"time": 2, "x": 0.983, "y": 0.983, "curve": [0.271, 0.09, 0.753, 1]}, {"time": 2.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9667, "x": 1, "y": 1, "curve": [0.318, 0, 0.652, 0.34]}, {"time": 4, "x": 0.983, "y": 0.983, "curve": [0.271, 0.09, 0.753, 1]}, {"time": 4.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.9667, "x": 1, "y": 1, "curve": [0.318, 0, 0.652, 0.34]}, {"time": 6, "x": 0.983, "y": 0.983, "curve": [0.271, 0.09, 0.753, 1]}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.9667, "x": 1, "y": 1, "curve": [0.318, 0, 0.652, 0.34]}, {"time": 8, "x": 0.983, "y": 0.983, "curve": [0.271, 0.09, 0.753, 1]}, {"time": 8.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.9667, "x": 1, "y": 1, "curve": [0.318, 0, 0.652, 0.34]}, {"time": 10, "x": 0.983, "y": 0.983, "curve": [0.271, 0.09, 0.753, 1]}, {"time": 10.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.9667, "x": 1, "y": 1, "curve": [0.318, 0, 0.652, 0.34]}, {"time": 12, "x": 0.983, "y": 0.983}]}, "par38": {"translate": [{"time": 0, "x": 24.52, "y": 8.44}, {"time": 0.8333, "x": 65.38, "y": 22.52}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 24.52, "y": 8.44}, {"time": 2.8333, "x": 65.38, "y": 22.52}, {"time": 3.5, "x": 0, "y": 0}, {"time": 4, "x": 24.52, "y": 8.44}, {"time": 4.8333, "x": 65.38, "y": 22.52}, {"time": 5.5, "x": 0, "y": 0}, {"time": 6, "x": 24.52, "y": 8.44}, {"time": 6.8333, "x": 65.38, "y": 22.52}, {"time": 7.5, "x": 0, "y": 0}, {"time": 8, "x": 24.52, "y": 8.44}, {"time": 8.8333, "x": 65.38, "y": 22.52}, {"time": 9.5, "x": 0, "y": 0}, {"time": 10, "x": 24.52, "y": 8.44}, {"time": 10.8333, "x": 65.38, "y": 22.52}, {"time": 11.5, "x": 0, "y": 0}, {"time": 12, "x": 24.52, "y": 8.44}], "scale": [{"time": 0, "x": 0.816, "y": 0.816, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": [0.242, 0, 0.667, 0.67]}, {"time": 2, "x": 0.816, "y": 0.816, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 2.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5, "x": 0, "y": 0, "curve": [0.242, 0, 0.667, 0.67]}, {"time": 4, "x": 0.816, "y": 0.816, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 4.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.5, "x": 0, "y": 0, "curve": [0.242, 0, 0.667, 0.67]}, {"time": 6, "x": 0.816, "y": 0.816, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 6.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5, "x": 0, "y": 0, "curve": [0.242, 0, 0.667, 0.67]}, {"time": 8, "x": 0.816, "y": 0.816, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 8.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.5, "x": 0, "y": 0, "curve": [0.242, 0, 0.667, 0.67]}, {"time": 10, "x": 0.816, "y": 0.816, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 10.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.5, "x": 0, "y": 0, "curve": [0.242, 0, 0.667, 0.67]}, {"time": 12, "x": 0.816, "y": 0.816}]}, "par39": {"translate": [{"time": 0, "x": -4.38, "y": -5.55}, {"time": 1.0333, "x": -19.48, "y": -24.68}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": -4.38, "y": -5.55}, {"time": 3.0333, "x": -19.48, "y": -24.68}, {"time": 3.7, "x": 0, "y": 0}, {"time": 4, "x": -4.38, "y": -5.55}, {"time": 5.0333, "x": -19.48, "y": -24.68}, {"time": 5.7, "x": 0, "y": 0}, {"time": 6, "x": -4.38, "y": -5.55}, {"time": 7.0333, "x": -19.48, "y": -24.68}, {"time": 7.7, "x": 0, "y": 0}, {"time": 8, "x": -4.38, "y": -5.55}, {"time": 9.0333, "x": -19.48, "y": -24.68}, {"time": 9.7, "x": 0, "y": 0}, {"time": 10, "x": -4.38, "y": -5.55}, {"time": 11.0333, "x": -19.48, "y": -24.68}, {"time": 11.7, "x": 0, "y": 0}, {"time": 12, "x": -4.38, "y": -5.55}], "scale": [{"time": 0, "x": 0.434, "y": 0.434, "curve": [0.37, 0.47, 0.753, 1]}, {"time": 0.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7, "x": 0, "y": 0, "curve": [0.253, 0, 0.621, 0.48]}, {"time": 2, "x": 0.434, "y": 0.434, "curve": [0.37, 0.47, 0.753, 1]}, {"time": 2.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.7, "x": 0, "y": 0, "curve": [0.253, 0, 0.621, 0.48]}, {"time": 4, "x": 0.434, "y": 0.434, "curve": [0.37, 0.47, 0.753, 1]}, {"time": 4.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7, "x": 0, "y": 0, "curve": [0.253, 0, 0.621, 0.48]}, {"time": 6, "x": 0.434, "y": 0.434, "curve": [0.37, 0.47, 0.753, 1]}, {"time": 6.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.7, "x": 0, "y": 0, "curve": [0.253, 0, 0.621, 0.48]}, {"time": 8, "x": 0.434, "y": 0.434, "curve": [0.37, 0.47, 0.753, 1]}, {"time": 8.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.7, "x": 0, "y": 0, "curve": [0.253, 0, 0.621, 0.48]}, {"time": 10, "x": 0.434, "y": 0.434, "curve": [0.37, 0.47, 0.753, 1]}, {"time": 10.3667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.7, "x": 0, "y": 0, "curve": [0.253, 0, 0.621, 0.48]}, {"time": 12, "x": 0.434, "y": 0.434}]}, "par40": {"translate": [{"time": 0, "x": 1.36, "y": -1.69}, {"time": 1.2333, "x": 18.19, "y": -22.52}, {"time": 1.9, "x": 0, "y": 0}, {"time": 2, "x": 1.36, "y": -1.69}, {"time": 3.2333, "x": 18.19, "y": -22.52}, {"time": 3.9, "x": 0, "y": 0}, {"time": 4, "x": 1.36, "y": -1.69}, {"time": 5.2333, "x": 18.19, "y": -22.52}, {"time": 5.9, "x": 0, "y": 0}, {"time": 6, "x": 1.36, "y": -1.69}, {"time": 7.2333, "x": 18.19, "y": -22.52}, {"time": 7.9, "x": 0, "y": 0}, {"time": 8, "x": 1.36, "y": -1.69}, {"time": 9.2333, "x": 18.19, "y": -22.52}, {"time": 9.9, "x": 0, "y": 0}, {"time": 10, "x": 1.36, "y": -1.69}, {"time": 11.2333, "x": 18.19, "y": -22.52}, {"time": 11.9, "x": 0, "y": 0}, {"time": 12, "x": 1.36, "y": -1.69}], "scale": [{"time": 0, "x": 0.083, "y": 0.083, "curve": [0.306, 0.23, 0.756, 1]}, {"time": 0.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9, "x": 0, "y": 0, "curve": [0.294, 0, 0.631, 0.37]}, {"time": 2, "x": 0.083, "y": 0.083, "curve": [0.306, 0.23, 0.756, 1]}, {"time": 2.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9, "x": 0, "y": 0, "curve": [0.294, 0, 0.631, 0.37]}, {"time": 4, "x": 0.083, "y": 0.083, "curve": [0.306, 0.23, 0.756, 1]}, {"time": 4.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.9, "x": 0, "y": 0, "curve": [0.294, 0, 0.631, 0.37]}, {"time": 6, "x": 0.083, "y": 0.083, "curve": [0.306, 0.23, 0.756, 1]}, {"time": 6.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.9, "x": 0, "y": 0, "curve": [0.294, 0, 0.631, 0.37]}, {"time": 8, "x": 0.083, "y": 0.083, "curve": [0.306, 0.23, 0.756, 1]}, {"time": 8.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.9, "x": 0, "y": 0, "curve": [0.294, 0, 0.631, 0.37]}, {"time": 10, "x": 0.083, "y": 0.083, "curve": [0.306, 0.23, 0.756, 1]}, {"time": 10.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.9, "x": 0, "y": 0, "curve": [0.294, 0, 0.631, 0.37]}, {"time": 12, "x": 0.083, "y": 0.083}]}, "bone11": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -30.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 9.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -8.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0, 0.62, 0.59, 1]}, {"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1}]}, "bone16": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -64.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 12.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -12.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0, 0.62, 0.59, 1]}, {"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1}]}, "bone": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": 28.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -9.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 9.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0, 0.62, 0.59, 1]}, {"time": 0.3333, "x": 1, "y": 1}]}, "bone6": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": 61.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -17.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 9.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0, 0.62, 0.59, 1]}, {"time": 0.3333, "x": 1, "y": 1}]}, "you win": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 2.56, "y": 2.56, "curve": [0.255, 0, 1, 0.61]}, {"time": 0.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1}]}, "par": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2667, "x": 0, "y": 0, "curve": [0, 0.51, 0.383, 1]}, {"time": 0.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1}]}, "glow_1": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2667, "x": 0, "y": 0, "curve": [0, 0.6, 0.543, 1]}, {"time": 0.5333, "x": 1, "y": 1}]}, "xinxilan1": {"scale": [{"time": 0, "x": 0, "y": 1, "curve": "stepped"}, {"time": 0.2667, "x": 0, "y": 1, "curve": [0, 0.52, 0.571, 1]}, {"time": 0.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1}]}, "caidai1": {"rotate": [{"time": 0.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 170.41}], "translate": [{"time": 0.1333, "x": 184.1, "y": 381.04, "curve": [0, 0.79, 0.75, 1]}, {"time": 0.7333, "x": 83.95, "y": 555.84, "curve": [0.25, 0, 1, 0.41]}, {"time": 1.4, "x": 7.29, "y": 122.76}], "scale": [{"time": 0.1333, "x": 1, "y": 1}]}, "caidai2": {"rotate": [{"time": 0.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -168.8}], "translate": [{"time": 0.1333, "x": 273.91, "y": -109.01, "curve": [0, 0.81, 0.75, 1]}, {"time": 0.5333, "x": 86.79, "y": 19.26, "curve": [0.25, 0, 1, 0.24]}, {"time": 1.1, "x": -8.78, "y": -212.12}], "scale": [{"time": 0.7333, "x": 1.012, "y": 1.012}]}, "caidai3": {"rotate": [{"time": 0.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 180}], "translate": [{"time": 0.1333, "x": 118.51, "y": -80.5, "curve": [0, 0.81, 0.75, 1]}, {"time": 0.6333, "x": -40.62, "y": 43.97, "curve": [0.25, 0, 1, 0.24]}, {"time": 1.2, "x": -47.78, "y": -176.15}]}, "caidai4": {"rotate": [{"time": 0.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 180}], "translate": [{"time": 0.1333, "x": -293.48, "y": -168.26, "curve": [0, 0.81, 0.75, 1]}, {"time": 0.6333, "x": -117.61, "y": -56.76, "curve": [0.25, 0, 1, 0.24]}, {"time": 1.2, "x": 43.56, "y": -288.95}]}, "caidai5": {"rotate": [{"time": 0.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 180}], "translate": [{"time": 0.1333, "x": -263.29, "y": 12.3, "curve": [0, 0.81, 0.75, 1]}, {"time": 0.6333, "x": -74.28, "y": 32.83, "curve": [0.25, 0, 1, 0.24]}, {"time": 1.2, "x": 46.03, "y": -235.34}]}, "baodian_star1": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": -75.12}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.9333, "x": 83.65, "y": -133.65}], "scale": [{"time": 0.6667, "x": 1, "y": 1}, {"time": 0.9333, "x": 0, "y": 0}]}, "baodian_par1": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": 159.53, "y": -43.5}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par2": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": 65.4, "y": -146.5}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par3": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": 165.01, "y": -133.77}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par4": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": -35.24, "y": -157.8}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par5": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": -124.26, "y": 39.89}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par6": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": 10.41, "y": 104.13}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par7": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": -217.87, "y": -83.3}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par8": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": 349.89, "y": 32.7}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par9": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": 168.08, "y": 109.87}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_par10": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": -214.51, "y": 1.96}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_star2": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": 83.77}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.8333, "x": 146.52, "y": -139.59}], "scale": [{"time": 0.5667, "x": 1, "y": 1}, {"time": 0.8333, "x": 0, "y": 0}]}, "baodian_star3": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": -45.67}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.9333, "x": -84.15, "y": -96.52}], "scale": [{"time": 0.6667, "x": 1, "y": 1}, {"time": 0.9333, "x": 0, "y": 0}]}, "baodian_star4": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": 51.43}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.8667, "x": -191.05, "y": 73.78}], "scale": [{"time": 0.6, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_star5": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": 29.53}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.9333, "x": -24.25, "y": 125.23}], "scale": [{"time": 0.6667, "x": 1, "y": 1}, {"time": 0.9333, "x": 0, "y": 0}]}, "baodian_star6": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": -41.44}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.8667, "x": 76.23, "y": 84.06}], "scale": [{"time": 0.6, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0}]}, "baodian_star7": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": 44.61}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.8333, "x": -111.65, "y": 97.82}], "scale": [{"time": 0.5667, "x": 1, "y": 1}, {"time": 0.8333, "x": 0, "y": 0}]}, "baodian_star8": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": -81.99, "curve": "stepped"}, {"time": 2, "angle": -81.99, "curve": "stepped"}, {"time": 4, "angle": -81.99, "curve": "stepped"}, {"time": 6, "angle": -81.99, "curve": "stepped"}, {"time": 8, "angle": -81.99, "curve": "stepped"}, {"time": 10, "angle": -81.99}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.9, "x": 167.47, "y": 91.65, "curve": "stepped"}, {"time": 2, "x": 167.47, "y": 91.65, "curve": "stepped"}, {"time": 4, "x": 167.47, "y": 91.65, "curve": "stepped"}, {"time": 6, "x": 167.47, "y": 91.65, "curve": "stepped"}, {"time": 8, "x": 167.47, "y": 91.65, "curve": "stepped"}, {"time": 10, "x": 167.47, "y": 91.65}], "scale": [{"time": 0.6333, "x": 1, "y": 1}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}, "baodian_star9": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": -98.68, "curve": "stepped"}, {"time": 2, "angle": -98.68, "curve": "stepped"}, {"time": 4, "angle": -98.68, "curve": "stepped"}, {"time": 6, "angle": -98.68, "curve": "stepped"}, {"time": 8, "angle": -98.68, "curve": "stepped"}, {"time": 10, "angle": -98.68}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.8333, "x": 77.68, "y": 121.41, "curve": "stepped"}, {"time": 2, "x": 77.68, "y": 121.41, "curve": "stepped"}, {"time": 4, "x": 77.68, "y": 121.41, "curve": "stepped"}, {"time": 6, "x": 77.68, "y": 121.41, "curve": "stepped"}, {"time": 8, "x": 77.68, "y": 121.41, "curve": "stepped"}, {"time": 10, "x": 77.68, "y": 121.41}], "scale": [{"time": 0.5667, "x": 1, "y": 1}, {"time": 0.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}, "baodian_star10": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.9333, "angle": -52.71, "curve": "stepped"}, {"time": 2, "angle": -52.71, "curve": "stepped"}, {"time": 4, "angle": -52.71, "curve": "stepped"}, {"time": 6, "angle": -52.71, "curve": "stepped"}, {"time": 8, "angle": -52.71, "curve": "stepped"}, {"time": 10, "angle": -52.71}], "translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.58, 0.75, 1]}, {"time": 0.8667, "x": 210.37, "y": -57.42, "curve": "stepped"}, {"time": 2, "x": 210.37, "y": -57.42, "curve": "stepped"}, {"time": 4, "x": 210.37, "y": -57.42, "curve": "stepped"}, {"time": 6, "x": 210.37, "y": -57.42, "curve": "stepped"}, {"time": 8, "x": 210.37, "y": -57.42, "curve": "stepped"}, {"time": 10, "x": 210.37, "y": -57.42}], "scale": [{"time": 0.6, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}, "baodian_par11": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": -140.15, "y": 87.49, "curve": "stepped"}, {"time": 2, "x": -140.15, "y": 87.49, "curve": "stepped"}, {"time": 4, "x": -140.15, "y": 87.49, "curve": "stepped"}, {"time": 6, "x": -140.15, "y": 87.49, "curve": "stepped"}, {"time": 8, "x": -140.15, "y": 87.49, "curve": "stepped"}, {"time": 10, "x": -140.15, "y": 87.49}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}, "baodian_par12": {"translate": [{"time": 0.1333, "x": 0, "y": 0, "curve": [0, 0.59, 0.75, 1]}, {"time": 0.8667, "x": 96.14, "y": 125.57, "curve": "stepped"}, {"time": 2, "x": 96.14, "y": 125.57, "curve": "stepped"}, {"time": 4, "x": 96.14, "y": 125.57, "curve": "stepped"}, {"time": 6, "x": 96.14, "y": 125.57, "curve": "stepped"}, {"time": 8, "x": 96.14, "y": 125.57, "curve": "stepped"}, {"time": 10, "x": 96.14, "y": 125.57}], "scale": [{"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]