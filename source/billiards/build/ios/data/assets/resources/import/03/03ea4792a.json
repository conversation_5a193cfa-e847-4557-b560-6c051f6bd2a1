[1, ["5fca13b6-a092-4e40-bddd-90ebb154da1c@f9941", "fd0c36d8-11b4-4071-9acf-20a783b46e64"], ["node", "root", "msgLabel", "data", "_spriteFrame", "_font"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent"], 1, 9, 4, 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab"], 1, 1, 12, 4], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["04c26tpAftEdbu0SCIRHVrJ", ["node", "__prefab", "msgLabel"], 3, 1, 4, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["edf98QH2D1AUoCrtQlbxFrU", ["node", "__prefab"], 3, 1, 4], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_overflow", "_enableWrapText", "_isSystemFontUsed", "node", "__prefab"], -3, 1, 4], ["cc.TTFFont", ["_name", "_native"], 1]], [[4, 0, 2], [3, 0, 1, 2, 1], [6, 0, 1, 2, 3, 4, 5, 5], [1, 0, 2], [0, 0, 1, 4, 2, 3, 3], [0, 0, 1, 5, 2, 3, 3], [2, 0, 1, 2, 3, 4, 3], [5, 0, 1, 2, 1], [7, 0, 1, 1], [8, 0, 1, 2, 3, 2], [9, 0, 1, 2, 3, 4, 5, 6, 7, 7], [10, 0, 1, 3]], [[[[3, "toastItem"], [4, "toastItem", 33554432, [-5, -6], [[1, -2, [0, "afoteK7HlCtrGcM6PUaLzF"], [5, 463, 182]], [7, -4, [0, "7eZttRClpPnbdf/XWj/Ktw"], -3]], [2, "a2bh6rq95LFLUdPLcrP4I/", null, null, null, -1, 0]], [6, "msgLabel", 33554432, 1, [[[1, -7, [0, "daRUUXluhE+7qi8Egp4hGl"], [5, 300, 50.4]], -8, [8, -9, [0, "1aT1YIyqZAhoAN8elzrI/q"]]], 4, 1, 4], [2, "a3RI581BtNwLGfDjyvvNii", null, null, null, 1, 0]], [5, "BG_chat80", 33554432, 1, [[1, -10, [0, "faQMehd+VCraAoQKCLT5cL"], [5, 290, 36]], [9, 0, -11, [0, "848QCcIA1IY7T5lzjRMq9N"], 0]], [2, "d6XiFNxU1DVKtOc2yXZlbM", null, null, null, 1, 0]], [10, "", 22, 22, 1, false, false, 2, [0, "2cr3R9fktFfqJZp+9jSoMr"]]], 0, [0, 1, 1, 0, 0, 1, 0, 2, 4, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, 0, 2, 0, -2, 4, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 3, 1, 11], [0, 4], [4, 5], [0, 1]], [[[11, "MontserratBold", "MontserratBold.ttf"], -1], 0, 0, [], [], []]]]