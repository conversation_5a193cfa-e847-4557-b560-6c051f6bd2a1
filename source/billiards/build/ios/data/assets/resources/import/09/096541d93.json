[1, ["fd0c36d8-11b4-4071-9acf-20a783b46e64", "7055f3b7-b41d-46a7-9e71-d55b414def76", "d3aa92f6-1d2a-4060-8983-97cf3acc89ef@6c48a", "b22644f3-2a40-43d7-be63-da1c0e8e0914@6c48a", "0f8b2128-78ae-430f-8ec7-27a69f0eb2b6@f9941", "eff37f2c-9c3f-47c2-8db2-f1d6f809d293@f9941", "ef52a33f-dd06-4a53-9eb7-1a41e475300e@f9941", "ae443b40-b879-473f-8036-943e40aa4e7b@f9941", "4519243f-cc5f-4ac8-be53-067ac614fe71@f9941", "89e28899-9e89-43ef-88e5-e5a67715aa7e@f9941", "178a2582-3c1d-485f-8ad7-02d2a5f6841c@f9941", "ac7956f7-a54d-48e8-adf3-ee304e9a9526@f9941", "6133513c-fb7e-47e4-8df1-21215cd93bc7@f9941", "c3bba95c-0644-4d8b-9c72-d26cd3a638e8@f9941", "278b20f3-d837-4b21-b7a1-3af7eab379c3@f9941", "d086a589-9a48-49fa-925a-b183c5bc2674@f9941", "7cb858c1-b262-4a82-8013-d19e5fa09820@f9941", "88790053-2ebf-4d15-9cb3-4945b09ac603", "c68942a2-f949-4752-be4b-d39072c0489c", "43789abb-c0bf-4517-a0de-25eb179351c6@f9941", "1ce7d901-cd4b-4e75-8fb4-72bc75ce1103@f9941", "5ca2f70a-9def-4c09-b32f-2c2b0bce2d97@f9941"], ["node", "_spriteFrame", "targetInfo", "_font", "target", "source", "root", "_target", "_parent", "asset", "_skeletonData", "test<PERSON><PERSON><PERSON>", "closeBtn", "matchBetCtrl", "matchVsCtrl", "lbName", "spAvatar", "data", "lbRank", "lbBet", "pfSeat"], [["cc.Widget", ["_alignFlags", "_originalWidth", "_alignMode", "_left", "_originalHeight", "_right", "_top", "_enabled", "_isAbsRight", "node", "__prefab", "_target"], -6, 1, 4, 1], ["cc.Label", ["_actualFontSize", "_fontSize", "_string", "_isSystemFontUsed", "_lineHeight", "_enableWrapText", "_overflow", "_enableOutline", "_horizontalAlign", "node", "__prefab", "_color", "_outlineColor", "_font"], -6, 1, 4, 5, 5, 6], ["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_active", "_prefab", "_components", "_parent", "_children", "_lpos"], -2, 4, 9, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_lpos", "_parent", "_children"], 0, 12, 4, 5, 1, 2], ["cc.<PERSON><PERSON>", ["_transition", "_duration", "_zoomScale", "node", "__prefab", "_target", "clickEvents", "_normalColor", "_pressedColor"], 0, 1, 4, 1, 9, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.TargetOverrideInfo", ["propertyPath", "target", "targetInfo", "source", "sourceInfo"], 2, 1, 4, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "mountedComponents", "propertyOverrides", "mountedChil<PERSON>n"], 2, 1, 9, 9, 9], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_premultipliedAlpha", "_preCacheMode", "loop", "node", "__prefab", "_skeletonData"], -2, 1, 4, 6], ["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["ac394eHEe1LZ5JrWo2hFxy/", ["cache", "node", "__prefab", "matchVsCtrl", "matchBetCtrl", "closeBtn", "test<PERSON><PERSON><PERSON>", "pfSeat"], 2, 1, 4, 1, 1, 1, 1, 6], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.TargetInfo", ["localID"], 2], ["cc.MountedChildrenInfo", ["targetInfo", "nodes"], 3, 4, 2], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 9], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["cc.UIOpacity", ["node", "__prefab"], 3, 1, 4], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["97323FPHLdFhLcK/XCsmO6e", ["_size", "nameFormatLength", "node", "__prefab", "spAvatar", "lbName"], 1, 1, 4, 1, 1], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["edf98QH2D1AUoCrtQlbxFrU", ["key", "node", "__prefab"], 2, 1, 4], ["87408Y8hzhMKrgqrApN/pN2", ["node", "__prefab", "matchVsAni", "btnClose"], 3, 1, 4, 1, 1], ["440e4Tz4rRLQ5k+Fpiy9+xn", ["node", "__prefab", "lblCost", "btnAdd", "btnSub"], 3, 1, 4, 1, 1, 1]], [[12, 0, 2], [16, 0, 1, 2, 3, 4, 5, 5], [6, 0, 1, 2, 1], [17, 0, 2], [2, 0, 1, 7, 6, 5, 9, 3], [0, 0, 3, 9, 10, 3], [24, 0, 1, 2, 3], [3, 3, 4, 5, 1], [2, 0, 1, 7, 8, 6, 5, 9, 3], [26, 0, 1, 2, 3], [30, 0, 1, 2, 2], [6, 0, 1, 2, 3, 1], [23, 0, 1, 2, 2], [4, 0, 1, 6, 3, 4, 5, 3], [21, 0, 1, 2, 3], [5, 3, 4, 6, 1], [4, 0, 2, 1, 6, 3, 4, 5, 4], [7, 0, 3, 4, 1, 2, 2], [3, 0, 3, 4, 5, 2], [3, 1, 0, 3, 4, 5, 3], [10, 0, 1, 2, 3, 4, 5], [2, 0, 1, 7, 8, 6, 5, 3], [2, 0, 1, 7, 6, 5, 3], [6, 0, 1, 1], [0, 0, 6, 1, 4, 9, 10, 5], [0, 0, 3, 2, 9, 10, 11, 4], [15, 0, 1, 2, 3, 4, 5, 4], [7, 0, 1, 2, 2], [7, 0, 3, 1, 2, 2], [19, 0, 1, 1], [22, 0, 1, 2, 2], [1, 2, 8, 0, 1, 4, 5, 3, 9, 10, 12, 8], [11, 0, 2], [2, 0, 1, 8, 6, 5, 3], [2, 2, 3, 5, 3], [2, 2, 3, 7, 5, 3], [2, 0, 4, 1, 7, 6, 5, 9, 4], [4, 0, 1, 7, 3, 4, 5, 3], [4, 0, 2, 1, 6, 7, 3, 4, 5, 4], [4, 0, 1, 6, 3, 4, 3], [0, 7, 0, 4, 2, 9, 10, 5], [0, 0, 5, 9, 10, 3], [0, 7, 0, 1, 4, 9, 10, 5], [0, 0, 6, 1, 2, 9, 10, 5], [0, 0, 1, 9, 10, 3], [0, 0, 8, 2, 9, 10, 11, 4], [0, 0, 3, 5, 9, 10, 4], [0, 0, 6, 9, 10, 3], [0, 0, 3, 5, 1, 9, 10, 5], [13, 0, 1, 2, 3, 4, 5, 6, 7, 2], [14, 0, 1, 2, 3, 4, 5, 3], [8, 0, 1, 4, 2, 3, 2], [8, 0, 1, 2, 3, 2], [18, 0, 1, 1], [20, 0, 1, 1], [25, 0, 1, 1], [3, 1, 0, 2, 3, 4, 5, 4], [3, 0, 2, 3, 4, 5, 3], [3, 0, 3, 4, 2], [5, 0, 1, 2, 3, 4, 7, 5, 4], [5, 0, 1, 2, 3, 4, 6, 8, 5, 4], [5, 3, 4, 6, 5, 1], [27, 0, 1, 2, 3, 4, 5, 3], [28, 0, 1, 2, 2], [29, 0, 1, 2, 1], [1, 2, 8, 0, 1, 4, 6, 5, 3, 9, 10, 11, 12, 13, 9], [1, 2, 0, 1, 4, 6, 5, 3, 9, 10, 11, 12, 13, 8], [1, 2, 0, 1, 4, 5, 3, 7, 9, 10, 11, 12, 13, 8], [1, 2, 0, 1, 4, 6, 5, 3, 7, 9, 10, 11, 12, 13, 9], [1, 2, 0, 1, 4, 6, 5, 3, 7, 9, 10, 11, 13, 9], [1, 2, 0, 1, 4, 3, 7, 9, 10, 11, 12, 7], [1, 2, 0, 1, 6, 5, 3, 9, 10, 7], [1, 0, 1, 6, 9, 10, 4], [9, 0, 1, 2, 3, 5, 6, 7, 5], [9, 0, 1, 2, 3, 4, 5, 6, 7, 6], [31, 0, 1, 2, 3, 1], [32, 0, 1, 2, 3, 4, 1]], [[[[20, "pipei", "\npipei.png\nsize: 2048,1024\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\ncg_00000\n  rotate: false\n  xy: 1, 258\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00001\n  rotate: false\n  xy: 258, 515\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00002\n  rotate: false\n  xy: 1, 1\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00003\n  rotate: false\n  xy: 258, 258\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00004\n  rotate: false\n  xy: 515, 515\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00005\n  rotate: false\n  xy: 258, 1\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00006\n  rotate: false\n  xy: 515, 258\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00007\n  rotate: false\n  xy: 772, 515\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00008\n  rotate: false\n  xy: 515, 1\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00009\n  rotate: false\n  xy: 772, 258\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00010\n  rotate: false\n  xy: 1029, 515\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00011\n  rotate: false\n  xy: 772, 1\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00012\n  rotate: false\n  xy: 1029, 258\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00013\n  rotate: false\n  xy: 1286, 515\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00014\n  rotate: false\n  xy: 1029, 1\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00015\n  rotate: false\n  xy: 1286, 258\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00016\n  rotate: false\n  xy: 1543, 515\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ncg_00017\n  rotate: false\n  xy: 1, 515\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\nsweep\n  rotate: false\n  xy: 1286, 1\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\n", ["pipei.png"], {"skeleton": {"hash": "nsbZFg/aW6oy/etPB0esORFvyTA", "spine": "3.6.53", "width": 0, "height": 0, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "pp", "parent": "root"}, {"name": "cg", "parent": "pp", "scaleX": 2, "scaleY": 2}, {"name": "sweep", "parent": "pp"}], "slots": [{"name": "sweep", "bone": "sweep", "blend": "additive"}, {"name": "cg", "bone": "cg", "blend": "additive"}], "skins": {"default": {"cg": {"cg_00000": {"width": 256, "height": 256}, "cg_00001": {"width": 256, "height": 256}, "cg_00002": {"width": 256, "height": 256}, "cg_00003": {"width": 256, "height": 256}, "cg_00004": {"width": 256, "height": 256}, "cg_00005": {"width": 256, "height": 256}, "cg_00006": {"width": 256, "height": 256}, "cg_00007": {"width": 256, "height": 256}, "cg_00008": {"width": 256, "height": 256}, "cg_00009": {"width": 256, "height": 256}, "cg_00010": {"width": 256, "height": 256}, "cg_00011": {"width": 256, "height": 256}, "cg_00012": {"width": 256, "height": 256}, "cg_00013": {"width": 256, "height": 256}, "cg_00014": {"width": 256, "height": 256}, "cg_00015": {"width": 256, "height": 256}, "cg_00016": {"width": 256, "height": 256}, "cg_00017": {"width": 256, "height": 256}}, "sweep": {"sweep": {"width": 256, "height": 256}}}}, "animations": {"idle_1": {"slots": {"sweep": {"attachment": [{"time": 0, "name": "sweep"}]}}, "bones": {"sweep": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": 180}, {"time": 0.6667, "angle": 0}]}}}, "idle_2": {"slots": {"cg": {"attachment": [{"time": 0, "name": "cg_00000"}, {"time": 0.0333, "name": "cg_00001"}, {"time": 0.0667, "name": "cg_00002"}, {"time": 0.1, "name": "cg_00003"}, {"time": 0.1333, "name": "cg_00004"}, {"time": 0.1667, "name": "cg_00005"}, {"time": 0.2, "name": "cg_00006"}, {"time": 0.2333, "name": "cg_00007"}, {"time": 0.2667, "name": "cg_00008"}, {"time": 0.3, "name": "cg_00009"}, {"time": 0.3333, "name": "cg_00010"}, {"time": 0.3667, "name": "cg_00011"}, {"time": 0.4, "name": "cg_00012"}, {"time": 0.4333, "name": "cg_00013"}, {"time": 0.4667, "name": "cg_00014"}, {"time": 0.5, "name": "cg_00015"}, {"time": 0.5333, "name": "cg_00016"}, {"time": 0.5667, "name": "cg_00017"}, {"time": 0.6, "name": null}]}}}}}, [0]]], 0, 0, [0], [-1], [2]], [[[20, "vs", "\nvs.png\nsize: 2048,2048\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nglow_00000\n  rotate: false\n  xy: 1863, 1800\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\nks_00000\n  rotate: false\n  xy: 1, 1672\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00001\n  rotate: false\n  xy: 1, 1415\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00002\n  rotate: false\n  xy: 322, 1672\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00003\n  rotate: false\n  xy: 1, 1158\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00004\n  rotate: false\n  xy: 322, 1415\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00005\n  rotate: false\n  xy: 643, 1672\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00006\n  rotate: false\n  xy: 1, 901\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00007\n  rotate: false\n  xy: 322, 1158\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00008\n  rotate: false\n  xy: 643, 1415\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00009\n  rotate: false\n  xy: 964, 1672\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00010\n  rotate: false\n  xy: 1, 644\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00011\n  rotate: false\n  xy: 322, 901\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00012\n  rotate: false\n  xy: 643, 1158\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00013\n  rotate: false\n  xy: 964, 1415\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00014\n  rotate: false\n  xy: 1285, 1672\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00015\n  rotate: false\n  xy: 1, 387\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\nks_00016\n  rotate: false\n  xy: 1, 387\n  size: 320, 256\n  orig: 320, 256\n  offset: 0, 0\n  index: -1\npar_00001\n  rotate: false\n  xy: 643, 901\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\npar_00003\n  rotate: false\n  xy: 964, 1158\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\npar_00005\n  rotate: false\n  xy: 1285, 1415\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\npar_00007\n  rotate: false\n  xy: 1606, 1672\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\npar_00009\n  rotate: false\n  xy: 1, 130\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\npar_00011\n  rotate: false\n  xy: 322, 387\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\npar_00013\n  rotate: false\n  xy: 579, 644\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\npar_00015\n  rotate: false\n  xy: 900, 901\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\npar_00017\n  rotate: false\n  xy: 1221, 1158\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\npar_00019\n  rotate: false\n  xy: 1542, 1415\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\npar_00021\n  rotate: false\n  xy: 258, 130\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\npar_00023\n  rotate: false\n  xy: 579, 387\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\npar_00025\n  rotate: false\n  xy: 836, 644\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\npar_00027\n  rotate: false\n  xy: 1157, 901\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\npar_00029\n  rotate: false\n  xy: 322, 644\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\nqglow_00000\n  rotate: false\n  xy: 1, 1\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\ns\n  rotate: false\n  xy: 1478, 1286\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\nv\n  rotate: false\n  xy: 515, 258\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\n", ["vs.png"], {"skeleton": {"hash": "YsEMypMz5j6HVH7dw++ulawiNMg", "spine": "3.6.53", "width": 0, "height": 0, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "glow", "parent": "root", "scaleX": 2, "scaleY": 2}, {"name": "par", "parent": "root", "scaleX": 2, "scaleY": 2}, {"name": "s", "parent": "root"}, {"name": "v", "parent": "root"}, {"name": "vs", "parent": "root", "scaleX": 2, "scaleY": 2}], "slots": [{"name": "glow_00000", "bone": "glow"}, {"name": "s", "bone": "s"}, {"name": "v", "bone": "v"}, {"name": "qglow_00000", "bone": "glow"}, {"name": "vs", "bone": "vs", "blend": "additive"}, {"name": "par", "bone": "par", "blend": "additive"}], "skins": {"default": {"glow_00000": {"glow_00000": {"width": 128, "height": 128}}, "par": {"par_00001": {"width": 256, "height": 256}, "par_00003": {"width": 256, "height": 256}, "par_00005": {"width": 256, "height": 256}, "par_00007": {"width": 256, "height": 256}, "par_00009": {"width": 256, "height": 256}, "par_00011": {"width": 256, "height": 256}, "par_00013": {"width": 256, "height": 256}, "par_00015": {"width": 256, "height": 256}, "par_00017": {"width": 256, "height": 256}, "par_00019": {"width": 256, "height": 256}, "par_00021": {"width": 256, "height": 256}, "par_00023": {"width": 256, "height": 256}, "par_00025": {"width": 256, "height": 256}, "par_00027": {"width": 256, "height": 256}, "par_00029": {"width": 256, "height": 256}}, "qglow_00000": {"qglow_00000": {"x": 0.77, "scaleX": 0.5, "scaleY": 0.5, "width": 128, "height": 128}}, "s": {"s": {"width": 128, "height": 128}}, "v": {"v": {"width": 128, "height": 128}}, "vs": {"ks_00000": {"width": 320, "height": 256}, "ks_00001": {"width": 320, "height": 256}, "ks_00002": {"width": 320, "height": 256}, "ks_00003": {"width": 320, "height": 256}, "ks_00004": {"width": 320, "height": 256}, "ks_00005": {"width": 320, "height": 256}, "ks_00006": {"width": 320, "height": 256}, "ks_00007": {"width": 320, "height": 256}, "ks_00008": {"width": 320, "height": 256}, "ks_00009": {"width": 320, "height": 256}, "ks_00010": {"width": 320, "height": 256}, "ks_00011": {"width": 320, "height": 256}, "ks_00012": {"width": 320, "height": 256}, "ks_00013": {"width": 320, "height": 256}, "ks_00014": {"width": 320, "height": 256}, "ks_00015": {"width": 320, "height": 256}, "ks_00016": {"width": 320, "height": 256}}}}, "animations": {"idle_1": {"slots": {"glow_00000": {"attachment": [{"time": 0.2, "name": "glow_00000"}]}, "par": {"attachment": [{"time": 0.2, "name": "par_00001"}, {"time": 0.2667, "name": "par_00003"}, {"time": 0.3333, "name": "par_00005"}, {"time": 0.4, "name": "par_00007"}, {"time": 0.4667, "name": "par_00009"}, {"time": 0.5333, "name": "par_00011"}, {"time": 0.6, "name": "par_00013"}, {"time": 0.6667, "name": "par_00015"}, {"time": 0.7333, "name": "par_00017"}, {"time": 0.8, "name": "par_00019"}, {"time": 0.8667, "name": "par_00021"}, {"time": 0.9333, "name": "par_00023"}, {"time": 1, "name": "par_00025"}, {"time": 1.0667, "name": "par_00027"}, {"time": 1.1333, "name": "par_00029"}]}, "qglow_00000": {"attachment": [{"time": 0.2, "name": "qglow_00000"}]}, "s": {"attachment": [{"time": 0, "name": "s"}]}, "v": {"attachment": [{"time": 0, "name": "v"}]}, "vs": {"attachment": [{"time": 0.1667, "name": "ks_00000"}, {"time": 0.2, "name": "ks_00001"}, {"time": 0.2333, "name": "ks_00002"}, {"time": 0.2667, "name": "ks_00003"}, {"time": 0.3, "name": "ks_00004"}, {"time": 0.3333, "name": "ks_00005"}, {"time": 0.3667, "name": "ks_00006"}, {"time": 0.4, "name": "ks_00007"}, {"time": 0.4333, "name": "ks_00008"}, {"time": 0.4667, "name": "ks_00009"}, {"time": 0.5, "name": "ks_00010"}, {"time": 0.5333, "name": "ks_00011"}, {"time": 0.5667, "name": "ks_00012"}, {"time": 0.6, "name": "ks_00013"}, {"time": 0.6333, "name": "ks_00014"}, {"time": 0.6667, "name": "ks_00015"}, {"time": 0.7, "name": "ks_00016"}, {"time": 0.7333, "name": null}]}}, "bones": {"v": {"translate": [{"time": 0, "x": -483.59, "y": 0, "curve": [0, 0.68, 0.75, 1]}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "s": {"translate": [{"time": 0, "x": 438.48, "y": 0, "curve": [0, 0.68, 0.75, 1]}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "vs": {"translate": [{"time": 0.1667, "x": 26.76, "y": 22.37}]}}}, "idle_2": {"slots": {"glow_00000": {"attachment": [{"time": 0, "name": "glow_00000"}]}, "par": {"attachment": [{"time": 0, "name": "par_00001"}, {"time": 0.0667, "name": "par_00003"}, {"time": 0.1333, "name": "par_00005"}, {"time": 0.2, "name": "par_00007"}, {"time": 0.2667, "name": "par_00009"}, {"time": 0.3333, "name": "par_00011"}, {"time": 0.4, "name": "par_00013"}, {"time": 0.4667, "name": "par_00015"}, {"time": 0.5333, "name": "par_00017"}, {"time": 0.6, "name": "par_00019"}, {"time": 0.6667, "name": "par_00021"}, {"time": 0.7333, "name": "par_00023"}, {"time": 0.8, "name": "par_00025"}, {"time": 0.8667, "name": "par_00027"}, {"time": 0.9333, "name": "par_00029"}, {"time": 1, "name": "par_00001"}]}, "qglow_00000": {"attachment": [{"time": 0, "name": "qglow_00000"}]}, "s": {"attachment": [{"time": 0, "name": "s"}]}, "v": {"attachment": [{"time": 0, "name": "v"}]}}}}}, [0]]], 0, 0, [0], [-1], [3]], [[[32, "matchVs"], [33, "matchVs", 33554432, [-23], [[2, -16, [0, "e64p4QHLRKQZi9G52Cma1B"], [5, 749.9999999999999, 1334]], [40, false, 45, 1624, 1, -17, [0, "228PEuMR5AlonA3JxbEhLe"]], [49, true, -22, [0, "13G6N+6mBBHaAByzLDdjD9"], -21, -20, -19, -18, 24]], [50, "69QjWxNXpIppmxpRPODO39", null, -15, 0, [[27, ["MatchSeat1"], -3, [3, ["13zOtgiYlGfJ2xVQ3jl3NU"]]], [27, ["MatchSeat2"], -4, [3, ["13zOtgiYlGfJ2xVQ3jl3NU"]]], [17, ["profile"], -6, [3, ["13zOtgiYlGfJ2xVQ3jl3NU"]], -5, [3, ["a9fYCvaHtIxYV+fNgF/VcD"]]], [17, ["matching"], -8, [3, ["13zOtgiYlGfJ2xVQ3jl3NU"]], -7, [3, ["92BkQWm7pPVJl3/yYn1mRv"]]], [17, ["matching<PERSON>ni"], -10, [3, ["13zOtgiYlGfJ2xVQ3jl3NU"]], -9, [3, ["e8PqUbNhJMmJPxp52eBxjS"]]], [28, ["MatchSeat1"], -12, -11, [3, ["13zOtgiYlGfJ2xVQ3jl3NU"]]], [28, ["MatchSeat2"], -14, -13, [3, ["13zOtgiYlGfJ2xVQ3jl3NU"]]]], [-1, -2]]], [34, 0, {}, [26, "30HHAvjstCwLW6m/p4LsTs", null, null, -32, [51, "d2fjpq8e5NSb63Cc/KrAjS", 1, [[53, [3, ["14Yup30UFIuLHfJDvqK0gX"]], [-31]]], [[29, [3, ["30HHAvjstCwLW6m/p4LsTs"]], [[41, 32, 113.37, -29, [0, "6eNXZCbUtOCI+iN3qIBhC0"]]]], [54, [3, ["01mSM95BRAbqXOkZ+E3fMP"]], [-30]]], [[14, "seat2", ["_name"], [3, ["30HHAvjstCwLW6m/p4LsTs"]]], [30, ["_lpos"], [3, ["30HHAvjstCwLW6m/p4LsTs"]], [1, 205.62999999999994, -414.343, 0]], [30, ["_lrot"], [3, ["30HHAvjstCwLW6m/p4LsTs"]], [3, 0, 0, 0, 1]], [12, ["_euler"], -24, [1, 0, 0, 0]], [14, "", ["_string"], [3, ["d1NJnq/9NOj6DjxRosyHN6"]]], [12, ["_color"], -25, [4, 4294967295]], [14, true, ["_active"], [3, ["92BkQWm7pPVJl3/yYn1mRv"]]], [6, 28.821589205397302, ["_actualFontSize"], -26], [6, true, ["_active"], -27], [6, false, ["_isBold"], -28]]], 15]], [37, "betView", 33554432, [-36, -37, -38, -39], [[[11, -33, [0, "a0SMm+xBVA7J2Fzj6UgW0S"], [5, 749.9999999999999, 834], [0, 0.5, 1]], [24, 45, 500, 100, 100, -34, [0, "e3MdY+WYVDdpUjLQg+k8SG"]], -35], 4, 4, 1], [1, "2ehb7hPq9D26GePfxXiC0s", null, null, null, 1, 0], [1, 0, 167, 0]], [21, "box", 33554432, 1, [-43, -44, 3, -45, -46], [[2, -40, [0, "04Bpiv2xFCSa9DzqyRUjVn"], [5, 749.9999999999999, 1334]], [55, -41, [0, "b9CBSuPxZHA72O9jJbCejL"]], [42, false, 45, 750, 1344, -42, [0, "541KdO/wFEw7oNbx5tqspz"]]], [1, "c61vxTMP1FoaPzmHtPnnKW", null, null, null, 1, 0]], [8, "topBar", 33554432, 4, [-49, -50, -51], [[11, -47, [0, "73r327Dm1PX6EqoQvK3wCT"], [5, 749.9999999999999, 100], [0, 0.5, 1]], [43, 41, 40.04499999999993, 100, 1, -48, [0, "11/tTJJ2dCkrKn1EWeQCCg"]]], [1, "0331PvoYxML7Zomq8Wp0OJ", null, null, null, 1, 0], [1, 0, 626.955, 0]], [8, "betMid", 33554432, 3, [-54, -55, -56, -57, -58, -59], [[2, -52, [0, "6ciFMcW/lLOJ1BfrSFzF0A"], [5, 749.9999999999999, 500]], [44, 40, 100, -53, [0, "afqRDq4NFB+ISj2pn/SawV"]]], [1, "b60G2T3cpD0IEWRaxU7JC/", null, null, null, 1, 0], [1, 0, -201.768, 0]], [8, "btnStart", 33554432, 3, [-66], [[2, -60, [0, "49e/R5MeZDHY7vteGDY5jF"], [5, 280, 125]], [56, 1, 0, false, -61, [0, "6edHZ2a9NLGpII5Xt6t/6O"], 8], [59, 3, 0, 0.94, -63, [0, "fcFUMwXR1CoY7pdA3aU8Ml"], [4, 4292269782], -62], [60, 3, 0, 0.94, -65, [0, "2222EJhVxFFokUTHjLq25c"], [[9, "ac394eHEe1LZ5JrWo2hFxy/", "onStartBtn", 1]], [4, 4291611852], -64]], [1, "89FB96QqhLsamPDtoG4Ssj", null, null, null, 1, 0], [1, -2.407, -702.685, 0]], [38, "vsView", false, 33554432, 4, [-70, 2, -71, -72], [[[11, -67, [0, "5bB7MiwTBNf7MyPWant+gd"], [5, 749.9999999999999, 834], [0, 0.5, 1]], [24, 45, 500, 100, 100, -68, [0, "73TV4ts2BFWp+mw52uypt6"]], -69], 4, 4, 1], [1, "ab8mMEn3NObY6qJZ6DLsla", null, null, null, 1, 0], [1, 0, 167, 0]], [8, "rankNode", 33554432, 8, [-74, -75, -76, -77, -78, -79], [[2, -73, [0, "9dsu8zlh5Jor8X1L/rZw8t"], [5, 500, 100]]], [1, "6e4QXEBJhIhZeIQ6VT0Hkj", null, null, null, 1, 0], [1, 0.789, -17.333, 0]], [4, "close", 33554432, 5, [[2, -80, [0, "0clSK0hnFFp4uoip2nXh9v"], [5, 107, 101]], [7, -81, [0, "51kLA8HjtGR5XZToyWG81m"], 3], [45, 34, false, 1, -82, [0, "28lhkgzt5MNaXvqXF/SWSr"], 5], [15, -83, [0, "d7kOobckBEsJ340W5RYvjE"], [[9, "ac394eHEe1LZ5JrWo2hFxy/", "onCloseView", 1]]]], [1, "7bSD/KHa9BJ6vSQAC10r2a", null, null, null, 1, 0], [1, 321.49999999999994, -50, 0]], [8, "profile", 33554432, 2, [-88, -89, -90, -91, -92], [[2, -84, [0, "b6PlbgbiNL848ETkrgwUe7"], [5, 112, 112]], [62, 112, 11, -87, [0, "a9fYCvaHtIxYV+fNgF/VcD"], -86, -85]], [1, "14Yup30UFIuLHfJDvqK0gX", null, null, null, 1, 0], [1, 0, 17.885, 0]], [16, "add", false, 33554432, 6, [[[2, -93, [0, "7dfwcfRo9DLbdTZ2Wpjyjk"], [5, 78, 78]], [7, -94, [0, "8eEj2KDCpPWYhnQT0lPbye"], 11], -95, [46, 32, 612.4300000000001, 59.569999999999936, -96, [0, "81cYM5Ib5EU7QpnfjMOhtK"]]], 4, 4, 1, 4], [1, "794VWvHNFM5qUTQLRH5XRh", null, null, null, 1, 0], [1, 276.43000000000006, -13.365000000000009, 0]], [35, 0, {}, 8, [26, "30HHAvjstCwLW6m/p4LsTs", null, null, -106, [52, "49cAo55jRGorGC2TuD2EvD", 1, [[29, [3, ["30HHAvjstCwLW6m/p4LsTs"]], [[5, 8, 125.00399999999999, -105, [0, "17RmrvoWBLBoeu7LN9Do+r"]]]]], [[6, "seat1", ["_name"], -97], [12, ["_lpos"], -98, [1, -193.99599999999995, -410.93100000000004, 0]], [12, ["_lrot"], -99, [3, 0, 0, 0, 1]], [12, ["_euler"], -100, [1, 0, 0, 0]], [14, false, ["_active"], [3, ["92BkQWm7pPVJl3/yYn1mRv"]]], [6, "", ["_string"], -101], [6, 28.821589205397302, ["_actualFontSize"], -102], [6, true, ["_active"], -103], [6, false, ["_isBold"], -104]]], 14]], [8, "match_logo", 33554432, 4, [-110], [[2, -107, [0, "c5wUsz3X5KwIJcZAdKYODG"], [5, 390, 210]], [7, -108, [0, "55FjRymNtFZa6c47nPTAN1"], 1], [47, 1, 241.308, -109, [0, "c9romB8tVJi7EB/exvQlJ+"]]], [1, "f60cvCt8ZCFJVVL639ZY/r", null, null, null, 1, 0], [1, -1.239, 320.692, 0]], [4, "setting", 33554432, 5, [[2, -111, [0, "6fof3GG7RC0K1LcABt6+ZQ"], [5, 61, 62]], [57, 0, false, -112, [0, "1b6K9A89pChZ2b5C8kXVj6"], 2], [25, 10, 104.58799999999997, 1, -113, [0, "ddtgmQna1PMIXp9jluyjK/"], 5], [15, -114, [0, "86NM2A+05KMpUdZX2yAYEU"], [[9, "ac394eHEe1LZ5JrWo2hFxy/", "onShowSettings", 1]]]], [1, "10IkVCdutHTq8oDndLjyoL", null, null, null, 1, 0], [1, -239.91199999999998, -50, 0]], [4, "help", 33554432, 5, [[2, -115, [0, "d4P0z50JJI54/MPFOFcGsm"], [5, 61, 61]], [18, 0, -116, [0, "cbgPP8dtRFv6dzMxg1URUV"], 4], [25, 10, 26.093999999999994, 1, -117, [0, "dcnQkqJx9DSJcaRHlY7UgJ"], 5], [15, -118, [0, "b0TL5RY6RAXoWS/VWhMM3x"], [[9, "ac394eHEe1LZ5JrWo2hFxy/", "onShowRule", 1]]]], [1, "a0hozmbDVOPJBs81nEfxvB", null, null, null, 1, 0], [1, -318.40599999999995, -50, 0]], [16, "minus", false, 33554432, 6, [[[2, -119, [0, "41QoESwjZKiJwoRghDq9BO"], [5, 78, 78]], [7, -120, [0, "8f1E7HDlxGt4eclS2JEE1b"], 12], -121, [48, 8, 65.02499999999998, 606.975, 78, -122, [0, "e1ixVV2K9Lrr83SEDn5C7q"]]], 4, 4, 1, 4], [1, "7fOCZT7i1MAIXWtNhiqetH", null, null, null, 1, 0], [1, -270.975, -13.365000000000009, 0]], [3, ["30HHAvjstCwLW6m/p4LsTs"]], [21, "mask", 33554432, 11, [-126], [[2, -123, [0, "e0KdkVfZpMa4MQl/UxaeR3"], [5, 138, 138]], [63, 1, -124, [0, "a0gkydRIlMl72BiVNmMLcg"]], [64, -125, [0, "efSNCovXRG6IsJTgMyLMqJ"], [4, 16777215]]], [1, "4f4hSYUcRAeq5d/ak3Aowr", null, null, null, 1, 0]], [4, "lbRankTittle", 33554432, 9, [[2, -127, [0, "33R5OiGr1DW7l+UvlV3c8O"], [5, 150, 88.2]], [65, "RANK 1", 0, 34, 34, 70, 1, false, false, -128, [0, "601ScaP8pHm58GUWDJgDuu"], [4, 4284147192], [4, 4279653530], 22], [5, 8, 162.208, -129, [0, "55Z+8z2TRATpm+NpuFzn30"]], [10, "RANK_1", -130, [0, "7bz9aHGAlO8LFpz662USpu"]]], [1, "b71TN9EZFDfoowYJV6Uyd0", null, null, null, 1, 0], [1, -12.792000000000002, -4.205, 0]], [4, "lbBetTittle", 33554432, 9, [[2, -131, [0, "7162zx56pMspbU/WcI88vI"], [5, 200, 88.2]], [66, "Bets", 47, 47, 70, 1, false, false, -132, [0, "17UNuk/VxJgbG8K5zq4Xkr"], [4, 4284147192], [4, 4279653530], 23], [5, 8, 88.586, -133, [0, "6ccVahplZA6YQ9nefmrDwT"]], [10, "Bet", -134, [0, "4dQgn2/GdGa4SXbrJi2Zl4"]]], [1, "6fjx+w0cxGWrHE0ODtBd95", null, null, null, 1, 0], [1, -61.41399999999999, -98.511, 0]], [4, "title", 33554432, 14, [[2, -135, [0, "4a/hTHIqpAcbG8tZr5jNRz"], [5, 238.759765625, 59.44]], [67, "8 Ball Pool", 44, 44, 44, false, false, true, -136, [0, "a2+irHUidLwYRekf9aKqRA"], [4, 4278216133], [4, 4285526271], 0], [10, "ball8", -137, [0, "8aKssd3PdPIbK73cl9UDb6"]]], [1, "23mmNxN45K5arR5WkAXXTV", null, null, null, 1, 0], [1, 0, -51.747, 0]], [4, "Label", 33554432, 7, [[2, -138, [0, "64XNOgeslDl4lRIXrKyk3B"], [5, 200, 60.7]], [68, "Start", 42, 42, 45, 1, false, false, true, -139, [0, "73m7OXHwlNmLtK2soohGeN"], [4, 4279653530], [4, 4286184191], 7], [10, "Start", -140, [0, "579oj8275P8aZUlQWyJBNH"]]], [1, "04c22HzJdMqrnaWA/UzFzu", null, null, null, 1, 0], [1, -0.659, 4.5649999999999995, 0]], [4, "coin", 33554432, 6, [[2, -141, [0, "a4fc6ep31Mrru5146eAwYj"], [5, 75, 75]], [18, 0, -142, [0, "f0jpRnS9hBiZGFLb6dA1Oc"], 10], [5, 8, 259.767, -143, [0, "d3a1ABfUNGe5g4y+6aIfyB"]]], [1, "5cDkILC1tKs50a6gOEv1Go", null, null, null, 1, 0], [1, -77.73299999999995, -13.365000000000009, 0]], [13, "lblCost", 33554432, 6, [[[2, -144, [0, "6eKlAOd6hHmINN7lV6ER9Y"], [5, 4, 92.2]], -145, [5, 8, 402.753, -146, [0, "2fy3tCMX1KEoWQnXJWMV8s"]]], 4, 1, 4], [1, "9dDFz3EWpOkYIyUFwYQ4om", null, null, null, 1, 0], [1, 29.753000000000043, -13.365000000000009, 0]], [4, "title", 33554432, 6, [[2, -147, [0, "d2eHJ75yRPUb+RBPTHpIkA"], [5, 400, 59.44]], [69, "Bets", 44, 44, 44, 1, false, false, true, -148, [0, "76NUnx0G1DB5h86e5Dn9CY"], [4, 4287424511], 13], [10, "bets", -149, [0, "daSJXzsNBFEZG0I7m51vB7"]]], [1, "e5lPn+IR9Kd5DadDtzqWC7", null, null, null, 1, 0], [1, 0, 141.936, 0]], [4, "pipei", 33554432, 11, [[23, -150, [0, "9cJBhMkUtF44cHwd9/lLf7"]], [73, "default", "idle_1", false, 0, -151, [0, "e8PqUbNhJMmJPxp52eBxjS"], 18]], [1, "f3gQN/nRtA0Ig3AaJXYxhV", null, null, null, 1, 0], [1, 4.372, 2.937, 0]], [13, "lblName", 33554432, 11, [[[2, -152, [0, "4aXxcoe/pL07xMO5soEalX"], [5, 200, 50]], -153, -154], 4, 1, 1], [1, "01mSM95BRAbqXOkZ+E3fMP", null, null, null, 1, 0], [1, 0, -97.4, 0]], [36, "vs", false, 33554432, 8, [[23, -155, [0, "a4qiGFFxBCs5lkZgscZGoX"]], [74, "default", "idle_1", false, 0, false, -156, [0, "dc9scObO5EmJs/BHHOIU6R"], 19]], [1, "45XM2aNLBCDKjRTVS+RPxS", null, null, null, 1, 0], [1, -4.144999999999982, -418.274, 0]], [4, "coin", 33554432, 9, [[2, -157, [0, "46hZHQcjNBq5MAAxN0p+ph"], [5, 103, 97]], [18, 0, -158, [0, "d6OWBUIXFAAabe4dg083/y"], 21], [5, 8, 39.226, -159, [0, "58mr+9H9NPX4XdqiLy6Jdc"]]], [1, "49J5uWWDdFR6ooP56LgFsH", null, null, null, 1, 0], [1, -159.274, -5.935, 0]], [13, "lbRank", 33554432, 9, [[[11, -160, [0, "61eedXGvBPBqiCq/NZtcgn"], [5, 111.2470703125, 88.2], [0, 0, 0.5]], -161, [5, 8, 314.402, -162, [0, "a97a4Ak6FKM4QqWm2RcWWL"]]], 4, 1, 4], [1, "3dNek/tcZBk4wYiqJDJ7mg", null, null, null, 1, 0], [1, 64.40199999999999, -5.462, 0]], [13, "lbBet", 33554432, 9, [[[11, -163, [0, "2d674oBl9MsawhWI7IwuRB"], [5, 84, 88.2], [0, 0, 0.5]], -164, [5, 8, 285.866, -165, [0, "19R/VZ8BtHvbKO8lTulCB4"]]], 4, 1, 4], [1, "b7eJSxqQZKI6/hWMFkgU6S", null, null, null, 1, 0], [1, 35.865999999999985, -99.768, 0]], [75, 8, [0, "01J/iax5hM8K8UewEfGi9a"], 29, 10], [4, "bg_dialog", 33554432, 3, [[2, -166, [0, "a75+xLfHxG2Kd6PR/vfBnR"], [5, 710, 310]], [19, 1, 0, -167, [0, "0er8B1CaxODYTSs2JKSGYN"], 5]], [1, "ae6aqrwhJJSJ6QS8Gm/l9d", null, null, null, 1, 0], [1, 0, -185.435, 0]], [4, "bg_dialogIn", 33554432, 3, [[2, -168, [0, "976RRixcRPJI/wpyk6x+FK"], [5, 680, 240]], [19, 1, 0, -169, [0, "d6Dm3fIXJEC4zz3eKwzIji"], 6]], [1, "72l88ip2dC6ZzfsytxiLXF", null, null, null, 1, 0], [1, 0, -205.975, 0]], [4, "bets_bg", 33554432, 6, [[2, -170, [0, "633q/e6fxEEJa1GOlaimUS"], [5, 430, 81]], [19, 1, 0, -171, [0, "91/XaQYidAwY/w7rUT9dTU"], 9]], [1, "d4nGZPFLZPybseM64A1kCi", null, null, null, 1, 0], [1, 0, -13.365000000000009, 0]], [3, ["d1NJnq/9NOj6DjxRosyHN6"]], [39, "spAvatar", 33554432, 19, [[[2, -172, [0, "46yqV1nNxOkJlCwNeuVNw5"], [5, 138, 138]], -173], 4, 1], [1, "73Y1fqidpKg64BHMd/b9+D", null, null, null, 1, 0]], [22, "matching", 33554432, 11, [[2, -174, [0, "81ZdA8MjlHwpUL84tSr+bY"], [5, 130, 130]], [7, -175, [0, "c8tmc/AVtLuol9tOmINRIJ"], 16]], [1, "92BkQWm7pPVJl3/yYn1mRv", null, null, null, 1, 0]], [22, "head_cr", 33554432, 11, [[2, -176, [0, "59sEDk7LNBe4ixUsdCPiSc"], [5, 150, 150]], [7, -177, [0, "010c1SWUlMIomscbdrqQnE"], 17]], [1, "c2BUJV7YVE379QVMrnFYbg", null, null, null, 1, 0]], [3, ["d1NJnq/9NOj6DjxRosyHN6"]], [4, "match_rank_bg", 33554432, 9, [[2, -178, [0, "7eczIb/JFCJKNqSe7ylta9"], [5, 422, 81]], [7, -179, [0, "15Ap3jxOhF4I/gDH/wGzWu"], 20]], [1, "02anuJokNGJp686Kdf+ND8", null, null, null, 1, 0], [1, 12.746, -5.03, 0]], [16, "test<PERSON><PERSON><PERSON>", false, 33554432, 4, [[[2, -180, [0, "35zO8LvuBDfJuhJsNrFYT4"], [5, 700, 50.4]], -181], 4, 1], [1, "81l8wB6/tKn6c2MF1/IPel", null, null, null, 1, 0], [1, 0, 480.699, 0]], [61, 12, [0, "85rFXQFnBEa61nPeuYjr4u"], [[9, "440e4Tz4rRLQ5k+Fpiy9+xn", "onClickAdd", 3]], 12], [15, 17, [0, "3efS5OYFRDYql36ztpccsP"], [[9, "440e4Tz4rRLQ5k+Fpiy9+xn", "onClickSub", 3]]], [70, "", 45, 45, 70, false, true, 25, [0, "87mo2GiCJKSppHvYV9W/yL"], [4, 4284147192], [4, 4279653530]], [76, 3, [0, "94tQLgJoRCIaTlIh2Neuvd"], 46, 44, 45], [58, 0, 38, [0, "bet3twL5xA7auwsCEabWd8"]], [71, "", 28.821589205397302, 24, 1, false, false, 28, [0, "d1NJnq/9NOj6DjxRosyHN6"]], [10, "Matching", 28, [0, "7eQ52107ZGj6oTeqKsHYqt"]], [3, ["30HHAvjstCwLW6m/p4LsTs"]], [31, "190011", 0, 34, 34, 70, false, false, 31, [0, "2434M8ezdP9qKV3P2de6My"], [4, 4279653530]], [31, "500", 0, 42, 42, 70, false, false, 32, [0, "0bjljVqSJMfasRZSyDtpKt"], [4, 4279653530]], [72, 24, 24, 3, 43, [0, "0b6UmNPfRJcZ/fDcFrk7Ef"]]], 0, [0, -1, 2, 0, -2, 13, 0, 4, 13, 0, 4, 2, 0, 4, 2, 0, 5, 2, 0, 4, 2, 0, 5, 2, 0, 4, 2, 0, 5, 2, 0, 4, 13, 0, 5, 33, 0, 4, 2, 0, 5, 33, 0, 6, 1, 0, 0, 1, 0, 0, 1, 0, 11, 54, 0, 12, 10, 0, 13, 47, 0, 14, 33, 0, 0, 1, 0, -1, 4, 0, 2, 51, 0, 2, 41, 0, 2, 41, 0, 2, 51, 0, 2, 41, 0, 0, 2, 0, -1, 50, 0, -1, 27, 0, 6, 2, 0, 0, 3, 0, 0, 3, 0, -3, 47, 0, -1, 34, 0, -2, 35, 0, -3, 7, 0, -4, 6, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 14, 0, -2, 5, 0, -4, 8, 0, -5, 43, 0, 0, 5, 0, 0, 5, 0, -1, 15, 0, -2, 10, 0, -3, 16, 0, 0, 6, 0, 0, 6, 0, -1, 36, 0, -2, 24, 0, -3, 12, 0, -4, 17, 0, -5, 25, 0, -6, 26, 0, 0, 7, 0, 0, 7, 0, 7, 7, 0, 0, 7, 0, 7, 7, 0, 0, 7, 0, -1, 23, 0, 0, 8, 0, 0, 8, 0, -3, 33, 0, -1, 13, 0, -3, 29, 0, -4, 9, 0, 0, 9, 0, -1, 42, 0, -2, 30, 0, -3, 20, 0, -4, 31, 0, -5, 21, 0, -6, 32, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 15, 49, 0, 16, 48, 0, 0, 11, 0, -1, 19, 0, -2, 39, 0, -3, 40, 0, -4, 28, 0, -5, 27, 0, 0, 12, 0, 0, 12, 0, -3, 44, 0, 0, 12, 0, 2, 18, 0, 2, 18, 0, 2, 18, 0, 2, 18, 0, 2, 37, 0, 2, 37, 0, 2, 18, 0, 2, 37, 0, 0, 13, 0, 6, 13, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, -1, 22, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, -3, 45, 0, 0, 17, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, -1, 38, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, -2, 46, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, -2, 49, 0, -3, 50, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, -2, 52, 0, 0, 31, 0, 0, 32, 0, -2, 53, 0, 0, 32, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 38, 0, -2, 48, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, -2, 54, 0, 17, 1, 2, 8, 8, 3, 8, 4, 33, 18, 52, 33, 19, 53, 181], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 48, 49, 52, 53], [3, 1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 3, 9, 9, 1, 1, 10, 10, 1, 1, 3, 3, 20, 3, 1, 3, 3, 3], [0, 4, 5, 6, 7, 8, 9, 0, 10, 11, 12, 13, 14, 0, 1, 1, 15, 16, 17, 18, 19, 20, 0, 0, 1, 0, 21, 0, 0, 0]]]]