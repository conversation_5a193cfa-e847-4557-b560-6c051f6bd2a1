[1, ["0093ef90-650f-4168-8d5b-a96826b7b1ce", "5fca13b6-a092-4e40-bddd-90ebb154da1c@f9941", "fd0c36d8-11b4-4071-9acf-20a783b46e64"], ["node", "_spriteFrame", "_font", "_defaultClip", "root", "tipFloat", "tipTxt", "tipBgTransform", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_lpos", "_parent"], 1, 12, 4, 2, 5, 1], ["cc.Prefab", ["_name"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Sprite", ["node", "__prefab", "_spriteFrame"], 3, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_enableOutline", "node", "__prefab"], -2, 1, 4], ["edf98QH2D1AUoCrtQlbxFrU", ["node", "__prefab"], 3, 1, 4], ["cc.Animation", ["node", "__prefab", "_clips"], 3, 1, 4, 3], ["89d1dlIYXpAWafcUQ173gO9", ["node", "__prefab", "tipBgTransform", "tipTxt", "tipFloat"], 3, 1, 4, 1, 1, 1]], [[3, 0, 2], [2, 0, 1, 2, 1], [5, 0, 1, 2, 3, 4, 5, 5], [0, 0, 1, 6, 2, 3, 3], [1, 0, 2], [0, 0, 1, 4, 2, 3, 5, 3], [4, 0, 1, 2, 1], [6, 0, 1, 2, 3, 4, 5, 6, 6], [7, 0, 1, 1], [8, 0, 1, 2, 1], [9, 0, 1, 2, 3, 4, 1]], [[4, "tipItem"], [5, "tipItem", 33554432, [-8, -9], [[[1, -2, [0, "afoteK7HlCtrGcM6PUaLzF"], [5, 463, 100]], -3, [10, -7, [0, "3c+m38YxpHaZxxN2b/+HXh"], -6, -5, -4]], 4, 1, 4], [2, "a2bh6rq95LFLUdPLcrP4I/", null, null, null, -1, 0], [1, 812, -70, 0]], [3, "tipLabel", 33554432, 1, [[[1, -10, [0, "e1Gn1GTtZHzr7OAWad4A+N"], [5, 4, 54.4]], -11, [8, -12, [0, "5449qUaZlG0LirhfNUeAkE"]]], 4, 1, 4], [2, "e9wH21Fl5FfZWVryJWXiRT", null, null, null, 1, 0]], [3, "tipBg", 33554432, 1, [[-13, [6, -14, [0, "7elNEnFgNAQbGG+69rBtol"], 0]], 1, 4], [2, "9dbIeH6ZlKjbZn8YiATrfW", null, null, null, 1, 0]], [1, 3, [0, "8c491ec8tJzZlhS1Kjhkwc"], [5, 721, 54]], [7, "", 30, 30, false, true, 2, [0, "b5bfvZHYxBL66eF+FCFaiS"]], [9, 1, [0, "796KQ8Ab5EErCmuA5YKFqr"], [1]]], 0, [0, 4, 1, 0, 0, 1, 0, -2, 6, 0, 5, 6, 0, 6, 5, 0, 7, 4, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, 0, 2, 0, -2, 5, 0, 0, 2, 0, -1, 4, 0, 0, 3, 0, 8, 1, 14], [0, 0, 5, 6], [1, -1, 2, 3], [1, 0, 2, 0]]