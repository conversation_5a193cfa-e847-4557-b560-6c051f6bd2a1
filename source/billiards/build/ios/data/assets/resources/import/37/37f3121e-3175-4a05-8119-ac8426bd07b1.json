[1, ["fd0c36d8-11b4-4071-9acf-20a783b46e64", "60e4f849-0c79-4364-8d48-3b3d80cc7b25@f9941", "10e35763-4000-4a6d-a675-e117c58c379c@f9941", "15ff93af-c833-40c0-9859-6f12dc987594@f9941", "89e28899-9e89-43ef-88e5-e5a67715aa7e@f9941", "39d1dcd1-cdb2-4974-b536-b954922b1616@f9941", "9a0ef865-6a6e-4334-8488-14688656ede5@f9941", "4519243f-cc5f-4ac8-be53-067ac614fe71@f9941"], ["node", "_spriteFrame", "_font", "toggleOnSpriteFrame", "toggleOffSpriteFrame", "root", "bg", "musicToggle", "soundToggle", "btn_exit", "data"], [["cc.Widget", ["_alignFlags", "_top", "_bottom", "_originalHeight", "_right", "_alignMode", "_originalWidth", "node", "__prefab"], -4, 1, 4], ["cc.Node", ["_name", "_layer", "_components", "_prefab", "_parent", "_children", "_lpos"], 1, 9, 4, 1, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_overflow", "_enableWrapText", "_enableOutline", "_lineHeight", "_outlineWidth", "_horizontalAlign", "node", "__prefab", "_color", "_font", "_outlineColor"], -7, 1, 4, 5, 6, 5], ["cc.Node", ["_name", "_layer", "_active", "_parent", "_components", "_prefab", "_lpos", "_children"], 0, 1, 12, 4, 5, 2], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.<PERSON><PERSON>", ["node", "__prefab", "clickEvents", "_normalColor", "_target"], 3, 1, 4, 9, 5, 1], ["cc.Toggle", ["node", "__prefab", "checkEvents", "_target"], 3, 1, 4, 9, 1], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["edf98QH2D1AUoCrtQlbxFrU", ["key", "node", "__prefab"], 2, 1, 4], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["9e96312AYxLE6DDX/YlFqLT", ["showType", "node", "__prefab", "btn_exit", "soundToggle", "musicToggle", "bg", "toggleOnSpriteFrame", "toggleOffSpriteFrame"], 2, 1, 4, 1, 1, 1, 1, 6, 6]], [[9, 0, 2], [10, 0, 1, 2, 3, 4, 5, 5], [4, 0, 1, 2, 1], [1, 0, 1, 4, 2, 3, 6, 3], [5, 0, 1, 2, 3, 4, 3], [12, 0, 1, 2, 2], [0, 0, 1, 7, 8, 3], [11, 0, 1, 2, 3], [5, 2, 3, 4, 1], [3, 0, 1, 3, 4, 5, 6, 3], [4, 0, 1, 2, 3, 1], [0, 0, 4, 1, 5, 7, 8, 5], [0, 0, 1, 5, 7, 8, 4], [2, 0, 9, 1, 2, 4, 3, 10, 11, 12, 13, 7], [8, 0, 2], [1, 0, 1, 5, 2, 3, 3], [1, 0, 1, 4, 5, 2, 3, 3], [1, 0, 1, 4, 2, 3, 3], [3, 0, 2, 1, 3, 7, 4, 5, 6, 4], [0, 0, 1, 2, 3, 7, 8, 5], [0, 0, 4, 1, 7, 8, 4], [0, 0, 2, 7, 8, 3], [0, 0, 6, 3, 7, 8, 4], [6, 0, 1, 2, 1], [6, 0, 1, 2, 3, 4, 1], [2, 0, 1, 2, 4, 5, 3, 6, 10, 11, 12, 14, 13, 8], [2, 0, 1, 2, 7, 5, 3, 6, 10, 11, 12, 13, 8], [2, 0, 1, 2, 7, 4, 3, 8, 10, 11, 12, 14, 13, 8], [7, 0, 1, 3, 2, 1], [7, 0, 1, 2, 1], [13, 0, 1, 1], [14, 0, 1, 2, 3, 4, 5, 6, 7, 8, 2]], [[14, "settingDialog"], [15, "settingDialog", 33554432, [-10], [[2, -2, [0, "9e/8fsCVZLTZKb6yRHQzIF"], [5, 750, 1334]], [30, -3, [0, "80S+oqYH9BBJRdOSTroL30"]], [22, 45, 750, 478, -4, [0, "718iCGIfxAvKI6O2Bz8ybr"]], [31, 1, -9, [0, "628N9Ym85PYaeX0ZzsV3lT"], -8, -7, -6, -5, 13, 14]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [16, "bg", 33554432, 1, [-13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23], [[2, -11, [0, "d7wDKoNFlFFqGwriNZPguT"], [5, 738, 478]], [4, 1, 0, -12, [0, "dcYTj9551IFrI6POFlr7n/"], 12]], [1, "e2DvpHdUxPrYXn6/slm+is", null, null, null, 1, 0]], [18, "btn_exit", false, 33554432, 2, [-28], [[[2, -24, [0, "d8WduBrABKUbg5MRkgWl4O"], [5, 286, 112]], [4, 1, 0, -25, [0, "91xY9nJIlB15kx/yImWK+o"], 3], -26, [21, 4, 43.028999999999996, -27, [0, "0avseTKKBG2pOdsR5A/+8c"]]], 4, 4, 1, 4], [1, "05PaB7M7lH36/2+zwt8Al4", null, null, null, 1, 0], [1, 8.039, -139.971, 0]], [9, "soundBtn", 33554432, 2, [[[2, -29, [0, "1dLe+zWotKBZFqVxwWebhy"], [5, 149, 72]], [4, 1, 0, -30, [0, "138ijj6WxGOpdemfcuUL2S"], 7], -31, [11, 33, 71.63, 166.34299999999996, 1, -32, [0, "62hjFwfcFFAo7IhQ3TbzrM"]]], 4, 4, 1, 4], [1, "251olbUcBISbNmfezExu30", null, null, null, 1, 0], [1, 222.87, 36.65700000000004, 0]], [3, "btn_close", 33554432, 2, [[2, -33, [0, "e9DR9csCNOf75OU7odiJLZ"], [5, 47, 47]], [8, -34, [0, "85bEp/15RFFZfKvkYsRviP"], 1], [23, -35, [0, "7fBXepuX9AX5uKcKOg7+o2"], [[7, "9e96312AYxLE6DDX/YlFqLT", "onCancel", 1]]], [20, 33, 29.411999999999978, 24.757000000000005, -36, [0, "8fM4cokXRM7pLk/1gZKMNB"]]], [1, "bfkAQQGeVKnIwLUisJH8WK", null, null, null, 1, 0], [1, 316.088, 190.743, 0]], [3, "title", 33554432, 2, [[2, -37, [0, "8cQn56lDlHt4ZemptQop1p"], [5, 191.322265625, 59.44]], [26, "Settings", 44, 44, 44, false, false, true, -38, [0, "4895LDIZBFx7oi/c0+X/OH"], [4, 4287424511], 4], [5, "settings", -39, [0, "4cXUBv/gJMUoNd00bOEZwi"]], [6, 1, 13.221000000000004, -40, [0, "88Mj0NwG1Isp3C2eftPMrm"]]], [1, "19ndXOIuFBC5Aohw05G4Fv", null, null, null, 1, 0], [1, 0, 196.059, 0]], [3, "tipTx", 33554432, 2, [[2, -41, [0, "6fzkY4F2NDOaz6iiaN3Z9d"], [5, 616, 52.92]], [27, "", 30, 30, 42, 3, false, 3, -42, [0, "b5s8YXRo9BF4Y5fMRdW2zx"], [4, 4281227652], [4, 4285433396], 5], [5, "Tip", -43, [0, "45VviJbQ5HSIFhq7cFppMM"]], [6, 1, 173.821, -44, [0, "acMyEpT5dKSJfL++Sx6X86"]]], [1, "4c/Au9ojtIRKeFMGR4bYLc", null, null, null, 1, 0], [1, 0, 38.719, 0]], [3, "soundlb", 33554432, 2, [[10, -45, [0, "35NcSN6gtOjL0zQLik/JgJ"], [5, 300, 50.4], [0, 0, 0.5]], [13, "Sound effects", 0, 34, 34, 1, false, -46, [0, "a5NpQYYVBJ/rsdaVYyW7hl"], [4, 4281227652], 6], [6, 1, 177.14300000000003, -47, [0, "03shoaZQxM168TtV9v5lEN"]], [5, "sound", -48, [0, "6cUjL8lnFB4pac0xtVpVVi"]]], [1, "d25Slt5jxAe5t4k8YiScPu", null, null, null, 1, 0], [1, -301.615, 36.65699999999997, 0]], [3, "musiclb", 33554432, 2, [[10, -49, [0, "cdeO1B6qRMVJWtCwA/K7B7"], [5, 200, 50.4], [0, 0, 0.5]], [13, "Music", 0, 34, 34, 1, false, -50, [0, "c1ZHI7WplLX4/iklyXAyY+"], [4, 4281227652], 8], [6, 1, 300.61400000000003, -51, [0, "ccbOyQpvlCHa33tYRhwagO"]], [5, "music", -52, [0, "2dmB9dGElCLb/31OnnxFpm"]]], [1, "2cdUi9l+BHrrr734pGECTj", null, null, null, 1, 0], [1, -300.156, -86.81400000000004, 0]], [9, "musicBtn", 33554432, 2, [[[2, -53, [0, "1dNIbWk55Hxqsz4eDSVOkW"], [5, 149, 72]], [4, 1, 0, -54, [0, "2dNNTpHzBDN7lk5Fe46aNT"], 9], -55, [11, 33, 71.63, 289.81449999999995, 1, -56, [0, "ed2k890J9Hm50syVKODa1m"]]], 4, 4, 1, 4], [1, "43Oja84NtO5pR04Z0VsPjw", null, null, null, 1, 0], [1, 222.87, -86.81449999999995, 0]], [3, "bgIn", 33554432, 2, [[2, -57, [0, "abbWukLuxEfZ74RKr6LkHj"], [5, 708, 378]], [4, 1, 0, -58, [0, "4dz3+lnrNPk5rpOeOiyAM7"], 0], [19, 5, 88.275, 11.724999999999994, 378, -59, [0, "95XIm6eWVDI7x706DBHZii"]]], [1, "043qEwJe5DC54NcksSmrfU", null, null, null, 1, 0], [1, 0, -38.275000000000006, 0]], [17, "Label", 33554432, 3, [[2, -60, [0, "6eaYaXPFlB56RK2MoBtkoR"], [5, 270, 54.4]], [25, "Exit", 36, 36, 1, false, false, true, -61, [0, "00WupCyedMeq/gNHeEeXFn"], [4, 4286586958], [4, 4294344380], 2], [5, "exit", -62, [0, "c4gMGxhUJPAYsPVfSAUR5u"]]], [1, "78JOvF51xE1JU1yaFQc3b3", null, null, null, 1, 0]], [3, "img_line", 33554432, 2, [[2, -63, [0, "464rgmpCdKPYZG3/MhIB3C"], [5, 606, 3]], [8, -64, [0, "e2YnXbnchNhLvxnZ94iIe3"], 10], [12, 1, 255.93599999999992, 1, -65, [0, "2cEO8rTnxAb6WJ5nnNErPq"]]], [1, "5d4w00AWlCkJIA6kyRP7Sp", null, null, null, 1, 0], [1, 1.6541884947739618, -18.435999999999922, 0]], [3, "img_line", 33554432, 2, [[2, -66, [0, "43Xsy4GPZB76VrHZ7MBxO3"], [5, 606, 3]], [8, -67, [0, "55iBOF9p9ArIUYRQMQmKH/"], 11], [12, 1, 378.8425, 1, -68, [0, "aehK4yBulJuLuBkj04znZT"]]], [1, "efZWu76XBFj60BWJsQuDHd", null, null, null, 1, 0], [1, 1.9420000000000073, -141.34249999999997, 0]], [24, 3, [0, "de3WYYGnFCMr1UXFD0vECc"], [[7, "9e96312AYxLE6DDX/YlFqLT", "onExit", 1]], [4, 4292269782], 3], [28, 4, [0, "364Q4UaftIuafxcdBL4wLC"], 4, [[7, "9e96312AYxLE6DDX/YlFqLT", "onSoundToggleChanged", 1]]], [29, 10, [0, "7coPfyJUNDhrb5AH4KdnSW"], [[7, "9e96312AYxLE6DDX/YlFqLT", "onVolumeToggleChanged", 1]]]], 0, [0, 5, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 6, 2, 0, 7, 17, 0, 8, 16, 0, 9, 15, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -1, 11, 0, -2, 5, 0, -3, 3, 0, -4, 6, 0, -5, 7, 0, -6, 8, 0, -7, 4, 0, -8, 9, 0, -9, 10, 0, -10, 13, 0, -11, 14, 0, 0, 3, 0, 0, 3, 0, -3, 15, 0, 0, 3, 0, -1, 12, 0, 0, 4, 0, 0, 4, 0, -3, 16, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, -3, 17, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 10, 1, 68], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 2, 1, 2, 2, 2, 1, 2, 1, 1, 1, 1, 3, 4], [4, 5, 0, 6, 0, 0, 0, 1, 0, 2, 3, 3, 7, 1, 2]]