[1, ["391e3640-8878-4033-a2c5-8e6620b3216d@f9941", "d086a589-9a48-49fa-925a-b183c5bc2674@f9941", "7cb858c1-b262-4a82-8013-d19e5fa09820@f9941", "5ca2f70a-9def-4c09-b32f-2c2b0bce2d97@f9941", "fd0c36d8-11b4-4071-9acf-20a783b46e64"], ["node", "_spriteFrame", "_font", "root", "matching", "profile", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent"], 1, 9, 4, 2, 1], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_children"], 1, 1, 12, 4, 5, 2], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mask", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_overflow", "_enableWrapText", "_isSystemFontUsed", "node", "__prefab", "_color"], -3, 1, 4, 5], ["97323FPHLdFhLcK/XCsmO6e", ["_size", "nameFormatLength", "node", "__prefab", "spAvatar", "lbName"], 1, 1, 4, 1, 1], ["42f377qzDNA+JEYbDkuKumf", ["node", "__prefab", "profile", "matching"], 3, 1, 4, 1, 1]], [[5, 0, 2], [4, 0, 1, 2, 1], [6, 0, 1, 2, 3, 4, 5, 5], [0, 0, 1, 5, 2, 3, 3], [2, 1, 2, 3, 1], [3, 0, 2], [0, 0, 1, 4, 2, 3, 3], [0, 0, 1, 5, 4, 2, 3, 3], [1, 0, 1, 2, 6, 3, 4, 5, 3], [1, 0, 1, 2, 3, 4, 3], [1, 0, 1, 2, 3, 4, 5, 3], [2, 0, 1, 2, 2], [7, 0, 1, 2, 2], [8, 0, 1, 2, 1], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 7], [10, 0, 1, 2, 3, 4, 5, 3], [11, 0, 1, 2, 3, 1]], [[5, "seat"], [6, "seat", 33554432, [-6, -7], [[1, -2, [0, "bfpmcnLv9PbrFjeP+LyfdB"], [5, 112, 112]], [16, -5, [0, "13zOtgiYlGfJ2xVQ3jl3NU"], -4, -3]], [2, "30HHAvjstCwLW6m/p4LsTs", null, null, null, -1, 0]], [8, "profile", 33554432, 1, [-10, -11, -12, -13], [[[1, -8, [0, "b6PlbgbiNL848ETkrgwUe7"], [5, 112, 112]], -9], 4, 1], [2, "14Yup30UFIuLHfJDvqK0gX", null, null, null, 1, 0], [1, 0, 17.885, 0]], [7, "mask", 33554432, 2, [-17], [[1, -14, [0, "e0KdkVfZpMa4MQl/UxaeR3"], [5, 138, 138]], [12, 1, -15, [0, "a0gkydRIlMl72BiVNmMLcg"]], [13, -16, [0, "efSNCovXRG6IsJTgMyLMqJ"], [4, 16777215]]], [2, "4f4hSYUcRAeq5d/ak3Aowr", null, null, null, 1, 0]], [3, "matching", 33554432, 2, [[1, -18, [0, "81ZdA8MjlHwpUL84tSr+bY"], [5, 130, 130]], [4, -19, [0, "c8tmc/AVtLuol9tOmINRIJ"], 1]], [2, "92BkQWm7pPVJl3/yYn1mRv", null, null, null, 1, 0]], [3, "match_player_bg", 33554432, 1, [[1, -20, [0, "07mRIgh4ZPdZ2+tASL9LIU"], [5, 204, 358]], [4, -21, [0, "dcH3jBew1OcqWhvVvRDNzt"], 0]], [2, "03FYBlQZNNjKHcH+szfR01", null, null, null, 1, 0]], [9, "spAvatar", 33554432, 3, [[[1, -22, [0, "46yqV1nNxOkJlCwNeuVNw5"], [5, 138, 138]], -23], 4, 1], [2, "73Y1fqidpKg64BHMd/b9+D", null, null, null, 1, 0]], [3, "head_cr", 33554432, 2, [[1, -24, [0, "59sEDk7LNBe4ixUsdCPiSc"], [5, 150, 150]], [4, -25, [0, "010c1SWUlMIomscbdrqQnE"], 2]], [2, "c2BUJV7YVE379QVMrnFYbg", null, null, null, 1, 0]], [10, "lblName", 33554432, 2, [[[1, -26, [0, "4aXxcoe/pL07xMO5soEalX"], [5, 200, 50]], -27], 4, 1], [2, "01mSM95BRAbqXOkZ+E3fMP", null, null, null, 1, 0], [1, 0, -97.4, 0]], [11, 0, 6, [0, "bet3twL5xA7auwsCEabWd8"]], [14, "Player2323", 24, 24, 1, false, false, 8, [0, "d1NJnq/9NOj6DjxRosyHN6"], [4, 4283037438]], [15, 112, 11, 2, [0, "a9fYCvaHtIxYV+fNgF/VcD"], 9, 10]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 4, 0, 5, 11, 0, 0, 1, 0, -1, 5, 0, -2, 2, 0, 0, 2, 0, -2, 11, 0, -1, 3, 0, -2, 4, 0, -3, 7, 0, -4, 8, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 6, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -2, 9, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -2, 10, 0, 6, 1, 27], [0, 0, 0, 9, 10], [1, 1, 1, 1, 2], [0, 1, 2, 3, 4]]