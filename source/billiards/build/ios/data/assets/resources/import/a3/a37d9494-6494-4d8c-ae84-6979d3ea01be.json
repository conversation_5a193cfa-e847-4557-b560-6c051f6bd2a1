[1, ["fd0c36d8-11b4-4071-9acf-20a783b46e64", "a29c1079-290d-4f94-bece-12ea74c17514@f9941", "1f6bfd4b-37c2-4bdd-b4fc-bb065b238d2d@f9941", "0573bc9e-d56d-4da0-9203-3d7f4b30bd4a@f9941", "ddb451b7-f31c-4c32-be0d-6a8b5bbdff4c@f9941", "39e1240d-8073-41c2-847c-b87a3885106c"], ["node", "_spriteFrame", "_font", "_customMaterial", "root", "playerWidget", "lbName", "spAvatar", "data"], [["cc.Node", ["_name", "_layer", "_active", "_parent", "_components", "_prefab", "_lpos", "_children", "_lscale"], 0, 1, 9, 4, 5, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_fillType", "_fillStart", "node", "__prefab", "_spriteFrame", "_fillCenter"], -2, 1, 4, 6, 5], ["cc.Node", ["_name", "_layer", "_components", "_prefab", "_lpos", "_parent", "_children"], 1, 12, 4, 5, 1, 2], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Label", ["_string", "_actualFontSize", "_enableWrapText", "_isSystemFontUsed", "_horizontalAlign", "_fontSize", "_overflow", "node", "__prefab", "_font"], -4, 1, 4, 6], ["cc.Widget", ["_alignFlags", "_left", "_alignMode", "node", "__prefab"], 0, 1, 4], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["6e0cdvfdK1AHqEHnYZNdwhO", ["node", "__prefab"], 3, 1, 4], ["97323FPHLdFhLcK/XCsmO6e", ["nameFormatLength", "node", "__prefab", "spAvatar", "lbName"], 2, 1, 4, 1, 1], ["de413E/7KdKG57LfQhVHaFr", ["node", "__prefab", "playerWidget"], 3, 1, 4, 1]], [[7, 0, 2], [8, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [0, 0, 2, 1, 3, 4, 5, 4], [0, 0, 1, 3, 4, 5, 6, 3], [0, 0, 1, 3, 7, 4, 5, 3], [0, 0, 2, 1, 3, 4, 5, 6, 8, 4], [3, 0, 1, 2, 3, 1], [3, 0, 1, 1], [1, 5, 6, 7, 1], [1, 5, 6, 1], [6, 0, 2], [2, 0, 1, 6, 2, 3, 4, 3], [2, 0, 1, 5, 2, 3, 3], [2, 0, 1, 5, 2, 3, 4, 3], [0, 0, 1, 3, 4, 5, 3], [1, 0, 5, 6, 7, 2], [1, 0, 1, 5, 6, 3], [1, 2, 3, 0, 4, 5, 6, 8, 7, 5], [9, 0, 1, 1], [10, 0, 1, 2, 3, 4, 2], [4, 0, 4, 1, 5, 6, 2, 3, 7, 8, 8], [4, 0, 1, 2, 3, 7, 8, 9, 5], [5, 0, 3, 4, 2], [5, 0, 1, 2, 3, 4, 4], [11, 0, 1, 2, 1]], [[11, "playerItem0"], [12, "playerItem0", 33554432, [-6, -7, -8, -9], [[[8, -2, [0, "5bntDTwD9C45FWJL9XZOwb"]], -3, [25, -5, [0, "56j2+kD/dNAZ9JY+Po3m/1"], -4]], 4, 1, 4], [1, "31RfTjxrxLDYdP1R5tZUtB", null, null, null, -1, 0], [1, -322, 0, 0]], [5, "headNode", 33554432, 1, [-12, -13, -14, -15, -16, -17, -18], [[8, -10, [0, "7dWDnvwxlAvqgEhj4krWX3"]], [23, 8, -11, [0, "ddfsTbp8dDvrfGNmB3m4L8"]]], [1, "855ULUr5ZCIpTX+sjTZJmS", null, null, null, 1, 0]], [5, "profile", 33554432, 2, [-23], [[2, -19, [0, "02/RTRdxdEgZvffpmQznxy"], [5, 72, 72]], [20, 13, -22, [0, "e1tdQjV4pDEbtLdQ113cl6"], -21, -20]], [1, "5eev3+HDVAVrUwYpfPvWLh", null, null, null, 1, 0]], [4, "playerBg", 33554432, 1, [[2, -24, [0, "c7ftshottCC5hYjRI7gxTv"], [5, 372, 92]], [9, -25, [0, "9fF4GQ7+pPUZEjw6kfqK6R"], 0]], [1, "fe/Mj4sJFAs4RLho36T7Zv", null, null, null, 1, 0], [1, 133.206, 0, 0]], [4, "objBallNode", 33554432, 1, [[7, -26, [0, "23GtW5sOVDVIOXcK+CO8AP"], [5, 260, 50], [0, 0, 0.5]], [19, -27, [0, "45yWPIX9FGFakUoQcvzO7y"]]], [1, "d8X6R0eC5BiZB9ggqxjYnL", null, null, null, 1, 0], [1, 60, -22, 0]], [3, "bg", false, 33554432, 2, [[2, -28, [0, "d9fjfsLa5F17aK3f98k4O7"], [5, 78, 78]], [16, 0, -29, [0, "68tsyOiOFHNqcVpmz0GBB4"], 1]], [1, "9b93tPHBFD3oPKbAxulyTO", null, null, null, 1, 0]], [3, "changeBg", false, 33554432, 2, [[2, -30, [0, "b5Us0q2lFC6oMEWsBGy0HF"], [5, 80, 80]], [9, -31, [0, "cdCOgriH1D4KcrDDL5R9PP"], 2]], [1, "6c82Z1SnBL75mFgSfCDkVB", null, null, null, 1, 0]], [13, "spAvatar", 33554432, 3, [[[2, -32, [0, "a8V28L/YRE6I+J7JrCEnm2"], [5, 72, 72]], -33], 4, 1], [1, "b9DcLCCCdNRIjJh7i0ekDj", null, null, null, 1, 0]], [14, "lblName", 33554432, 1, [[[7, -34, [0, "9b7/b0uy5HKb5BX5NPee8u"], [5, 240, 40], [0, 0, 0.5]], -35], 4, 1], [1, "c3Q0zGu8NNw76VQN3wUcTg", null, null, null, 1, 0], [1, 47, 16, 0]], [3, "headAlphaBg", false, 33554432, 2, [[2, -36, [0, "d04htEMd1H143gERG89e/P"], [5, 72, 72]], [18, 3, 2, 0, 0.25, -37, [0, "c1GLUWXMtCLo+FqENrEdGp"], [0, 0.5, 0.5], 3]], [1, "c1ht91XgdIBLDqKHMhO5My", null, null, null, 1, 0]], [15, "cdTimeLabel", 33554432, 2, [[2, -38, [0, "15dmQSYylC5pQouUdh8+Lq"], [5, 0, 50.4]], [22, "", 40, false, false, -39, [0, "07Uhru9VJNHYT7G1DSL6oP"], 4]], [1, "5cUOyi5RpKNqCAQL2z91Ug", null, null, null, 1, 0]], [6, "img_count0", false, 33554432, 2, [[2, -40, [0, "b4Nc5lRMFN9IxCP1OVxk6N"], [5, 31, 37]], [10, -41, [0, "de6hb7vqZNfZBWAAYhxSzy"]]], [1, "a1zAFJ7E9LprDm0nTAAx2j", null, null, null, 1, 0], [1, -13.279, 0, 0], [1, 0.9, 0.9, 1]], [6, "img_count1", false, 33554432, 2, [[2, -42, [0, "02bGqDYNRI9qhj82dwli5Z"], [5, 35, 37]], [10, -43, [0, "aeDbxGowZJBYxCrJNQXjYQ"]]], [1, "24uxJQKu5K3q5c6qFl9f4j", null, null, null, 1, 0], [1, 15.721, 0, 0], [1, 0.9, 0.9, 1]], [17, 0, false, 8, [0, "20xsWidmxOULn+HE0fR67r"]], [21, "Name", 0, 24, 24, 1, false, false, 9, [0, "4fw+qWJQFOnI5tfN5z0gBG"]], [24, 8, 3, 1, 1, [0, "1bJKRwAbhOb5B/FatBT6OC"]]], 0, [0, 4, 1, 0, 0, 1, 0, -2, 16, 0, 5, 16, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, -3, 2, 0, -4, 9, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 7, 0, -3, 3, 0, -4, 10, 0, -5, 11, 0, -6, 12, 0, -7, 13, 0, 0, 3, 0, 6, 15, 0, 7, 14, 0, 0, 3, 0, -1, 8, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -2, 14, 0, 0, 9, 0, -2, 15, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 8, 1, 43], [0, 0, 0, 0, 0, 14, 15], [1, 1, 1, 1, 2, 3, 2], [1, 2, 3, 4, 0, 5, 0]]