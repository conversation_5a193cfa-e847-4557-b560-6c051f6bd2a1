[1, ["fd0c36d8-11b4-4071-9acf-20a783b46e64", "89e28899-9e89-43ef-88e5-e5a67715aa7e@f9941", "39d1dcd1-cdb2-4974-b536-b954922b1616@f9941", "9a0ef865-6a6e-4334-8488-14688656ede5@f9941", "178a2582-3c1d-485f-8ad7-02d2a5f6841c@f9941", "4519243f-cc5f-4ac8-be53-067ac614fe71@f9941"], ["node", "_spriteFrame", "_font", "root", "tipTx", "btn_confirm", "btn_cancel", "data"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "_components", "_prefab", "_parent", "_children", "_lpos"], 0, 9, 4, 1, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_overflow", "_enableWrapText", "_enableOutline", "_lineHeight", "_outlineWidth", "node", "__prefab", "_color", "_outlineColor", "_font"], -6, 1, 4, 5, 5, 6], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos", "_children"], 1, 1, 12, 4, 5, 2], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.<PERSON><PERSON>", ["node", "__prefab", "clickEvents", "_normalColor", "_target"], 3, 1, 4, 9, 5, 1], ["cc.Prefab", ["_name"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["edf98QH2D1AUoCrtQlbxFrU", ["key", "node", "__prefab"], 2, 1, 4], ["26c1c5+BNNO0695uqyI6DXl", ["showType", "node", "__prefab", "btn_cancel", "btn_confirm", "tipTx"], 2, 1, 4, 1, 1, 1], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4]], [[7, 0, 2], [6, 0, 1, 2, 1], [8, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 3, 4, 3], [0, 0, 1, 5, 3, 4, 7, 3], [9, 0, 1, 2, 3], [2, 0, 1, 2, 6, 3, 4, 5, 3], [4, 0, 1, 2, 3, 4, 1], [1, 0, 1, 2, 4, 5, 3, 6, 9, 10, 11, 12, 13, 8], [10, 0, 1, 2, 2], [5, 0, 2], [0, 0, 1, 6, 3, 4, 3], [0, 0, 1, 5, 6, 3, 4, 3], [0, 0, 2, 1, 5, 3, 4, 4], [0, 0, 2, 1, 5, 3, 4, 7, 4], [2, 0, 1, 2, 3, 4, 5, 3], [3, 2, 3, 4, 1], [4, 0, 1, 2, 1], [1, 0, 1, 2, 7, 5, 3, 6, 9, 10, 11, 13, 8], [1, 0, 1, 2, 7, 4, 3, 8, 9, 10, 11, 12, 8], [11, 0, 1, 2, 3, 4, 5, 2], [12, 0, 1, 1]], [[10, "confirmDialog"], [11, "confirmDialog", 33554432, [-8], [[1, -2, [0, "9e/8fsCVZLTZKb6yRHQzIF"], [5, 750, 400]], [20, 1, -6, [0, "24nDBJjapB+YFkGqin92G1"], -5, -4, -3], [21, -7, [0, "80S+oqYH9BBJRdOSTroL30"]]], [2, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [12, "bg", 33554432, 1, [-11, -12, -13, -14, -15, -16], [[1, -9, [0, "d7wDKoNFlFFqGwriNZPguT"], [5, 738, 478]], [3, 1, 0, -10, [0, "dcYTj9551IFrI6POFlr7n/"], 7]], [2, "e2DvpHdUxPrYXn6/slm+is", null, null, null, 1, 0]], [6, "btn_cancel", 33554432, 2, [-20], [[[1, -17, [0, "d8WduBrABKUbg5MRkgWl4O"], [5, 286, 112]], [3, 1, 0, -18, [0, "91xY9nJIlB15kx/yImWK+o"], 3], -19], 4, 4, 1], [2, "05PaB7M7lH36/2+zwt8Al4", null, null, null, 1, 0], [1, -177, -119, 0]], [6, "btn_confirm", 33554432, 2, [-24], [[[1, -21, [0, "5da2iMPSBC5aIvpoRx48xM"], [5, 286, 112]], [3, 1, 0, -22, [0, "79hAfyvepNzLXgFVrZAEA4"], 5], -23], 4, 4, 1], [2, "81zpDMSv5IuoWbp3HNdHgQ", null, null, null, 1, 0], [1, 177, -119, 0]], [4, "btn_close", 33554432, 2, [[1, -25, [0, "e9DR9csCNOf75OU7odiJLZ"], [5, 47, 47]], [16, -26, [0, "85bEp/15RFFZfKvkYsRviP"], 1], [17, -27, [0, "7fBXepuX9AX5uKcKOg7+o2"], [[5, "26c1c5+BNNO0695uqyI6DXl", "onCancel", 1]]]], [2, "bfkAQQGeVKnIwLUisJH8WK", null, null, null, 1, 0], [1, 316.088, 190.743, 0]], [4, "title", 33554432, 2, [[1, -28, [0, "8cQn56lDlHt4ZemptQop1p"], [5, 97.220703125, 59.44]], [18, "Tips", 44, 44, 44, false, false, true, -29, [0, "4895LDIZBFx7oi/c0+X/OH"], [4, 4287424511], 6], [9, "tips", -30, [0, "4cXUBv/gJMUoNd00bOEZwi"]]], [2, "19ndXOIuFBC5Aohw05G4Fv", null, null, null, 1, 0], [1, 0, 196.059, 0]], [15, "tipTx", 33554432, 2, [[[1, -31, [0, "6fzkY4F2NDOaz6iiaN3Z9d"], [5, 616, 52.92]], -32, [9, "Tip", -33, [0, "45VviJbQ5HSIFhq7cFppMM"]]], 4, 1, 4], [2, "4c/Au9ojtIRKeFMGR4bYLc", null, null, null, 1, 0], [1, 0, 38.719, 0]], [4, "bgIn", 33554432, 2, [[1, -34, [0, "abbWukLuxEfZ74RKr6LkHj"], [5, 708, 378]], [3, 1, 0, -35, [0, "4dz3+lnrNPk5rpOeOiyAM7"], 0]], [2, "043qEwJe5DC54NcksSmrfU", null, null, null, 1, 0], [1, 0, -38.275, 0]], [13, "Label", 512, 33554432, 3, [[1, -36, [0, "6eaYaXPFlB56RK2MoBtkoR"], [5, 270, 54.4]], [8, "Cancel", 36, 36, 1, false, false, true, -37, [0, "00WupCyedMeq/gNHeEeXFn"], [4, 4286586958], [4, 4294344380], 2]], [2, "78JOvF51xE1JU1yaFQc3b3", null, null, null, 1, 0]], [14, "Label", 512, 33554432, 4, [[1, -38, [0, "1e4D2G3+tGObRIsZf+75XJ"], [5, 270, 54.4]], [8, "Confirm", 36, 36, 1, false, false, true, -39, [0, "cdVe+s3WJDE4KZPsHl6MMI"], [4, 4279653530], [4, 4286184191], 4]], [2, "056npCywNDOJxCXn5ax9Uv", null, null, null, 1, 0], [1, 0, 6.369, 0]], [7, 3, [0, "de3WYYGnFCMr1UXFD0vECc"], [[5, "26c1c5+BNNO0695uqyI6DXl", "onCancel", 1]], [4, 4292269782], 3], [7, 4, [0, "b7jHS/AUBNiZq3pHy6NwXx"], [[5, "26c1c5+BNNO0695uqyI6DXl", "onConfirm", 1]], [4, 4292269782], 4], [19, "", 30, 30, 42, 3, false, 3, 7, [0, "b5s8YXRo9BF4Y5fMRdW2zx"], [4, 4281227652], [4, 4285433396]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 13, 0, 5, 12, 0, 6, 11, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -1, 8, 0, -2, 5, 0, -3, 3, 0, -4, 4, 0, -5, 6, 0, -6, 7, 0, 0, 3, 0, 0, 3, 0, -3, 11, 0, -1, 9, 0, 0, 4, 0, 0, 4, 0, -3, 12, 0, -1, 10, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -2, 13, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 7, 1, 39], [0, 0, 0, 0, 0, 0, 0, 0, 13], [1, 1, 2, 1, 2, 1, 2, 1, 2], [1, 2, 0, 3, 0, 4, 0, 5, 0]]