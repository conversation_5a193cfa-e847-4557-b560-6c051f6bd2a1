[1, ["fd0c36d8-11b4-4071-9acf-20a783b46e64", "bd1bcaba-bd7d-4a71-b143-997c882383e4@f9941", "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "b59fa3d0-66e1-4088-a85b-231b1675f08c@f9941", "c82d4ea1-0bf3-4667-a8c0-09091a65594f@f9941", "a1f8ddf1-b7db-48c2-9260-d11368c6df48@f9941", "20835ba4-6145-4fbc-a58a-051ce700aa3e@6c48a", "557dc3db-51d2-4ffc-8616-982d7566d797@6c48a", "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@6c48a", "e706adc5-34f6-49c2-bdea-dc96fbe04083", "951249e0-9f16-456d-8b85-a6ca954da16b@6c48a", "9cf11a8a-1769-480b-bfa5-12d3f6c8f8c0@6c48a", "bd1bcaba-bd7d-4a71-b143-997c882383e4@6c48a", "f3d7a3cc-9c41-4861-943f-d4e6f6507c75@6c48a", "99a650fd-cec4-482c-9f04-b87bc44d4d58@f9941", "b13356c4-ea40-4731-9a7d-44fbe4ad348e@f9941", "3f43ebcd-f512-4a4a-b0b2-043f38b60f72@f9941", "f91dda88-8315-4d12-b306-f6d423782a98@f9941", "9cf11a8a-1769-480b-bfa5-12d3f6c8f8c0@f9941", "ea1da508-64b7-4544-80a9-3cb1abeef8cf@f9941", "c313bb0a-ef52-4fcd-b557-9f1b7e2ade24@f9941", "98be28a6-9661-4905-a4d0-053bc944d93d@f9941", "b81feaa9-842a-4176-92c7-92b9da6d1a7a@f9941", "cd9e02ff-e9b5-4a66-8ee6-2106a4591bce@f9941", "71b81800-9630-4229-9447-92e9e2a9abe9@f9941", "aacbec2e-c79f-4b43-8d76-82a92adab6d5@f9941", "9ca8dcb7-d263-49f5-b285-ef9c0b7a5ccc@f9941", "ec205bb2-9f8c-4486-a3d7-58b2fdb24109@f9941", "19675c19-6bd8-4f2e-918c-c4d31dd8a6e5@f9941", "4e7ead1f-a22a-4a0e-9e17-8b83ab0f0712", "f3d7a3cc-9c41-4861-943f-d4e6f6507c75@f9941", "ffcc534a-fa56-489e-bd65-bf4116f79a3f@f9941", "4b6633e0-b4a5-4ceb-ab5d-df0cebe73897", "80f32270-ec66-4a10-a211-5a402d8d283f@f9941", "4646441b-9911-4f34-aead-207321f53af4@f9941", "9c1cc9e2-9ce1-4bdf-91a3-0796706ef653@f9941", "5346414f-374b-4c0d-a916-4eee0f7afad8@f9941", "4d1696cd-8048-4bac-8086-3671387df56b", "a37d9494-6494-4d8c-ae84-6979d3ea01be", "297a1fad-d713-4d13-84be-02bf64c92e35", "9800039e-478e-4818-8228-129c713b3a14", "71350878-a712-4da3-982c-119b2afbb5a5", "6bb9b7d4-7e62-4501-a638-ba268f838f00@bb1a3", "6bb9b7d4-7e62-4501-a638-ba268f838f00@93043", "6bb9b7d4-7e62-4501-a638-ba268f838f00@a6b9d", "6bb9b7d4-7e62-4501-a638-ba268f838f00@ecffe", "6bb9b7d4-7e62-4501-a638-ba268f838f00@74ef1", "6bb9b7d4-7e62-4501-a638-ba268f838f00@7d214", "6bb9b7d4-7e62-4501-a638-ba268f838f00@fea17", "6bb9b7d4-7e62-4501-a638-ba268f838f00@d7755", "6bb9b7d4-7e62-4501-a638-ba268f838f00@fe069", "6bb9b7d4-7e62-4501-a638-ba268f838f00@0f466", "6bb9b7d4-7e62-4501-a638-ba268f838f00@d6710", "6bb9b7d4-7e62-4501-a638-ba268f838f00@6422a", "6bb9b7d4-7e62-4501-a638-ba268f838f00@cdab0", "6bb9b7d4-7e62-4501-a638-ba268f838f00@c10f9", "6bb9b7d4-7e62-4501-a638-ba268f838f00@a8656", "6bb9b7d4-7e62-4501-a638-ba268f838f00@979f3"], ["node", "_spriteFrame", "_font", "_backgroundImage", "_textureSource", "_target", "_parent", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite", "_effectAsset", "root", "test<PERSON><PERSON><PERSON>", "txtWinGold", "sliderbar", "table", "cue", "tableLinearDampEdit", "frictionEdit", "restitutionEdit", "linearDampEdit", "powerRange", "maxPowerEdit", "minPowerEdit", "_placeholder<PERSON><PERSON><PERSON>", "_textLabel", "data", "_customMaterial", "_skeletonData", "pfBall", "b1", "b2"], [["cc.Label", ["_actualFontSize", "_fontSize", "_string", "_isSystemFontUsed", "_overflow", "_horizontalAlign", "_enableWrapText", "_lineHeight", "_isBold", "_verticalAlign", "node", "__prefab", "_font", "_color"], -7, 1, 4, 6, 5], ["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "_components", "_prefab", "_parent", "_children", "_lpos"], -1, 9, 4, 1, 2, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_left", "_right", "_verticalCenter", "_alignMode", "_enabled", "_originalHeight", "node", "__prefab", "_target"], -5, 1, 4, 1], ["cc.Sprite", ["_sizeMode", "_type", "_fillType", "_fillStart", "_fillRange", "node", "__prefab", "_spriteFrame", "_customMaterial"], -2, 1, 4, 6, 6], "cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_lpos", "_children", "_parent"], 0, 12, 4, 5, 2, 1], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Mask", ["_type", "_alphaThreshold", "node", "__prefab"], 1, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "clickEvents", "_target", "_normalColor", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite"], 2, 1, 4, 9, 1, 5, 6, 6, 6, 6], ["cc.EditBox", ["_maxLength", "_inputMode", "node", "__prefab", "_textLabel", "_placeholder<PERSON><PERSON><PERSON>", "_backgroundImage"], 1, 1, 4, 1, 1, 6], ["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3], ["cc.Material", ["_name", "_props", "_states", "_defines"], -1], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["38f0e6Dj2RNGKg7QovKytkK", ["node", "__prefab", "playerPrefabs", "cue", "table", "sliderbar", "txtWinGold", "textures", "test<PERSON><PERSON><PERSON>", "pfBall", "b1", "b2"], 3, 1, 4, 3, 1, 1, 1, 1, 3, 1, 6, 6, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PolygonCollider2D", ["tag", "_group", "_friction", "node", "__prefab", "_points"], 0, 1, 4, 12], ["cc.RigidBody2D", ["enabledContactListener", "awakeOnLoad", "_type", "node", "__prefab"], 0, 1, 4], ["cc.BoxCollider2D", ["_group", "_restitution", "node", "__prefab", "_offset", "_size"], 1, 1, 4, 5, 5], ["27bbdYLsvZKsLpWyOKbnUd9", ["node", "__prefab", "minPowerEdit", "maxPowerEdit", "powerRange", "linearDampEdit", "restitutionEdit", "frictionEdit", "tableLinearDampEdit"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1], ["0e249Csn/5O1ZXK7lr2zy24", ["node", "__prefab"], 3, 1, 4], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.UIOpacity", ["node", "__prefab"], 3, 1, 4], ["adc10Jcl0JBIqKggIVkilUH", ["node", "__prefab"], 3, 1, 4], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["edf98QH2D1AUoCrtQlbxFrU", ["key", "node", "__prefab"], 2, 1, 4], ["sp.Skeleton", ["defaultSkin", "_premultipliedAlpha", "_preCacheMode", "loop", "node", "__prefab", "_skeletonData"], -1, 1, 4, 6], ["6b32dVI2p9OLaXSJZMy7uhG", ["node", "__prefab"], 3, 1, 4], ["35249I9WChLf5HdmIvwbtpm", ["node", "__prefab"], 3, 1, 4], ["12b15H4/apPz5m9UvQKUFXV", ["node", "__prefab"], 3, 1, 4]], [[13, 0, 2], [15, 0, 1, 2, 3, 4, 5, 5], [6, 0, 1, 2, 1], [6, 0, 1, 2, 3, 1], [1, 0, 1, 6, 4, 5, 8, 3], [3, 5, 6, 7, 1], [3, 1, 0, 5, 6, 7, 3], [5, 0, 1, 7, 3, 4, 5, 3], [5, 0, 2, 1, 7, 3, 4, 5, 4], [3, 0, 5, 6, 7, 2], [5, 0, 1, 7, 6, 3, 4, 5, 3], [9, 1, 0, 2, 3, 4, 5, 3], [0, 2, 5, 0, 1, 4, 6, 3, 10, 11, 8], [0, 2, 5, 0, 1, 4, 6, 3, 10, 11, 13, 8], [1, 0, 1, 6, 7, 4, 5, 8, 3], [16, 0, 1, 2, 3, 4, 5, 4], [18, 0, 1, 2, 3, 4, 5, 3], [1, 0, 1, 6, 4, 5, 3], [0, 2, 5, 0, 1, 7, 3, 8, 10, 11, 12, 8], [1, 0, 2, 1, 6, 7, 4, 5, 8, 4], [25, 0, 1, 2, 3], [1, 0, 3, 1, 6, 4, 5, 4], [23, 0, 1, 1], [8, 0, 1, 2, 3, 5, 4, 6, 7, 8, 9, 2], [0, 2, 0, 1, 4, 6, 3, 10, 11, 13, 12, 7], [1, 0, 1, 7, 4, 5, 3], [1, 0, 2, 1, 6, 4, 5, 4], [1, 0, 2, 1, 6, 4, 5, 8, 4], [2, 0, 3, 8, 9, 3], [21, 0, 1, 1], [10, 0, 1, 2, 3, 4, 5], [11, 0, 1, 2, 3, 5], [12, 0, 2], [1, 0, 1, 6, 7, 4, 5, 3], [5, 0, 1, 6, 3, 4, 3], [5, 0, 1, 6, 3, 4, 5, 3], [6, 0, 1, 1], [2, 6, 0, 1, 7, 8, 9, 5], [2, 0, 2, 3, 1, 8, 9, 5], [2, 0, 2, 3, 4, 1, 5, 8, 9, 7], [2, 0, 2, 3, 4, 1, 5, 8, 9, 10, 7], [2, 0, 2, 8, 9, 3], [2, 0, 8, 9, 2], [2, 0, 4, 1, 5, 8, 9, 5], [14, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [3, 0, 5, 6, 2], [3, 1, 5, 6, 7, 2], [3, 2, 0, 3, 4, 5, 6, 7, 5], [3, 1, 0, 5, 6, 8, 7, 3], [17, 0, 1, 2, 3, 4, 4], [19, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [20, 0, 1, 1], [7, 2, 3, 1], [7, 0, 1, 2, 3, 3], [22, 0, 1, 2, 1], [24, 0, 1, 1], [8, 1, 2, 3, 4, 1], [9, 0, 2, 3, 4, 5, 6, 2], [0, 2, 0, 1, 4, 6, 3, 10, 11, 12, 7], [0, 2, 0, 1, 7, 3, 8, 10, 11, 12, 7], [0, 2, 5, 0, 1, 6, 3, 10, 11, 13, 7], [0, 0, 1, 10, 11, 3], [0, 2, 5, 9, 0, 1, 4, 3, 10, 11, 8], [0, 2, 5, 0, 1, 4, 3, 10, 11, 13, 7], [26, 0, 1, 2, 2], [27, 0, 1, 2, 3, 4, 5, 6, 5], [28, 0, 1, 1], [29, 0, 1, 1], [30, 0, 1, 1]], [[[{"name": "default_btn_normal", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [4], [8]], [[[30, "skeleton", "\nskeleton.png\nsize: 512,128\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n183-1\n  rotate: false\n  xy: 416, 90\n  size: 37, 36\n  orig: 37, 36\n  offset: 0, 0\n  index: -1\n183-10\n  rotate: false\n  xy: 190, 68\n  size: 59, 58\n  orig: 59, 58\n  offset: 0, 0\n  index: -1\n183-11\n  rotate: false\n  xy: 190, 10\n  size: 58, 56\n  orig: 58, 56\n  offset: 0, 0\n  index: -1\n183-12\n  rotate: false\n  xy: 251, 70\n  size: 57, 56\n  orig: 57, 56\n  offset: 0, 0\n  index: -1\n183-2\n  rotate: false\n  xy: 367, 79\n  size: 47, 47\n  orig: 47, 47\n  offset: 0, 0\n  index: -1\n183-3\n  rotate: false\n  xy: 310, 71\n  size: 55, 55\n  orig: 55, 55\n  offset: 0, 0\n  index: -1\n183-4\n  rotate: false\n  xy: 128, 67\n  size: 60, 59\n  orig: 60, 59\n  offset: 0, 0\n  index: -1\n183-5\n  rotate: false\n  xy: 2, 65\n  size: 61, 61\n  orig: 61, 61\n  offset: 0, 0\n  index: -1\n183-6\n  rotate: false\n  xy: 65, 66\n  size: 61, 60\n  orig: 61, 60\n  offset: 0, 0\n  index: -1\n183-7\n  rotate: false\n  xy: 65, 5\n  size: 61, 59\n  orig: 61, 59\n  offset: 0, 0\n  index: -1\n183-8\n  rotate: false\n  xy: 128, 6\n  size: 60, 59\n  orig: 60, 59\n  offset: 0, 0\n  index: -1\n183-9\n  rotate: false\n  xy: 2, 2\n  size: 61, 61\n  orig: 61, 61\n  offset: 0, 0\n  index: -1\n", ["skeleton.png"], {"skeleton": {"hash": "y5xsDTB0vx/OsPJeERxzdl+J3eI", "spine": "3.6.53", "width": 957.68, "height": 2073.69, "images": ""}, "bones": [{"name": "root"}], "slots": [{"name": "jd", "bone": "root"}], "skins": {"default": {"jd": {"183-1": {"width": 37, "height": 36}, "183-2": {"width": 47, "height": 47}, "183-3": {"width": 55, "height": 55}, "183-4": {"width": 60, "height": 59}, "183-5": {"width": 61, "height": 61}, "183-6": {"width": 61, "height": 60}, "183-7": {"width": 61, "height": 59}, "183-8": {"width": 60, "height": 59}, "183-9": {"width": 61, "height": 61}, "183-10": {"width": 59, "height": 58}, "183-11": {"width": 58, "height": 56}, "183-12": {"width": 57, "height": 56}, "jd": {"type": "point"}}}}, "animations": {"183": {"slots": {"jd": {"attachment": [{"time": 0, "name": "183-1"}, {"time": 0.0333, "name": "183-2"}, {"time": 0.0667, "name": "183-3"}, {"time": 0.1, "name": "183-4"}, {"time": 0.1333, "name": "183-5"}, {"time": 0.1667, "name": "183-6"}, {"time": 0.2, "name": "183-7"}, {"time": 0.2333, "name": "183-8"}, {"time": 0.2667, "name": "183-9"}, {"time": 0.3, "name": "183-10"}, {"time": 0.3333, "name": "183-11"}, {"time": 0.3667, "name": "183-12"}, {"time": 0.4, "name": null}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}}}}, [0]]], 0, 0, [0], [-1], [9]], [[{"name": "default_btn_pressed", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [4], [10]], [[[31, "billiards2", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_BELT": true}]]], 0, 0, [0], [11], [11]], [[{"name": "default_btn_disabled", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [4], [12]], [[{"name": "img_tableBg", "rect": {"x": 0, "y": 0, "width": 608, "height": 1075}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 608, "height": 1075}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-304, -537.5, 0, 304, -537.5, 0, -304, 537.5, 0, 304, 537.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1075, 608, 1075, 0, 0, 608, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -304, "y": -537.5, "z": 0}, "maxPos": {"x": 304, "y": 537.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [4], [13]], [[{"name": "default_editbox_bg", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [4], [14]], [[{"name": "img_fineTurning", "rect": {"x": 0, "y": 0, "width": 29, "height": 242}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 29, "height": 242}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-14.5, -121, 0, 14.5, -121, 0, -14.5, 121, 0, 14.5, 121, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 242, 29, 242, 0, 0, 29, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -14.5, "y": -121, "z": 0}, "maxPos": {"x": 14.5, "y": 121, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [4], [15]], [[[32, "gameUI"], [25, "gameUI", 33554432, [-10, -11, -12, -13, -14, -15, -16, -17, -18], [[2, -2, [0, "09sJoyf/NFApgPW4HFZYS/"], [5, 750, 1334]], [37, false, 45, 750, 1334, -3, [0, "1eIPS75W9LXLNSJxhUA/fe"]], [44, -9, [0, "carC7iu+VLkrMXNzUlh/zB"], [62, 63], -8, -7, -6, -5, [66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81], -4, 61, 64, 65]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [34, "table", 33554432, [-35, -36], [[[2, -19, [0, "8faCjl3sVPYpuPHqYZlAku"], [5, 608, 1075]], [5, -20, [0, "1aqbit2xNJUIFs5cbrxKjO"], 4], [15, 4, 4, 0, -21, [0, "390zNsvlBKc6PQhbC703dK"], [[[0, 253.8, 30.1], [0, 256, 15.9], [0, 256.1, 4.9], [0, 256, -9.6], [0, 255.2, -23.8], [0, 250.9, -38.1], [0, 293.2, -3.1]], 8, 8, 8, 8, 8, 8, 8]], [15, 5, 4, 0, -22, [0, "4d2CsOkkBGGIQ1m2Zbfc6t"], [[[0, 213.7, 486.5], [0, 227.3, 486.6], [0, 233.7, 480.2], [0, 240.7, 473], [0, 249.1, 463.1], [0, 252.3, 446], [0, 284.8, 482.8], [0, 230, 526.6]], 8, 8, 8, 8, 8, 8, 8, 8]], [15, 3, 4, 0, -23, [0, "2eQccYvUhOpbhuY30gMRVQ"], [[[0, 213.6, -484.7], [0, 228.1, -481.6], [0, 238, -470.4], [0, 249.4, -463.4], [0, 253, -448.6], [0, 302, -485.7], [0, 236.4, -526]], 8, 8, 8, 8, 8, 8, 8]], [15, 6, 4, 0, -24, [0, "abLcvtmYhLKIWo67YwEU/z"], [[[0, -211.7, 486.4], [0, -229.3, 486.3], [0, -238.8, 474.8], [0, -251.2, 464.8], [0, -253.1, 445.3], [0, -288.3, 482.8], [0, -248.6, 521.1]], 8, 8, 8, 8, 8, 8, 8]], [15, 1, 4, 0, -25, [0, "c8juKs24FCe5fcplzWFE0h"], [[[0, -252.7, 24.9], [0, -254.5, 11.3], [0, -251.4, -2.5], [0, -254, -14.9], [0, -249.8, -34.2], [0, -293.8, -3.3]], 8, 8, 8, 8, 8, 8]], [15, 2, 4, 0, -26, [0, "3d5U8pQGJPr7COlSu6DoCg"], [[[0, -205, -485.8], [0, -228.2, -484.4], [0, -238.3, -470.6], [0, -249.6, -464.3], [0, -252.7, -442.7], [0, -286.8, -475.4], [0, -230, -526.6]], 8, 8, 8, 8, 8, 8, 8]], [49, true, false, 0, -27, [0, "fcFBVDZZZInY93U+Gf3e9R"]], -28, [16, 8, 0.8, -29, [0, "84IYahsFVOXoTEQ51PouwF"], [0, 244.5, -236.4], [5, 18, 396]], [16, 8, 0.8, -30, [0, "18Sdrf36BOUZ0/dqjXqubd"], [0, 244.5, 233], [5, 18, 405]], [16, 8, 0.8, -31, [0, "b304chTiJIfKdlyNay9Rn5"], [0, -243.6, -236.4], [5, 18, 396]], [16, 8, 0.8, -32, [0, "35150BK/1B9qWzKAIvn36C"], [0, -243.6, 233], [5, 18, 405]], [16, 8, 0.8, -33, [0, "fd0ihfifJC/KFypRnVSDon"], [0, 0, 478], [5, 400, 20]], [16, 8, 0.8, -34, [0, "48eTsBlu5MPrvAkXQQ9aXM"], [0, 0, -478], [5, 400, 20]]], 4, 4, 4, 4, 4, 4, 4, 4, 4, 1, 4, 4, 4, 4, 4, 4], [1, "58621eULhIfazhZxUQ+e0E", null, null, null, 1, 0]], [19, "TestPanel", false, 33554432, 1, [-47, -48, -49, -50, -51, -52, -53, -54, -55, -56, -57, -58, -59, -60], [[2, -37, [0, "53rFsDo5FOzqYCBMM5oRp4"], [5, 300, 360]], [9, 0, -38, [0, "d4S5yKjeNE66O3E9OooYo7"], 46], [50, -46, [0, "9b8WnLwYhNR5m0SROGBH6O"], -45, -44, -43, -42, -41, -40, -39]], [1, "32Ith4d2NIOr7AWA4UePNx", null, null, null, 1, 0], [1, 5.179, 38.697, 0]], [35, "cue", 33554432, [-65, -66, -67, -68, -69, -70], [[[3, -61, [0, "eaOBs2WYlC5YtnH/NTndlG"], [5, 44, 580], [0, 0.4, 1]], [9, 0, -62, [0, "56wow6qYpGf5/S516PTpWV"], 12], -63, [51, -64, [0, "44z/c5qRtEWJEtkRmbuGbB"]]], 4, 4, 1, 4], [1, "71gS6zjaJI6KN9t2ok7rT8", null, null, null, 1, 0], [1, 3.0190000000000055, -969.402, 0]], [14, "tableLayer", 33554432, 1, [-72, 2, -73, 4, -74, -75, -76, -77], [[36, -71, [0, "e1s1yTIgJKOr2/DDBcMsNl"]]], [1, "75Fc9Vju1C6o5LVDAllCAD", null, null, null, 1, 0], [1, 0, -78, 0]], [10, "sliderbar", 33554432, 5, [-81, -82, -83], [[[2, -78, [0, "46j37UvPpLlKj9VVdvpbbd"], [5, 100, 444]], -79, [29, -80, [0, "5bJxb7N1NLcaopVNY5TMM5"]]], 4, 1, 4], [1, "c12QWemNtHvJea1LJ6Onyg", null, null, null, 1, 0], [1, -330, -248, 0]], [25, "node", 33554432, [-86, -87, -88, -89], [[2, -84, [0, "82MG/821dBka9p4JPO4jzg"], [5, 350, 102]], [38, 18, 325, 325, 100, -85, [0, "e2GQ9mhXNE7JCqxWWI/qxm"]]], [1, "caqynfPmhLpLoAT10s0OC9", null, null, null, 1, 0]], [14, "mask", 33554432, 6, [-94], [[2, -90, [0, "73kMIkoItNpZrarmEcdHCa"], [5, 40, 406]], [52, -91, [0, "f9CYmuSeZI3YagylWMsz2t"]], [54, -92, [0, "7feDNUvX9N8L4Ua16mG/XC"], [4, 16777215]], [22, -93, [0, "38l3EMqiVBx5gcnt9puLYf"]]], [1, "375BvBb7tEj4D6KDpJWzxI", null, null, null, 1, 0], [1, 4, -5, 0]], [14, "fineTurning", 33554432, 5, [-98, -99], [[2, -95, [0, "6ei92CDz1PgoXtDEuiZ77T"], [5, 60, 270]], [55, -96, [0, "d9V6jLi0BPA705CfU20wae"]], [29, -97, [0, "b7vK/U8UJEurLrkoAvOOwW"]]], [1, "687NTRBctGUaTpzHgJVJLF", null, null, null, 1, 0], [1, 330, -336, 0]], [14, "topLayer", 33554432, 1, [-102, 7], [[2, -100, [0, "5aBnI1X6RNDJHdXaf7xPxi"], [5, 750, 102]], [39, 41, -2.5757174171303632e-14, 2.5757174171303632e-14, 618, 750, 1, -101, [0, "c2n+Lk9RlJobiBQIS2lWCn"]]], [1, "54UYohG2hAsrOBDSYNpe1E", null, null, null, 1, 0], [1, 0, 616, 0]], [4, "icon_set", 33554432, 10, [[2, -103, [0, "7amsXNIpNEKrr2urpX3d8y"], [5, 61, 62]], [5, -104, [0, "c40UglVXdB3oF/lHaIqkGv"], 23], [56, -106, [0, "ebvItHQbhBZb97eTMY2A61"], [[20, "38f0e6Dj2RNGKg7QovKytkK", "onClickSet", 1]], -105], [40, 34, 19, 19, 6, 61, 1, -107, [0, "00rlbj3sVF97gycDAfbjyK"], 10]], [1, "ccEizfp9tCaqTFO1J7GgBz", null, null, null, 1, 0], [1, 325.5, 6, 0]], [14, "saveBtn", 33554432, 3, [-112], [[2, -108, [0, "b1bu0b4tNCGpsFupBs/v0y"], [5, 70, 36]], [6, 1, 0, -109, [0, "caEaennpBA2KpBd+nBRobU"], 34], [23, 2, -111, [0, "danFcjnFZGnLNL4YqJUaKn"], [[20, "27bbdYLsvZKsLpWyOKbnUd9", "saveData", 3]], [4, **********], -110, 35, 36, 37, 38]], [1, "57UM6+jcxCNaO3LRAAwVPj", null, null, null, 1, 0], [1, 1.261, -160.582, 0]], [10, "minPowerTxt", 33554432, 3, [-116, -117], [[[2, -113, [0, "87o9u85YNIA6IwuanoLS54"], [5, 86, 40]], [6, 1, 0, -114, [0, "99Z69jRtlJT7JTlBzzyd1R"], 39], -115], 4, 4, 1], [1, "18SZ5fIklO77KM9FLGEJc7", null, null, null, 1, 0], [1, -11.734, 156.984, 0]], [10, "maxPowerTxt", 33554432, 3, [-121, -122], [[[2, -118, [0, "9bbHCorntFEojSD+ATQhO0"], [5, 96, 40]], [6, 1, 0, -119, [0, "14qRpTgeRFCIYKmAuDnqkD"], 40], -120], 4, 4, 1], [1, "30VCdAaAtCtYk9oMOJKnh5", null, null, null, 1, 0], [1, 92.334, 157.257, 0]], [10, "powerRange", 33554432, 3, [-126, -127], [[[2, -123, [0, "1cEzvnV39G+rgXxRAd8U5C"], [5, 180, 40]], [6, 1, 0, -124, [0, "adUeTJtWNO36dsDjj9PU3k"], 41], -125], 4, 4, 1], [1, "6eqnsRADJLkbXcEYNDfuAe", null, null, null, 1, 0], [1, 33.862, 108.568, 0]], [10, "restitutionTxt", 33554432, 3, [-131, -132], [[[2, -128, [0, "f2qPQRVF5MkLOg03jmTwsI"], [5, 100, 40]], [6, 1, 0, -129, [0, "2fDz+AsMNN9K8vzezzQ9K8"], 42], -130], 4, 4, 1], [1, "c98imtBZlBtYm6DmUn32yc", null, null, null, 1, 0], [1, 41.334, 11.764, 0]], [10, "frictionTxt", 33554432, 3, [-136, -137], [[[2, -133, [0, "86cOlk2dxPC6h3KoEMQYGR"], [5, 100, 40]], [6, 1, 0, -134, [0, "22gqaF50ZP/4+C9SgfPhQa"], 43], -135], 4, 4, 1], [1, "1fq0gof+9O3oNbaCcTwi+b", null, null, null, 1, 0], [1, 40.484, -35.592, 0]], [10, "linearDampTxt", 33554432, 3, [-141, -142], [[[2, -138, [0, "cfDK/h9FtIo4UQMqeAcW19"], [5, 100, 40]], [6, 1, 0, -139, [0, "85d5PVyQNENp11AogWEoIY"], 44], -140], 4, 4, 1], [1, "f4MPUyW5xKX5srJOlZzLXP", null, null, null, 1, 0], [1, 40.998, 60.702, 0]], [10, "tableLinearDampTxt", 33554432, 3, [-146, -147], [[[2, -143, [0, "e8LNDHeh5DaY5Aq02AXWeb"], [5, 100, 40]], [6, 1, 0, -144, [0, "14nyMQhH5DxZrJQtrJbx9b"], 45], -145], 4, 4, 1], [1, "abTtKUCPtBmaoL/3dTPgrn", null, null, null, 1, 0], [1, 40.223, -81.642, 0]], [19, "test8Btn", false, 33554432, 1, [-152], [[2, -148, [0, "8a1YvmLNZGwYAXlFzV8Wl6"], [5, 100, 40]], [6, 1, 0, -149, [0, "65DS0bchtEb4J/3RCDIraq"], 48], [23, 2, -151, [0, "65xVYRRxlLqKeNa9Xqg/3c"], [[20, "38f0e6Dj2RNGKg7QovKytkK", "test8Object", 1]], [4, **********], -150, 49, 50, 51, 52]], [1, "82bsr38rNHRapO4f3VyICr", null, null, null, 1, 0], [1, 166.296, 444.634, 0]], [19, "testBtn", false, 33554432, 1, [-157], [[2, -153, [0, "1cUPq09PNLsZnhsLeo71gU"], [5, 80, 40]], [6, 1, 0, -154, [0, "56h7blxE9DfKj6OM08RwcK"], 54], [23, 2, -156, [0, "c1hHLXfHpN6Z7xyQoyftuB"], [[20, "38f0e6Dj2RNGKg7QovKytkK", "testEnterBall", 1]], [4, **********], -155, 55, 56, 57, 58]], [1, "8dZCiw+3ZMZYvKGFr3feYp", null, null, null, 1, 0], [1, 35.383, 444.656, 0]], [19, "inputBallId", false, 33554432, 1, [-163, -164], [[2, -158, [0, "1f8k2RocdBdrU+rc+DhOUm"], [5, 200, 50]], [6, 1, 0, -159, [0, "09d39trSBOuaxBdF2Fgq86"], 59], [57, 8, -162, [0, "dfXx4QSUhHhIBEwWJNL3Yx"], -161, -160, 60]], [1, "a6XzyQsZhIOoYfb1q7fNh4", null, null, null, 1, 0], [1, -105.271, 444.889, 0]], [33, "fineTurningBgMask", 33554432, 9, [-168], [[2, -165, [0, "46F0enk2VPT5PejEAnZNS0"], [5, 59, 270]], [53, 3, 1, -166, [0, "90Mt95rztMXZymqPcTgclQ"]], [5, -167, [0, "65HJoh6eRF5qN6xUrjr7a1"], 19]], [1, "10Ujn9jPJCo7j10d/EdDF4", null, null, null, 1, 0]], [14, "hand", 33554432, 5, [-171, -172], [[2, -169, [0, "71N7OKgpdDVaMbHXMepb2F"], [5, 130, 130]], [45, 0, -170, [0, "6328Y5vS5GD5MLNkv4GFQx"]]], [1, "1fYcFdprVHOJBk8M3GgCu6", null, null, null, 1, 0], [1, 0, 118, 0]], [4, "lblMode", 33554432, 7, [[3, -173, [0, "32kNC/N+pCx5rQPFyk1wBL"], [5, 180, 50.4], [0, 1, 0.5]], [58, "8 Ball Pool", 26, 26, 1, false, false, -174, [0, "d9sNYrJVBIpbj01nu4w12V"], 26], [41, 8, 10.195000000000007, -175, [0, "bcda4aBMJOd6CT+vBhOwog"]], [64, "ball8", -176, [0, "b0H76iPWpPar9VIDSjbE2p"]]], [1, "834AFRL7JEWLjzFv9Ut+eG", null, null, null, 1, 0], [1, 15.194999999999993, 8, 0]], [4, "img_topBg", 33554432, 1, [[2, -177, [0, "d8UMcjUD1GI43oI43dk/gR"], [5, 750, 102]], [5, -178, [0, "20ueQQxHlISpRUiGDHWmPQ"], 0], [42, 17, -179, [0, "a3d/2piwVFtrrlcgiXN6Ng"]]], [1, "b5C6D6PF5CFJOPdWR7RNeN", null, null, null, 1, 0], [1, 0, 616, 0]], [26, "slider<PERSON>ower", false, 33554432, 6, [[2, -180, [0, "d6Lo3O07dFu5uYAKLuDig5"], [5, 42, 420]], [22, -181, [0, "0541hWcPJCcr0eGDaKTmLq"]], [5, -182, [0, "8bvRGOAyxAGq+UY9FOvaGQ"], 14]], [1, "f7mcDTFVxNuIC/g/Ovefan", null, null, null, 1, 0]], [17, "img_fineTurning_bg", 33554432, 9, [[2, -183, [0, "d94+dZDFVAAIe0RvU11gHl"], [5, 59, 270]], [5, -184, [0, "fef1pJKPtKAqQtwLEUleo5"], 16], [22, -185, [0, "3crk14VmFJPJyqnYmfyTBP"]]], [1, "97xO6+8DFHcZ/BIajSY7oE", null, null, null, 1, 0]], [4, "img_coin", 33554432, 7, [[2, -186, [0, "54YS3MWrBBbrtZmS1sooZZ"], [5, 45, 45]], [9, 0, -187, [0, "092RQXejFHPobazOoFke+A"], 25], [28, 32, 116.292, -188, [0, "6b1yYX3chDVo3rL9PwzDlJ"]]], [1, "823DFRpzNGGadYDDIDoQrm", null, null, null, 1, 0], [1, 36.208, 6, 0]], [7, "txt_winGold", 33554432, 7, [[[3, -189, [0, "741qABofBHPZsmbxdo6dEd"], [5, 0, 50.4], [0, 0, 0.5]], -190, [28, 32, 95.806, -191, [0, "b1Zu6zJrJAML3o+vhUgdjj"]]], 4, 1, 4], [1, "ddVRNj9yZDNZZmVYM2tVro", null, null, null, 1, 0], [1, 79.194, 7, 0]], [4, "player<PERSON>ode", 33554432, 1, [[2, -192, [0, "101FxzOdJLv4wPoUGN4Z/5"], [5, 750, 100]], [43, 42, 514, 750, 1, -193, [0, "44VXvS/SpFt5oXc17/lT9T"]]], [1, "d6BIy7azNLzo+15mUweeQv", null, null, null, 1, 0], [1, 0, 514, 0]], [4, "img_package", 33554432, 5, [[2, -194, [0, "e32rRGqPJGzK0MZeqZ2KGC"], [5, 537, 108]], [5, -195, [0, "baSXiqARxN2YIRci+tvmZs"], 1]], [1, "4befvDsHRNqqLFGLP2Yq3/", null, null, null, 1, 0], [1, -8, -539, 0]], [4, "openningArea", 33554432, 2, [[2, -196, [0, "7fCtRXP3VInpKgxFQOLOaI"], [5, 504.166, 257.273]], [9, 0, -197, [0, "bayDdC99FNPZm0C9ohkGJm"], 2]], [1, "8aBdoQHzdC4KUdx3M0BO1m", null, null, null, 1, 0], [1, -1.2369999999999752, -360.283, 0]], [4, "bigCircle", 33554432, 2, [[2, -198, [0, "403WxBzpFNYbMHpvfWQnG9"], [5, 248, 248]], [9, 0, -199, [0, "78DOR7ujRM4blaTpb+wmdv"], 3]], [1, "878Y39pIVK7orZP5a3pi52", null, null, null, 1, 0], [1, 0, 40, 0]], [17, "touchNode", 33554432, 5, [[2, -200, [0, "ecQdrJ/GtCG5uWBh3qprG2"], [5, 1000, 1668]], [9, 0, -201, [0, "9an6Uu9EBInIuwZGsDhOTZ"], 5]], [1, "c8kOgV629FwZ3+KV0Wgr9n", null, null, null, 1, 0]], [4, "ray", 33554432, 4, [[3, -202, [0, "f3Q6dldZVOjo7ocuRTfj0M"], [5, 41, 61], [0, 0.5, 0]], [46, 1, -203, [0, "c53srmEbtMNoU+0SmjEjd4"], 6]], [1, "acNvgpg89DLbn1UKcfacTo", null, null, null, 1, 0], [1, -0.298, 4.945, 0]], [4, "lineH", 33554432, 4, [[3, -204, [0, "7esrKhI0JDRpLFYpuSfcXw"], [5, 22, 61], [0, 0.5, 0.18]], [5, -205, [0, "0fIHnb1QBMT69J8wQzat6H"], 7]], [1, "c51E5SIiRLZbDt/dKOOdqC", null, null, null, 1, 0], [1, 51.05, -144.791, 0]], [4, "circle", 33554432, 4, [[2, -206, [0, "a47n9kW2JBKYLdWDTcA34P"], [5, 47, 39]], [5, -207, [0, "66HnQCbxlPTbPuorD1Kc/P"], 8]], [1, "7cjEn95YpBZ6zXe3oUGg+h", null, null, null, 1, 0], [1, 0, 11.782, 0]], [4, "lineR", 33554432, 4, [[3, -208, [0, "13nAGgwjJMub7ngWwnOB5+"], [5, 22, 47], [0, 0.5, 0.2]], [5, -209, [0, "10Ejv2gH9FHolOeAlCvReJ"], 9]], [1, "03r59ecpdFr50EyrPnybWC", null, null, null, 1, 0], [1, 70.415, -146.551, 0]], [26, "forbidSet", false, 33554432, 4, [[2, -210, [0, "a9yq9dYZRC37/sxHyJgDiN"], [5, 30, 30]], [9, 0, -211, [0, "53Q5i9AmlDapam+nNJnwNc"], 10]], [1, "93e0sSp25IqLLOg4ES5UAs", null, null, null, 1, 0]], [27, "hitball", false, 33554432, 4, [[2, -212, [0, "8dpa6OEalGI6JIZvFJuQ7t"], [5, 64, 47]], [5, -213, [0, "caKrRLH19IErwV36x9Mjf2"], 11]], [1, "b25MyQJ09MG7+8B0umUIhT", null, null, null, 1, 0], [1, 0, -9.395, 0]], [17, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 33554432, 6, [[2, -214, [0, "b8Rv0xJ7FOo6PlJX9MC6D1"], [5, 64, 444]], [5, -215, [0, "5aWpwMnFRALJM8kwJSqBqL"], 13]], [1, "3dPG65iv1CcYOq5BxbwJDG", null, null, null, 1, 0]], [17, "sliderCue", 33554432, 8, [[2, -216, [0, "ed7GZZCtpO87uWXJKC8Zl7"], [5, 39, 406]], [47, 1, 0, 1, -0.7, -217, [0, "a3oc+5/bpNBpLMtp8RSmFn"], 15]], [1, "39EqvdHHhDJrEC1wMIv0Vt", null, null, null, 1, 0]], [4, "scale", 33554432, 23, [[2, -218, [0, "47EZG6msJBZruTVommCtcV"], [5, 29, 242]], [48, 2, 0, -219, [0, "b0T2/tELlOVpYuB337xxCg"], 17, 18]], [1, "4bSvncs1NN558A2k/Mb8f+", null, null, null, 1, 0], [1, -1, 0, 0]], [17, "forbidSet", 33554432, 24, [[2, -220, [0, "3eXXkYXKhP9JSG8krmIb+n"], [5, 30, 30]], [9, 0, -221, [0, "f7+r7yjK1NvLsNXss0oZi6"], 20]], [1, "db5HPnKm1KxoR9G0l5A8TG", null, null, null, 1, 0]], [4, "hand", 33554432, 24, [[2, -222, [0, "ffxGKOTs1FupdbFl1AMzUP"], [5, 56, 41]], [9, 0, -223, [0, "03igwFMttL87ZD7STgp5q3"], 21]], [1, "d7h4phPzNF97zHwD5S43aa", null, null, null, 1, 0], [1, 23.964, -0.844, 0]], [27, "enterBallSk", false, 33554432, 5, [[3, -224, [0, "9b9y+AQkJAYZdUTVW7xNe4"], [5, 957.6799926757812, 2073.68994140625], [0, 0, 0]], [65, "default", false, 0, false, -225, [0, "4dDjmG3QlMtr222bP6//4K"], 22]], [1, "aeMjM+GBNDGZpefvxTop+Y", null, null, null, 1, 0], [1, -250, 480, 0]], [4, "image_title_bg", 33554432, 7, [[2, -226, [0, "762fH22ZxIJbBLAdq767Pg"], [5, 350, 54]], [6, 1, 0, -227, [0, "e51yfniutE+YDciwBIy1pg"], 24]], [1, "5fXUiesLZEB61yWSK32qJK", null, null, null, 1, 0], [1, 0, 6, 0]], [4, "Label", 33554432, 3, [[3, -228, [0, "78NY5+yDxBc61pjv4idsYh"], [5, 86.66015625, 31.5], [0, 0, 0.5]], [59, "击球力度:", 20, 20, 25, false, true, -229, [0, "50E3fPF39NnY73TiF5w+sr"], 27]], [1, "a7IaxQ5GJNY6XeWWG5snno", null, null, null, 1, 0], [1, -144.99, 156.449, 0]], [4, "Label", 33554432, 3, [[3, -230, [0, "484UQds3hPG6D5y7QvC914"], [5, 126.66015625, 31.5], [0, 0, 0.5]], [18, "白球衰减速度:", 0, 20, 20, 25, false, true, -231, [0, "0ejgwcXS5EtqT+ZXOd7yM3"], 28]], [1, "7blUubTeZIkoZb2OBI0vfa", null, null, null, 1, 0], [1, -145, 63.152, 0]], [4, "Label", 33554432, 3, [[3, -232, [0, "5dUFpA4kxLZa0r27c6ytRW"], [5, 106.66015625, 31.5], [0, 0, 0.5]], [18, "白球反弹力:", 0, 20, 20, 25, false, true, -233, [0, "b2Fi5Ic0VEJ5w0bHHZjpKP"], 29]], [1, "e5RTbuxOhE54JcZojxgqwB", null, null, null, 1, 0], [1, -146.24, 9.487, 0]], [4, "Label", 33554432, 3, [[3, -234, [0, "1ax8UXFalMI4rOAgzgsAYu"], [5, 106.66015625, 31.5], [0, 0, 0.5]], [18, "白球摩擦力:", 0, 20, 20, 25, false, true, -235, [0, "59M+QUL8VOU7XGrYAxZLYh"], 30]], [1, "beuDaM0shN07jdk5Q1T973", null, null, null, 1, 0], [1, -144.925, -37.869, 0]], [4, "Label", 33554432, 3, [[3, -236, [0, "1a8dvmK4tO6rtAz43pdzim"], [5, 126.66015625, 31.5], [0, 0, 0.5]], [18, "库边衰减速度:", 0, 20, 20, 25, false, true, -237, [0, "0eExb2wiRI6aJbHgLvIdPV"], 31]], [1, "61pUJ6NqxD/reuDR0vt0wP", null, null, null, 1, 0], [1, -146.854, -81.736, 0]], [4, "Label", 33554432, 3, [[3, -238, [0, "5fCi1vE4lNI5wP0WiPI27g"], [5, 304.501953125, 56.49999999999999], [0, 0, 0.5]], [18, "他球速度衰减系数0.6;  反弹力0.85\n摩擦力0", 0, 20, 20, 25, false, true, -239, [0, "ad81EjoT9MGL3ov7qPS/tG"], 32]], [1, "7bQUCCdS1CwK0K09buXsbM", null, null, null, 1, 0], [1, -149.242, -127.939, 0]], [21, "Label", 512, 33554432, 12, [[2, -240, [0, "9fAjoowWZItJilAjkZBILs"], [5, 60, 35]], [24, "save", 20, 20, 1, false, false, -241, [0, "f0GsnkXflDUrcyaInnoWVB"], [4, 4278190080], 33]], [1, "b774T15UBFzpA90IDrbdQ/", null, null, null, 1, 0]], [8, "TEXT_LABEL", false, 33554432, 13, [[[3, -242, [0, "8eAauBReBLNbzv3vEOQlq3"], [5, 84, 40], [0, 0, 1]], -243], 4, 1], [1, "d6hKkqm8BED6tZZWeFjsEU", null, null, null, 1, 0], [1, -41, 20, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 13, [[[3, -244, [0, "a9GYnn+qBMroLqBUuddCOE"], [5, 84, 40], [0, 0, 1]], -245], 4, 1], [1, "06umR3/oJLQ55JveRXQ/eC", null, null, null, 1, 0], [1, -41, 20, 0]], [8, "TEXT_LABEL", false, 33554432, 14, [[[3, -246, [0, "5egiG/pi9C85JugDizsnxP"], [5, 94, 40], [0, 0, 1]], -247], 4, 1], [1, "f9bvzEp3VOqrAWta/6aBrB", null, null, null, 1, 0], [1, -46, 20, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 14, [[[3, -248, [0, "e19HLaQJ1CM5I7GzxDqPzC"], [5, 94, 40], [0, 0, 1]], -249], 4, 1], [1, "24V6n9MsFEhJxOwcWVnQms", null, null, null, 1, 0], [1, -46, 20, 0]], [8, "TEXT_LABEL", false, 33554432, 15, [[[3, -250, [0, "e92M+UCkxCmqja3Nv/KwJS"], [5, 178, 40], [0, 0, 1]], -251], 4, 1], [1, "89cITH3vxLg73+D3pCyi6n", null, null, null, 1, 0], [1, -88, 20, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 15, [[[3, -252, [0, "88qghycMdNbYJxL+DFG3mK"], [5, 178, 40], [0, 0, 1]], -253], 4, 1], [1, "e0JlVLunhFcIyvdUTs5kOG", null, null, null, 1, 0], [1, -88, 20, 0]], [8, "TEXT_LABEL", false, 33554432, 16, [[[3, -254, [0, "82OxlnNvdBAbZBlzG5TgMp"], [5, 98, 40], [0, 0, 1]], -255], 4, 1], [1, "9eTSsAvTVMVbpHXoNwjIAo", null, null, null, 1, 0], [1, -48, 20, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 16, [[[3, -256, [0, "f9reWRvcBAD64qX6kX6gfr"], [5, 98, 40], [0, 0, 1]], -257], 4, 1], [1, "d2sSMOPQdKd7GPmIvvEsR/", null, null, null, 1, 0], [1, -48, 20, 0]], [8, "TEXT_LABEL", false, 33554432, 17, [[[3, -258, [0, "07VbpG045NYpP/TzXt3j3D"], [5, 98, 40], [0, 0, 1]], -259], 4, 1], [1, "baDENsILlCNIvV9fLkAiGT", null, null, null, 1, 0], [1, -48, 20, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 17, [[[3, -260, [0, "efnca5rk9JOINay3G8YPca"], [5, 98, 40], [0, 0, 1]], -261], 4, 1], [1, "0aXoLsfWJMDKnQfwbP7AHB", null, null, null, 1, 0], [1, -48, 20, 0]], [8, "TEXT_LABEL", false, 33554432, 18, [[[3, -262, [0, "10L5r7dxZEcLvpIjg/qn1A"], [5, 98, 40], [0, 0, 1]], -263], 4, 1], [1, "4ay9MC1ctGo4DIO6jtKfxV", null, null, null, 1, 0], [1, -48, 20, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 18, [[[3, -264, [0, "a4yIug33ZKGLU6yBh//gLP"], [5, 98, 40], [0, 0, 1]], -265], 4, 1], [1, "528vl3Hc9DI7+jh<PERSON><PERSON><PERSON><PERSON>", null, null, null, 1, 0], [1, -48, 20, 0]], [8, "TEXT_LABEL", false, 33554432, 19, [[[3, -266, [0, "99VEsrJYdJPb78VCZSJj1Z"], [5, 98, 40], [0, 0, 1]], -267], 4, 1], [1, "46btxYSIlCwrIxEQ5fNwLe", null, null, null, 1, 0], [1, -48, 20, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 19, [[[3, -268, [0, "f3HxANiZ9OWa92GQh3scMQ"], [5, 98, 40], [0, 0, 1]], -269], 4, 1], [1, "baM9+njZtL0ZpYU0HoE1Yi", null, null, null, 1, 0], [1, -48, 20, 0]], [21, "Label", 512, 33554432, 20, [[2, -270, [0, "80+749NuVEcI7NO4L3ailP"], [5, 100, 40]], [24, "进黑8", 20, 20, 1, false, false, -271, [0, "a0rMkcMqJBiLhSsmF6kvKg"], [4, 4278190080], 47]], [1, "31iMtIioJLxaonocE8XV9h", null, null, null, 1, 0]], [8, "test<PERSON><PERSON><PERSON>", false, 33554432, 1, [[[2, -272, [0, "beG9AkYL9Kk5BJeVDy6ZF0"], [5, 50.70703125, 50.4]], -273], 4, 1], [1, "63dmh5oNBONL962Zn3l+fG", null, null, null, 1, 0], [1, 0, 567.684, 0]], [21, "Label", 512, 33554432, 21, [[2, -274, [0, "78sFE/wldAr4F7xqyWaZjZ"], [5, 100, 40]], [24, "进球", 20, 20, 1, false, false, -275, [0, "50/iogPRFHlL64V8G+E146"], [4, 4278190080], 53]], [1, "fbwNKsxQlMPLKygatAI7U/", null, null, null, 1, 0]], [8, "TEXT_LABEL", false, 33554432, 22, [[[3, -276, [0, "d7447P7MlBLqfAp2gfhiqs"], [5, 198, 50], [0, 0, 1]], -277], 4, 1], [1, "1aq2LsTshJ1bFXL5F9yCPj", null, null, null, 1, 0], [1, -98, 25, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 22, [[[3, -278, [0, "c4Qxp2KvVAypI2/FnCYa+B"], [5, 198, 50], [0, 0, 1]], -279], 4, 1], [1, "fekXwz/8FK0pBnWWt71M9R", null, null, null, 1, 0], [1, -98, 25, 0]], [66, 2, [0, "dfX2RypDdIRIJpo0c7FXPm"]], [67, 4, [0, "d7k5arhp1DUpdJIAyt+onF"]], [68, 6, [0, "9fGlaHmXhLiaXZ6ePRSrDH"]], [60, "", 0, 32, 32, false, false, 30, [0, "59Isj+kFZIfYFIuMn7uLWB"], [4, 4284217855]], [12, "", 0, 40, 20, 1, false, false, 56, [0, "9cODEqe4JEEqpA4b8+UKJW"]], [13, "1", 0, 20, 20, 1, false, false, 57, [0, "0d3Cjd0mZElKAdDKOeh8rp"], [4, 4290493371]], [11, 6, 8, 13, [0, "d3EcSnMGtNBYwhzKhnNqo2"], 79, 80], [12, "", 0, 40, 20, 1, false, false, 58, [0, "20vIeBeQdA9pkx+TtxY1Bl"]], [13, "5", 0, 20, 20, 1, false, false, 59, [0, "35Vq0VV7pDJL6B<PERSON><PERSON><PERSON>yi"], [4, 4290493371]], [11, 6, 8, 14, [0, "858TT7w7tKnKFEFOgkPZQh"], 82, 83], [12, "", 0, 40, 20, 1, false, false, 60, [0, "d5iDCeIN9IGLw7/wjjcHrj"]], [13, "80", 0, 20, 20, 1, false, false, 61, [0, "a7Uil3HgpDJqn4ECgOdvJV"], [4, 4290493371]], [11, 6, 8, 15, [0, "bf0Lq9AhNBrbDnxbkxKm75"], 85, 86], [12, "", 0, 40, 20, 1, false, false, 62, [0, "51n9abnEJA8ZaXcscGo9k+"]], [13, "0.85", 0, 20, 20, 1, false, false, 63, [0, "d8OVvqDEdFJIQu1BqAx1BS"], [4, 4290493371]], [11, 6, 8, 16, [0, "a6R/SdSAVAWY/JIsvTGWMo"], 88, 89], [12, "", 0, 40, 20, 1, false, false, 64, [0, "e7eAEMwg9KaKs7DRXUhydw"]], [13, "0", 0, 20, 20, 1, false, false, 65, [0, "ccXgv3Ft1BL71Yrt/rC27J"], [4, 4290493371]], [11, 6, 8, 17, [0, "dbhFFOAkNPNZ0f/BsQnW8O"], 91, 92], [12, "", 0, 40, 20, 1, false, false, 66, [0, "b5F6+kVR9GFqBw6pFJXEWf"]], [13, "0.6", 0, 20, 20, 1, false, false, 67, [0, "9818w4LzVKnpK9KT3mIz7h"], [4, 4290493371]], [11, 6, 8, 18, [0, "efhfFICZZMK7lSOjLiUStF"], 94, 95], [12, "", 0, 40, 20, 1, false, false, 68, [0, "6ck8NSqxdO+IZh8ycagepn"]], [13, "0-1", 0, 20, 20, 1, false, false, 69, [0, "1beaFDbgRGlo6VYHcQDdUT"], [4, 4290493371]], [11, 6, 8, 19, [0, "36HgBWgUBELr5aotogvCh3"], 97, 98], [61, 24, 24, 71, [0, "70ykswpp1A+Ig92VsREKwB"]], [62, "", 0, 0, 20, 20, 1, false, 73, [0, "96AUs+IvBMKKuGFPvWDklI"]], [63, "输入球id 1-15", 0, 20, 20, 1, false, 74, [0, "81+vmvrypHC6jqO+YagN0B"], [4, 4290493371]]], 0, [0, 12, 1, 0, 0, 1, 0, 0, 1, 0, 13, 100, 0, 14, 78, 0, 15, 77, 0, 16, 75, 0, 17, 76, 0, 0, 1, 0, -1, 26, 0, -2, 31, 0, -3, 5, 0, -4, 10, 0, -5, 3, 0, -6, 20, 0, -7, 71, 0, -8, 21, 0, -9, 22, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -10, 75, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 33, 0, -2, 34, 0, 0, 3, 0, 0, 3, 0, 18, 99, 0, 19, 93, 0, 20, 90, 0, 21, 96, 0, 22, 87, 0, 23, 84, 0, 24, 81, 0, 0, 3, 0, -1, 49, 0, -2, 50, 0, -3, 51, 0, -4, 52, 0, -5, 53, 0, -6, 54, 0, -7, 12, 0, -8, 13, 0, -9, 14, 0, -10, 15, 0, -11, 16, 0, -12, 17, 0, -13, 18, 0, -14, 19, 0, 0, 4, 0, 0, 4, 0, -3, 76, 0, 0, 4, 0, -1, 36, 0, -2, 37, 0, -3, 38, 0, -4, 39, 0, -5, 40, 0, -6, 41, 0, 0, 5, 0, -1, 32, 0, -3, 35, 0, -5, 6, 0, -6, 9, 0, -7, 24, 0, -8, 47, 0, 0, 6, 0, -2, 77, 0, 0, 6, 0, -1, 42, 0, -2, 27, 0, -3, 8, 0, 0, 7, 0, 0, 7, 0, -1, 48, 0, -2, 29, 0, -3, 30, 0, -4, 25, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 43, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 28, 0, -2, 23, 0, 0, 10, 0, 0, 10, 0, -1, 11, 0, 0, 11, 0, 0, 11, 0, 5, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 5, 12, 0, 0, 12, 0, -1, 55, 0, 0, 13, 0, 0, 13, 0, -3, 81, 0, -1, 56, 0, -2, 57, 0, 0, 14, 0, 0, 14, 0, -3, 84, 0, -1, 58, 0, -2, 59, 0, 0, 15, 0, 0, 15, 0, -3, 87, 0, -1, 60, 0, -2, 61, 0, 0, 16, 0, 0, 16, 0, -3, 90, 0, -1, 62, 0, -2, 63, 0, 0, 17, 0, 0, 17, 0, -3, 93, 0, -1, 64, 0, -2, 65, 0, 0, 18, 0, 0, 18, 0, -3, 96, 0, -1, 66, 0, -2, 67, 0, 0, 19, 0, 0, 19, 0, -3, 99, 0, -1, 68, 0, -2, 69, 0, 0, 20, 0, 0, 20, 0, 5, 20, 0, 0, 20, 0, -1, 70, 0, 0, 21, 0, 0, 21, 0, 5, 21, 0, 0, 21, 0, -1, 72, 0, 0, 22, 0, 0, 22, 0, 25, 102, 0, 26, 101, 0, 0, 22, 0, -1, 73, 0, -2, 74, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, -1, 44, 0, 0, 24, 0, 0, 24, 0, -1, 45, 0, -2, 46, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, -2, 78, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, -2, 79, 0, 0, 57, 0, -2, 80, 0, 0, 58, 0, -2, 82, 0, 0, 59, 0, -2, 83, 0, 0, 60, 0, -2, 85, 0, 0, 61, 0, -2, 86, 0, 0, 62, 0, -2, 88, 0, 0, 63, 0, -2, 89, 0, 0, 64, 0, -2, 91, 0, 0, 65, 0, -2, 92, 0, 0, 66, 0, -2, 94, 0, 0, 67, 0, -2, 95, 0, 0, 68, 0, -2, 97, 0, 0, 69, 0, -2, 98, 0, 0, 70, 0, 0, 70, 0, 0, 71, 0, -2, 100, 0, 0, 72, 0, 0, 72, 0, 0, 73, 0, -2, 101, 0, 0, 74, 0, -2, 102, 0, 27, 1, 2, 6, 5, 4, 6, 5, 7, 6, 10, 279], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 101, 102], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 28, 1, 1, 1, 1, 29, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 1, 7, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 7, 8, 9, 10, 2, 1, 7, 8, 9, 10, 1, 3, 30, -1, -2, 31, 32, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, 2, 2, 2, 3, 2, 2, 3, 2, 2, 3, 2, 2, 3, 2, 2, 3, 2, 2, 3, 2, 2, 3, 2, 2], [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 5, 26, 27, 28, 29, 30, 6, 31, 32, 6, 5, 33, 34, 35, 36, 37, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1, 38, 0, 2, 2, 2, 3, 4, 0, 2, 2, 2, 3, 4, 7, 7, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0]]]]