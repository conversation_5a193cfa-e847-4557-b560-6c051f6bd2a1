[1, 0, ["node", "_cameraComponent", "imgGameBg", "imgBg", "scene", "_parent"], [["cc.Node", ["_name", "_layer", "_id", "_components", "_children", "_lpos", "_parent"], 0, 9, 2, 5, 1], ["cc.Widget", ["_enabled", "_alignFlags", "_originalWidth", "_originalHeight", "_alignMode", "node"], -2, 1], ["cc.SceneAsset", ["_name"], 2], ["cc.Scene", ["_name", "_children", "_globals"], 2, 2, 4], ["cc.Node", ["_name", "_parent", "_components", "_lpos"], 2, 1, 2, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components"], 1, 1, 12], ["cc.Camera", ["_projection", "_priority", "_orthoHeight", "_far", "_visibility", "node", "_color"], -2, 1, 5], ["cc.UITransform", ["node", "_contentSize"], 3, 1, 5], ["cc.Sprite", ["_sizeMode", "node"], 2, 1], ["cc.<PERSON>", ["node", "_cameraComponent"], 3, 1, 1], ["1e8f0ZpUN1Gf631mJvDoVB5", ["node", "imgBg", "imgGameBg"], 3, 1, 1, 1], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyIllumLDR", "_skyColorHDR", "_groundAlbedoHDR", "_skyColorLDR", "_groundAlbedoLDR"], 2, 5, 5, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", ["_useHDR"], 2], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", [], 3], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3]], [[7, 0, 1, 1], [5, 0, 1, 2, 3, 3], [8, 0, 1, 2], [2, 0, 2], [3, 0, 1, 2, 2], [0, 0, 1, 2, 4, 3, 5, 4], [0, 0, 1, 6, 3, 3], [4, 0, 1, 2, 3, 2], [6, 0, 1, 2, 3, 4, 5, 6, 6], [1, 0, 1, 2, 3, 5, 5], [1, 0, 1, 4, 5, 4], [9, 0, 1, 1], [10, 0, 1, 2, 1], [11, 0, 1, 2, 3, 4, 5, 6, 7, 1], [12, 0, 1, 2, 3, 4, 2], [13, 0, 1, 1], [14, 0, 2], [15, 1], [16, 1], [17, 1], [18, 1], [19, 1]], [[3, "main"], [5, "<PERSON><PERSON>", 33554432, "2a1huiRgRNBryUxS2sM++Y", [-8, -9, -10, -11], [[0, -1, [5, 750, 1334]], [11, -3, -2], [10, false, 45, 1, -4], [12, -7, -6, -5]], [1, 375, 667, 0]], [1, "img_bg", 33554432, 1, [[[0, -12, [5, 750, 1334]], -13], 4, 1]], [1, "img_gameBg", 33554432, 1, [[[0, -14, [5, 750, 1334]], -15], 4, 1]], [6, "MainGame", 33554432, 1, [[0, -16, [5, 750, 1334]], [9, false, 45, 750, 1334, -17]]], [4, "main", [1], [13, [14, 0.5208, [2, 0.242613, 0.362617, 0.798746, 0.520833125], [2, 0.241814, 0.361945, 0.798799, 0], [2, 0.5137254901960784, 0.6274509803921569, 0.9019607843137255, 0.5208], [2, 0.5137254901960784, 0.6274509803921569, 0.9019607843137255, 1]], [15, [4, 4283190348], [0, 512, 512]], [16, false], [17], [18], [19], [20], [21]]], [7, "Camera", 1, [-18], [1, 0, 0, 1000]], [8, 0, 1073741824, 667, 2000, 41943040, 6, [4, 0]], [2, 0, 2], [2, 0, 3]], 0, [0, 0, 1, 0, 1, 7, 0, 0, 1, 0, 0, 1, 0, 2, 9, 0, 3, 8, 0, 0, 1, 0, -1, 6, 0, -2, 2, 0, -3, 3, 0, -4, 4, 0, 0, 2, 0, -2, 8, 0, 0, 3, 0, -2, 9, 0, 0, 4, 0, 0, 4, 0, -1, 7, 0, 4, 5, 1, 5, 5, 18], [], [], []]