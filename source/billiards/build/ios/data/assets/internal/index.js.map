{"version": 3, "sources": ["../file:/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/file:/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts", "../file:/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/file:/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts", "../file:/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/file:/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts"], "names": ["ccclass", "disallowMultiple", "executeInEditMode", "menu", "property", "requireComponent", "type", "_decorator", "BuiltinPipelineSettings", "_dec", "_dec2", "_dec3", "Camera", "_dec4", "CCBoolean", "_dec5", "displayName", "_dec6", "group", "id", "name", "_dec7", "style", "CCInteger", "range", "_dec8", "_dec9", "tooltip", "CCFloat", "slide", "_dec10", "_dec11", "Material", "_dec12", "_dec13", "_dec14", "min", "_dec15", "_dec16", "_dec17", "_dec18", "Texture2D", "_dec19", "_dec20", "_dec21", "_dec22", "_dec23", "_dec24", "_class", "_class2", "Component", "constructor", "args", "_initializerDefineProperty", "_descriptor", "_descriptor2", "getPipelineSettings", "_settings", "onEnable", "fillRequiredPipelineSettings", "cameraComponent", "getComponent", "camera", "pipelineSettings", "onDisable", "editorPreview", "_editorPreview", "v", "_tryEnableEditorPreview", "rendering", "undefined", "setEditorPipelineSettings", "_disableEditorPreview", "current", "getEditorPipelineSettings", "Msaa<PERSON>nable", "msaa", "enabled", "value", "msaaSampleCount", "Math", "ceil", "log2", "max", "sampleCount", "shadingScaleEnable", "enableShadingScale", "shadingScale", "bloomEnable", "bloom", "bloomMaterial", "material", "bloomEnableAlphaMask", "enableAlphaMask", "bloomIterations", "iterations", "bloom<PERSON><PERSON><PERSON>old", "threshold", "bloomIntensity", "intensity", "colorGradingEnable", "colorGrading", "colorGradingMaterial", "colorGradingContribute", "contribute", "colorGradingMap", "val", "fxaaEnable", "fxaa", "fxaaMaterial", "fsrEnable", "fsr", "fsrMaterial", "fsrSharpness", "sharpness", "toneMappingMaterial", "toneMapping", "_applyDecoratedDescriptor", "prototype", "configurable", "enumerable", "writable", "initializer", "makePipelineSettings", "Object", "getOwnPropertyDescriptor", "_RF", "pop", "SampleCount", "gfx", "makeMSAA", "X4", "fillRequiredMSAA", "makeHBAO", "radiusScale", "angleBiasDegree", "blurSharpness", "aoSaturation", "need<PERSON><PERSON><PERSON>", "fillRequiredHBAO", "makeBloom", "fillRequiredBloom", "makeColorGrading", "fillRequiredColorGrading", "makeFSR", "fillRequiredFSR", "makeFXAA", "fillRequiredFXAA", "makeToneMapping", "fillRequiredToneMapping", "AABB", "Sphere", "intersect", "geometry", "ClearFlagBit", "Color", "Format", "FormatFeatureBit", "LoadOp", "StoreOp", "TextureType", "Viewport", "scene", "renderer", "CameraUsage", "CSMLevel", "LightType", "forwardNeedClearColor", "clearFlag", "COLOR", "STENCIL", "getCsmMainLightViewport", "light", "w", "h", "level", "vp", "screenSpaceSignY", "shadowFixedArea", "csmLevel", "LEVEL_1", "left", "top", "width", "trunc", "height", "floor", "PipelineConfigs", "isWeb", "isWebGL1", "isWebGPU", "isMobile", "isHDR", "useFloatOutput", "toneMappingType", "shadowEnabled", "shadowMapFormat", "R32F", "shadowMapSize", "Vec2", "usePlanarShadow", "supportDepthSample", "mobileMaxSpotLightShadowMaps", "platform", "Vec4", "setupPipelineConfigs", "ppl", "configs", "sampleFeature", "SAMPLED_TEXTURE", "LINEAR_FILTER", "device", "sys", "isNative", "gfxAPI", "API", "WEBGL", "WEBGPU", "pipelineSceneData", "getMacroBool", "postSettings", "shadowInfo", "shadows", "pipeline", "supportsR32FloatTexture", "RGBA8", "set", "size", "ShadowType", "Planar", "capabilities", "getFormatFeatures", "DEPTH_STENCIL", "x", "clipSpaceSignY", "defaultSettings", "CameraConfigs", "settings", "isMainGameWindow", "renderWindowId", "colorName", "depthStencilName", "enableFullPipeline", "enableProfiler", "remainingPasses", "nativeWidth", "nativeHeight", "enableHDR", "radianceFormat", "copyAndTonemapMaterial", "enableStoreSceneDepth", "sClearColorTransparentBlack", "sortPipelinePassBuildersByConfigOrder", "passBuilders", "sort", "a", "b", "getConfigOrder", "sortPipelinePassBuildersByRenderOrder", "getRenderOrder", "addCopyToScreenPass", "pplConfigs", "cameraConfigs", "input", "assert", "pass", "addRenderPass", "addR<PERSON><PERSON>arget", "CLEAR", "STORE", "addTexture", "setVec4", "addQueue", "QueueHint", "OPAQUE", "addFullscreenQuad", "getPingPongRenderTarget", "prevName", "prefix", "startsWith", "Number", "char<PERSON>t", "length", "ForwardLighting", "lights", "shadowEnabledSpotLights", "_sphere", "create", "_boundingBox", "_rangedDirLightBoundingBox", "cullLights", "frustum", "cameraPos", "spotLights", "baked", "position", "y", "z", "sphereFrustum", "push", "sphereLights", "pointLights", "rangedDirLights", "transform", "node", "getWorldMatrix", "aabbFrustum", "lhs", "rhs", "Vec3", "squaredDistance", "_addLightQueues", "queue", "BLEND", "SPHERE", "SPOT", "POINT", "RANGED_DIRECTIONAL", "addScene", "SceneFlags", "addSpotlightShadowPasses", "maxNumShadowMaps", "i", "<PERSON><PERSON><PERSON>", "addDepthStencil", "DISCARD", "NONE", "MASK", "SHADOW_CASTER", "useLightFrustum", "addLightQueues", "addLightPasses", "depthStencilStoreOp", "viewport", "count", "storeOp", "setViewport", "LOAD", "isMultipleLightPassesNeeded", "BuiltinForwardPassBuilder", "forwardLighting", "_viewport", "_clearColor", "_reflectionProbeClearColor", "ConfigOrder", "RenderOrder", "configCamera", "pipelineConfigs", "enableMainLightShadowMap", "mainLight", "enableMainLightPlanarShadowMap", "enablePlanarReflectionProbe", "cameraUsage", "SCENE_VIEW", "enableMSAA", "enableSingleForwardPass", "windowResize", "window", "ResourceFlags", "ResourceResidency", "TEX2D", "COLOR_ATTACHMENT", "MEMORYLESS", "DEPTH_STENCIL_ATTACHMENT", "setup", "context", "_addCascadedShadowMapPass", "_tryAddReflectionProbePasses", "_addForwardRadiancePasses", "shadowSize", "csmSupported", "reflectionProbeManager", "cclegacy", "internal", "probes", "getProbes", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "probeID", "probe", "needRender", "area", "renderArea", "probeType", "ProbeType", "PLANAR", "realtimePlanarTexture", "addRenderWindow", "probePass", "_buildReflectionProbePass", "colorStoreOp", "clearColor", "packRGBE", "clear<PERSON><PERSON>h", "clearStencil", "REFLECTION_PROBE", "disableMSAA", "round", "_addForwardSingleRadiancePass", "_addForwardMultipleRadiancePasses", "_addPlanarShadowQueue", "sceneFlags", "<PERSON><PERSON><PERSON><PERSON>", "GEOMETRY", "msaaRadianceName", "msaaDepthStencilName", "msPass", "addMultisampleRenderPass", "_buildForwardMainLightPass", "resolve<PERSON><PERSON><PERSON><PERSON><PERSON>", "firstStoreOp", "warn", "PLANAR_SHADOW", "BuiltinBloomPassBuilder", "_clearColorTransparentBlack", "_bloomParams", "_bloomTexSize", "_bloomWidths", "_bloomHeights", "_bloomTexNames", "enableBloom", "bloomWidth", "bloomHeight", "prevRenderPass", "_addKawaseDualFilterBloomPasses", "radianceName", "sizeCount", "prefilterPass", "downPass", "upPass", "combinePass", "BuiltinToneMappingPassBuilder", "_colorGradingTexSize", "enableColorGrading", "enableToneMapping", "setProperty", "_addCopyAndTonemapPass", "ldrColorPrefix", "ldrColorName", "lutTex", "isSquareMap", "setVec2", "setFloat", "BuiltinFXAAPassBuilder", "_fxaaParams", "enableFXAA", "_addFxaaPass", "inputColorName", "lastPass", "BuiltinFsrPassBuilder", "_fsrParams", "_fsrTexSize", "enableFSR", "outputColorName", "_addFsrPass", "clamp", "uiColorPrefix", "fsrColorName", "easu<PERSON><PERSON>", "rcasPass", "BuiltinUiPassBuilder", "flags", "UI", "PROFILER", "showStatistics", "BuiltinPipelineBuilder", "_pipelineEvent", "director", "root", "pipelineEvent", "_forwardPass", "_bloomPass", "_toneMappingPass", "_fxaaPass", "_fsrPass", "_uiPass", "_configs", "_cameraConfigs", "_copyAndTonemapMaterial", "_initialized", "_passBuilders", "_setupPipelinePreview", "isEditorView", "PREVIEW", "editorSettings", "_prepare<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_passes", "_setupBuiltinCameraConfigs", "GAME", "swapchain", "visibility", "Layers", "Enum", "DEFAULT", "RGBA16F", "_setupCameraConfigs", "builder", "cameras", "_initMaterials", "emit", "PipelineEventType", "RENDER_CAMERA_BEGIN", "_buildFor<PERSON><PERSON><PERSON><PERSON>e", "_buildSimplePipeline", "RENDER_CAMERA_END", "_uuid", "initialize", "effectName", "effectAsset", "setCustomPipeline"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;MAmCA,MAAM;QAAEA,OAAO;QAAEC,gBAAgB;QAAEC,iBAAiB;QAAEC,IAAI;QAAEC,QAAQ;QAAEC,gBAAgB;QAAEC;MAAK,CAAC,GAAGC,UAAU;UAO9FC,uBAAuB,uCAAAC,IAAA,GALnCT,OAAO,CAAC,yBAAyB,CAAC,EAAAU,KAAA,GAClCP,IAAI,CAAC,mCAAmC,CAAC,EAAAQ,KAAA,GACzCN,gBAAgB,CAACO,MAAM,CAAC,EAAAC,KAAA,GAiCpBT,QAAQ,CAACU,SAAS,CAAC,EAAAC,KAAA,GAGnBX,QAAQ,CAAC;QACNY,WAAW,EAAE,+BAA+B;QAC5CV,IAAI,EAAEQ;MACV,CAAC,CAAC,EAAAG,KAAA,GA+BDb,QAAQ,CAAC;QACNc,KAAK,EAAE;UAAEC,EAAE,EAAE,MAAM;UAAEC,IAAI,EAAE;SAA6B;QACxDd,IAAI,EAAEQ;MACV,CAAC,CAAC,EAAAO,KAAA,GAWDjB,QAAQ,CAAC;QACNc,KAAK,EAAE;UAAEC,EAAE,EAAE,MAAM;UAAEC,IAAI,EAAE,2BAA2B;UAAEE,KAAK,EAAE;SAAW;QAC1EhB,IAAI,EAAEiB,SAAS;QACfC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACnB,CAAC,CAAC,EAAAC,KAAA,GAcDrB,QAAQ,CAAC;QACNc,KAAK,EAAE;UAAEC,EAAE,EAAE,cAAc;UAAEC,IAAI,EAAE,cAAc;UAAEE,KAAK,EAAE;SAAW;QACrEhB,IAAI,EAAEQ;MACV,CAAC,CAAC,EAAAY,KAAA,GAWDtB,QAAQ,CAAC;QACNuB,OAAO,EAAE,+BAA+B;QACxCT,KAAK,EAAE;UAAEC,EAAE,EAAE,cAAc;UAAEC,IAAI,EAAE;SAAgB;QACnDd,IAAI,EAAEsB,OAAO;QACbJ,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;QACtBK,KAAK,EAAE;MACX,CAAC,CAAC,EAAAC,MAAA,GAYD1B,QAAQ,CAAC;QACNc,KAAK,EAAE;UAAEC,EAAE,EAAE,OAAO;UAAEC,IAAI,EAAE,wBAAwB;UAAEE,KAAK,EAAE;SAAW;QACxEhB,IAAI,EAAEQ;MACV,CAAC,CAAC,EAAAiB,MAAA,GAWD3B,QAAQ,CAAC;QACNc,KAAK,EAAE;UAAEC,EAAE,EAAE,OAAO;UAAEC,IAAI,EAAE,wBAAwB;UAAEE,KAAK,EAAE;SAAW;QACxEhB,IAAI,EAAE0B;MACV,CAAC,CAAC,EAAAC,MAAA,GAcD7B,QAAQ,CAAC;QACNuB,OAAO,EAAE,4BAA4B;QACrCT,KAAK,EAAE;UAAEC,EAAE,EAAE,OAAO;UAAEC,IAAI,EAAE,wBAAwB;UAAEE,KAAK,EAAE;SAAW;QACxEhB,IAAI,EAAEQ;MACV,CAAC,CAAC,EAAAoB,MAAA,GAWD9B,QAAQ,CAAC;QACNuB,OAAO,EAAE,uBAAuB;QAChCT,KAAK,EAAE;UAAEC,EAAE,EAAE,OAAO;UAAEC,IAAI,EAAE,wBAAwB;UAAEE,KAAK,EAAE;SAAW;QACxEhB,IAAI,EAAEiB,SAAS;QACfC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBK,KAAK,EAAE;MACX,CAAC,CAAC,EAAAM,MAAA,GAWD/B,QAAQ,CAAC;QACNuB,OAAO,EAAE,sBAAsB;QAC/BT,KAAK,EAAE;UAAEC,EAAE,EAAE,OAAO;UAAEC,IAAI,EAAE,wBAAwB;UAAEE,KAAK,EAAE;SAAW;QACxEhB,IAAI,EAAEsB,OAAO;QACbQ,GAAG,EAAE;MACT,CAAC,CAAC,EAAAC,MAAA,GAgBDjC,QAAQ,CAAC;QACNc,KAAK,EAAE;UAAEC,EAAE,EAAE,eAAe;UAAEC,IAAI,EAAE,qCAAqC;UAAEE,KAAK,EAAE;SAAW;QAC7FhB,IAAI,EAAEQ;MACV,CAAC,CAAC,EAAAwB,MAAA,GAWDlC,QAAQ,CAAC;QACNc,KAAK,EAAE;UAAEC,EAAE,EAAE,eAAe;UAAEC,IAAI,EAAE,qCAAqC;UAAEE,KAAK,EAAE;SAAW;QAC7FhB,IAAI,EAAE0B;MACV,CAAC,CAAC,EAAAO,MAAA,GAcDnC,QAAQ,CAAC;QACNuB,OAAO,EAAE,+BAA+B;QACxCT,KAAK,EAAE;UAAEC,EAAE,EAAE,eAAe;UAAEC,IAAI,EAAE,qCAAqC;UAAEE,KAAK,EAAE;SAAW;QAC7FhB,IAAI,EAAEsB,OAAO;QACbJ,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QACnBK,KAAK,EAAE;MACX,CAAC,CAAC,EAAAW,MAAA,GAQDpC,QAAQ,CAAC;QACNuB,OAAO,EAAE,gCAAgC;QACzCT,KAAK,EAAE;UAAEC,EAAE,EAAE,eAAe;UAAEC,IAAI,EAAE,qCAAqC;UAAEE,KAAK,EAAE;SAAW;QAC7FhB,IAAI,EAAEmC;MACV,CAAC,CAAC,EAAAC,MAAA,GAYDtC,QAAQ,CAAC;QACNc,KAAK,EAAE;UAAEC,EAAE,EAAE,MAAM;UAAEC,IAAI,EAAE,iDAAiD;UAAEE,KAAK,EAAE;SAAW;QAChGhB,IAAI,EAAEQ;MACV,CAAC,CAAC,EAAA6B,MAAA,GAWDvC,QAAQ,CAAC;QACNc,KAAK,EAAE;UAAEC,EAAE,EAAE,MAAM;UAAEC,IAAI,EAAE,iDAAiD;UAAEE,KAAK,EAAE;SAAW;QAChGhB,IAAI,EAAE0B;MACV,CAAC,CAAC,EAAAY,MAAA,GAeDxC,QAAQ,CAAC;QACNc,KAAK,EAAE;UAAEC,EAAE,EAAE,KAAK;UAAEC,IAAI,EAAE,6BAA6B;UAAEE,KAAK,EAAE;SAAW;QAC3EhB,IAAI,EAAEQ;MACV,CAAC,CAAC,EAAA+B,MAAA,GAWDzC,QAAQ,CAAC;QACNc,KAAK,EAAE;UAAEC,EAAE,EAAE,KAAK;UAAEC,IAAI,EAAE,6BAA6B;UAAEE,KAAK,EAAE;SAAW;QAC3EhB,IAAI,EAAE0B;MACV,CAAC,CAAC,EAAAc,MAAA,GAcD1C,QAAQ,CAAC;QACNc,KAAK,EAAE;UAAEC,EAAE,EAAE,KAAK;UAAEC,IAAI,EAAE,6BAA6B;UAAEE,KAAK,EAAE;SAAW;QAC3EhB,IAAI,EAAEsB,OAAO;QACbJ,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QACnBK,KAAK,EAAE;MACX,CAAC,CAAC,EAAAkB,MAAA,GAQD3C,QAAQ,CAAC;QACNc,KAAK,EAAE;UAAEC,EAAE,EAAE,aAAa;UAAEC,IAAI,EAAE,aAAa;UAAEE,KAAK,EAAE;SAAW;QACnEhB,IAAI,EAAE0B;MACV,CAAC,CAAC,EAAAvB,IAAA,CAAAuC,MAAA,GAAAtC,KAAA,CAAAsC,MAAA,GAAArC,KAAA,CAAAqC,MAAA,GArWL/C,gBAAgB,CAAA+C,MAAA,GAChB9C,iBAAiB,CAAA8C,MAAA,IAAAC,OAAA,GAJlB,MAKazC,uBAAuB,SAAS0C,SAAS,CAAC;QAAAC,YAAA,GAAAC,IAAA;UAAA,SAAAA,IAAA;UAAAC,0BAAA,oBAAAC,WAAA;;UA6BnDD,0BAAA,yBAAAE,YAAA;;QAzBAC,mBAAmBA,CAAAA,EAAqB;UACpC,OAAO,IAAI,CAACC,SAAS;;;;QAIzBC,QAAQA,CAAAA,EAAS;UACbC,4BAA4B,CAAC,IAAI,CAACF,SAAS,CAAC;UAC5C,MAAMG,eAAe,GAAG,IAAI,CAACC,YAAY,CAACjD,MAAM,CAAE;UAClD,MAAMkD,MAAM,GAAGF,eAAe,CAACE,MAAM;UACrCA,MAAM,CAACC,gBAAgB,GAAG,IAAI,CAACN,SAAS;;QAM5CO,SAASA,CAAAA,EAAS;UACd,MAAMJ,eAAe,GAAG,IAAI,CAACC,YAAY,CAACjD,MAAM,CAAE;UAClD,MAAMkD,MAAM,GAAGF,eAAe,CAACE,MAAM;UACrCA,MAAM,CAACC,gBAAgB,GAAG,IAAI;;QAWlC,IAIIE,aAAaA,CAAAA,EAAY;UACzB,OAAO,IAAI,CAACC,cAAc;;QAE9B,IAAID,aAAaA,CAACE,CAAU,EAAE;UAC1B,IAAI,CAACD,cAAc,GAAGC,CAAC;;QAKpBC,uBAAuBA,CAAAA,EAAS;UACnC,IAAIC,SAAS,KAAKC,SAAS,EAAE;YACzB;;UAEJ,IAAI,IAAI,CAACJ,cAAc,EAAE;YACrBG,SAAS,CAACE,yBAAyB,CAAC,IAAI,CAACd,SAAS,CAAC;WACtD,MAAM;YACH,IAAI,CAACe,qBAAqB,EAAE;;;QAG7BA,qBAAqBA,CAAAA,EAAS;UACjC,IAAIH,SAAS,KAAKC,SAAS,EAAE;YACzB;;UAEJ,MAAMG,OAAO,GAAGJ,SAAS,CAACK,yBAAyB,EAA6B;UAChF,IAAID,OAAO,KAAK,IAAI,CAAChB,SAAS,EAAE;YAC5BY,SAAS,CAACE,yBAAyB,CAAC,IAAI,CAAC;;;;;QAKjD,IAIII,UAAUA,CAAAA,EAAY;UACtB,OAAO,IAAI,CAAClB,SAAS,CAACmB,IAAI,CAACC,OAAO;;QAEtC,IAAIF,UAAUA,CAACG,KAAc,EAAE;UAC3B,IAAI,CAACrB,SAAS,CAACmB,IAAI,CAACC,OAAO,GAAGC,KAAK;;QAMvC,IAKIC,eAAeA,CAACD,KAAa,EAAE;UAC/BA,KAAK,GAAG,CAAC,IAAIE,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,IAAI,CAACF,IAAI,CAACG,GAAG,CAACL,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;UACrDA,KAAK,GAAGE,IAAI,CAAC5C,GAAG,CAAC0C,KAAK,EAAE,CAAC,CAAC;UAC1B,IAAI,CAACrB,SAAS,CAACmB,IAAI,CAACQ,WAAW,GAAGN,KAAK;;QAK3C,IAAIC,eAAeA,CAAAA,EAAW;UAC1B,OAAO,IAAI,CAACtB,SAAS,CAACmB,IAAI,CAACQ,WAAW;;;;QAI1C,IAIIC,kBAAkBA,CAACP,KAAc,EAAE;UACnC,IAAI,CAACrB,SAAS,CAAC6B,kBAAkB,GAAGR,KAAK;;QAK7C,IAAIO,kBAAkBA,CAAAA,EAAY;UAC9B,OAAO,IAAI,CAAC5B,SAAS,CAAC6B,kBAAkB;;QAG5C,IAOIC,YAAYA,CAACT,KAAa,EAAE;UAC5B,IAAI,CAACrB,SAAS,CAAC8B,YAAY,GAAGT,KAAK;;QAKvC,IAAIS,YAAYA,CAAAA,EAAW;UACvB,OAAO,IAAI,CAAC9B,SAAS,CAAC8B,YAAY;;;;QAItC,IAIIC,WAAWA,CAACV,KAAc,EAAE;UAC5B,IAAI,CAACrB,SAAS,CAACgC,KAAK,CAACZ,OAAO,GAAGC,KAAK;;QAKxC,IAAIU,WAAWA,CAAAA,EAAY;UACvB,OAAO,IAAI,CAAC/B,SAAS,CAACgC,KAAK,CAACZ,OAAO;;QAGvC,IAIIa,aAAaA,CAACZ,KAAe,EAAE;UAC/B,IAAI,IAAI,CAACrB,SAAS,CAACgC,KAAK,CAACE,QAAQ,KAAKb,KAAK,EAAE;YACzC;;UAEJ,IAAI,CAACrB,SAAS,CAACgC,KAAK,CAACE,QAAQ,GAAGb,KAAK;;QAKzC,IAAIY,aAAaA,CAAAA,EAAa;UAC1B,OAAO,IAAI,CAACjC,SAAS,CAACgC,KAAK,CAACE,QAAQ;;QAGxC,IAKIC,oBAAoBA,CAACd,KAAc,EAAE;UACrC,IAAI,CAACrB,SAAS,CAACgC,KAAK,CAACI,eAAe,GAAGf,KAAK;;QAKhD,IAAIc,oBAAoBA,CAAAA,EAAY;UAChC,OAAO,IAAI,CAACnC,SAAS,CAACgC,KAAK,CAACI,eAAe;;QAG/C,IAOIC,eAAeA,CAAChB,KAAa,EAAE;UAC/B,IAAI,CAACrB,SAAS,CAACgC,KAAK,CAACM,UAAU,GAAGjB,KAAK;;QAK3C,IAAIgB,eAAeA,CAAAA,EAAW;UAC1B,OAAO,IAAI,CAACrC,SAAS,CAACgC,KAAK,CAACM,UAAU;;QAG1C,IAMIC,cAAcA,CAAClB,KAAa,EAAE;UAC9B,IAAI,CAACrB,SAAS,CAACgC,KAAK,CAACQ,SAAS,GAAGnB,KAAK;;QAE1C,IAAIkB,cAAcA,CAAAA,EAAW;UACzB,OAAO,IAAI,CAACvC,SAAS,CAACgC,KAAK,CAACQ,SAAS;;QAGzC,IAAIC,cAAcA,CAACpB,KAAa,EAAE;UAC9B,IAAI,CAACrB,SAAS,CAACgC,KAAK,CAACU,SAAS,GAAGrB,KAAK;;QAE1C,IAAIoB,cAAcA,CAAAA,EAAW;UACzB,OAAO,IAAI,CAACzC,SAAS,CAACgC,KAAK,CAACU,SAAS;;;;QAIzC,IAIIC,kBAAkBA,CAACtB,KAAc,EAAE;UACnC,IAAI,CAACrB,SAAS,CAAC4C,YAAY,CAACxB,OAAO,GAAGC,KAAK;;QAK/C,IAAIsB,kBAAkBA,CAAAA,EAAY;UAC9B,OAAO,IAAI,CAAC3C,SAAS,CAAC4C,YAAY,CAACxB,OAAO;;QAG9C,IAIIyB,oBAAoBA,CAACxB,KAAe,EAAE;UACtC,IAAI,IAAI,CAACrB,SAAS,CAAC4C,YAAY,CAACV,QAAQ,KAAKb,KAAK,EAAE;YAChD;;UAEJ,IAAI,CAACrB,SAAS,CAAC4C,YAAY,CAACV,QAAQ,GAAGb,KAAK;;QAKhD,IAAIwB,oBAAoBA,CAAAA,EAAa;UACjC,OAAO,IAAI,CAAC7C,SAAS,CAAC4C,YAAY,CAACV,QAAQ;;QAG/C,IAOIY,sBAAsBA,CAACzB,KAAa,EAAE;UACtC,IAAI,CAACrB,SAAS,CAAC4C,YAAY,CAACG,UAAU,GAAG1B,KAAK;;QAElD,IAAIyB,sBAAsBA,CAAAA,EAAW;UACjC,OAAO,IAAI,CAAC9C,SAAS,CAAC4C,YAAY,CAACG,UAAU;;QAGjD,IAKIC,eAAeA,CAACC,GAAc,EAAE;UAChC,IAAI,CAACjD,SAAS,CAAC4C,YAAY,CAACI,eAAe,GAAGC,GAAG;;QAKrD,IAAID,eAAeA,CAAAA,EAAc;UAC7B,OAAO,IAAI,CAAChD,SAAS,CAAC4C,YAAY,CAACI,eAAe;;;;QAItD,IAIIE,UAAUA,CAAC7B,KAAc,EAAE;UAC3B,IAAI,CAACrB,SAAS,CAACmD,IAAI,CAAC/B,OAAO,GAAGC,KAAK;;QAKvC,IAAI6B,UAAUA,CAAAA,EAAY;UACtB,OAAO,IAAI,CAAClD,SAAS,CAACmD,IAAI,CAAC/B,OAAO;;QAGtC,IAIIgC,YAAYA,CAAC/B,KAAe,EAAE;UAC9B,IAAI,IAAI,CAACrB,SAAS,CAACmD,IAAI,CAACjB,QAAQ,KAAKb,KAAK,EAAE;YACxC;;UAEJ,IAAI,CAACrB,SAAS,CAACmD,IAAI,CAACjB,QAAQ,GAAGb,KAAK;;QAKxC,IAAI+B,YAAYA,CAAAA,EAAa;UACzB,OAAO,IAAI,CAACpD,SAAS,CAACmD,IAAI,CAACjB,QAAQ;;;;QAIvC,IAIImB,SAASA,CAAChC,KAAc,EAAE;UAC1B,IAAI,CAACrB,SAAS,CAACsD,GAAG,CAAClC,OAAO,GAAGC,KAAK;;QAKtC,IAAIgC,SAASA,CAAAA,EAAY;UACrB,OAAO,IAAI,CAACrD,SAAS,CAACsD,GAAG,CAAClC,OAAO;;QAGrC,IAIImC,WAAWA,CAAClC,KAAe,EAAE;UAC7B,IAAI,IAAI,CAACrB,SAAS,CAACsD,GAAG,CAACpB,QAAQ,KAAKb,KAAK,EAAE;YACvC;;UAEJ,IAAI,CAACrB,SAAS,CAACsD,GAAG,CAACpB,QAAQ,GAAGb,KAAK;;QAKvC,IAAIkC,WAAWA,CAAAA,EAAa;UACxB,OAAO,IAAI,CAACvD,SAAS,CAACsD,GAAG,CAACpB,QAAQ;;QAGtC,IAMIsB,YAAYA,CAACnC,KAAa,EAAE;UAC5B,IAAI,CAACrB,SAAS,CAACsD,GAAG,CAACG,SAAS,GAAGpC,KAAK;;QAExC,IAAImC,YAAYA,CAAAA,EAAW;UACvB,OAAO,IAAI,CAACxD,SAAS,CAACsD,GAAG,CAACG,SAAS;;QAGvC,IAIIC,mBAAmBA,CAACrC,KAAe,EAAE;UACrC,IAAI,IAAI,CAACrB,SAAS,CAAC2D,WAAW,CAACzB,QAAQ,KAAKb,KAAK,EAAE;YAC/C;;UAEJ,IAAI,CAACrB,SAAS,CAAC2D,WAAW,CAACzB,QAAQ,GAAGb,KAAK;;QAK/C,IAAIqC,mBAAmBA,CAAAA,EAAa;UAChC,OAAO,IAAI,CAAC1D,SAAS,CAAC2D,WAAW,CAACzB,QAAQ;;MAElD,CAAC,GAAArC,WAAA,GAAA+D,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,gBA/WIlH,QAAQ;QAAAmH,YAAA;QAAAC,UAAA;QAAAC,QAAA;QAAAC,WAAA;UAAA,OACsCC,oBAAoB,EAAE;;MAAA,IAAApE,YAAA,GAAA8D,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,qBAAAzG,KAAA;QAAA0G,YAAA;QAAAC,UAAA;QAAAC,QAAA;QAAAC,WAAA;UAAA,OA6B1C,KAAK;;MAAA,IAAAL,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,oBAAAvG,KAAA,GAAA6G,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,oBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,iBAAArG,KAAA,GAAA2G,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,iBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,sBAAAjG,KAAA,GAAAuG,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,sBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,yBAAA7F,KAAA,GAAAmG,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,yBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,mBAAA5F,KAAA,GAAAkG,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,mBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,kBAAAxF,MAAA,GAAA8F,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,kBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,oBAAAvF,MAAA,GAAA6F,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,oBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,2BAAArF,MAAA,GAAA2F,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,2BAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,sBAAApF,MAAA,GAAA0F,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,sBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,qBAAAnF,MAAA,GAAAyF,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,qBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,yBAAAjF,MAAA,GAAAuF,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,yBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,2BAAAhF,MAAA,GAAAsF,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,2BAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,6BAAA/E,MAAA,GAAAqF,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,6BAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,sBAAA9E,MAAA,GAAAoF,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,sBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,iBAAA5E,MAAA,GAAAkF,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,iBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,mBAAA3E,MAAA,GAAAiF,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,mBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,gBAAA1E,MAAA,GAAAgF,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,gBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,kBAAAzE,MAAA,GAAA+E,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,kBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,mBAAAxE,MAAA,GAAA8E,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,mBAAArE,OAAA,CAAAqE,SAAA,GAAAD,yBAAA,CAAApE,OAAA,CAAAqE,SAAA,0BAAAvE,MAAA,GAAA6E,MAAA,CAAAC,wBAAA,CAAA5E,OAAA,CAAAqE,SAAA,0BAAArE,OAAA,CAAAqE,SAAA,IAAArE,OAAA,MAAAD,MAAA,KAAAA,MAAA,KAAAA,MAAA,KAAAA,MAAA,KAAAA,MAAA;cAiVnC,CAAA8E,GAAA,CAAAC,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MC1XD,MAAM;QAAEC;MAAY,CAAC,GAAGC,GAAG;MAQpB,SAASC,QAAQA,CAAAA,EAAS;QAC7B,OAAO;UACHrD,OAAO,EAAE,KAAK;UACdO,WAAW,EAAE4C,WAAW,CAACG;SAC5B;MACL;MAEO,SAASC,gBAAgBA,CAACtD,KAAW,EAAQ;QAChD,IAAIA,KAAK,CAACD,OAAO,KAAKP,SAAS,EAAE;UAC7BQ,KAAK,CAACD,OAAO,GAAG,KAAK;;QAEzB,IAAIC,KAAK,CAACM,WAAW,KAAKd,SAAS,EAAE;UACjCQ,KAAK,CAACM,WAAW,GAAG4C,WAAW,CAACG,EAAE;;MAE1C;MAYO,SAASE,QAAQA,CAAAA,EAAS;QAC7B,OAAO;UACHxD,OAAO,EAAE,KAAK;UACdyD,WAAW,EAAE,CAAC;UACdC,eAAe,EAAE,EAAE;UACnBC,aAAa,EAAE,CAAC;UAChBC,YAAY,EAAE,CAAC;UACfC,QAAQ,EAAE;SACb;MACL;MAEO,SAASC,gBAAgBA,CAAC7D,KAAW,EAAQ;QAChD,IAAIA,KAAK,CAACD,OAAO,KAAKP,SAAS,EAAE;UAC7BQ,KAAK,CAACD,OAAO,GAAG,KAAK;;QAEzB,IAAIC,KAAK,CAACwD,WAAW,KAAKhE,SAAS,EAAE;UACjCQ,KAAK,CAACwD,WAAW,GAAG,CAAC;;QAEzB,IAAIxD,KAAK,CAACyD,eAAe,KAAKjE,SAAS,EAAE;UACrCQ,KAAK,CAACyD,eAAe,GAAG,EAAE;;QAE9B,IAAIzD,KAAK,CAAC0D,aAAa,KAAKlE,SAAS,EAAE;UACnCQ,KAAK,CAAC0D,aAAa,GAAG,CAAC;;QAE3B,IAAI1D,KAAK,CAAC2D,YAAY,KAAKnE,SAAS,EAAE;UAClCQ,KAAK,CAAC2D,YAAY,GAAG,CAAC;;QAE1B,IAAI3D,KAAK,CAAC4D,QAAQ,KAAKpE,SAAS,EAAE;UAC9BQ,KAAK,CAAC4D,QAAQ,GAAG,KAAK;;MAE9B;MAYO,SAASE,SAASA,CAAAA,EAAU;QAC/B,OAAO;UACH/D,OAAO,EAAE,KAAK;UACdc,QAAQ,EAAE,IAAI;UACdE,eAAe,EAAE,KAAK;UACtBE,UAAU,EAAE,CAAC;UACbE,SAAS,EAAE,GAAG;UACdE,SAAS,EAAE;SACd;MACL;MAEO,SAAS0C,iBAAiBA,CAAC/D,KAAY,EAAQ;QAClD,IAAIA,KAAK,CAACD,OAAO,KAAKP,SAAS,EAAE;UAC7BQ,KAAK,CAACD,OAAO,GAAG,KAAK;;QAEzB,IAAIC,KAAK,CAACa,QAAQ,KAAKrB,SAAS,EAAE;UAC9BQ,KAAK,CAACa,QAAQ,GAAG,IAAI;;QAEzB,IAAIb,KAAK,CAACe,eAAe,KAAKvB,SAAS,EAAE;UACrCQ,KAAK,CAACe,eAAe,GAAG,KAAK;;QAEjC,IAAIf,KAAK,CAACiB,UAAU,KAAKzB,SAAS,EAAE;UAChCQ,KAAK,CAACiB,UAAU,GAAG,CAAC;;QAExB,IAAIjB,KAAK,CAACmB,SAAS,KAAK3B,SAAS,EAAE;UAC/BQ,KAAK,CAACmB,SAAS,GAAG,GAAG;;QAEzB,IAAInB,KAAK,CAACqB,SAAS,KAAK7B,SAAS,EAAE;UAC/BQ,KAAK,CAACqB,SAAS,GAAG,GAAG;;MAE7B;MAUO,SAAS2C,gBAAgBA,CAAAA,EAAiB;QAC7C,OAAO;UACHjE,OAAO,EAAE,KAAK;UACdc,QAAQ,EAAE,IAAI;UACda,UAAU,EAAE,CAAC;UACbC,eAAe,EAAE;SACpB;MACL;MAEO,SAASsC,wBAAwBA,CAACjE,KAAmB,EAAQ;QAChE,IAAIA,KAAK,CAACD,OAAO,KAAKP,SAAS,EAAE;UAC7BQ,KAAK,CAACD,OAAO,GAAG,KAAK;;QAEzB,IAAIC,KAAK,CAACa,QAAQ,KAAKrB,SAAS,EAAE;UAC9BQ,KAAK,CAACa,QAAQ,GAAG,IAAI;;QAEzB,IAAIb,KAAK,CAAC0B,UAAU,KAAKlC,SAAS,EAAE;UAChCQ,KAAK,CAAC0B,UAAU,GAAG,CAAC;;QAExB,IAAI1B,KAAK,CAAC2B,eAAe,KAAKnC,SAAS,EAAE;UACrCQ,KAAK,CAAC2B,eAAe,GAAG,IAAI;;MAEpC;MASO,SAASuC,OAAOA,CAAAA,EAAQ;QAC3B,OAAO;UACHnE,OAAO,EAAE,KAAK;UACdc,QAAQ,EAAE,IAAI;UACduB,SAAS,EAAE;SACd;MACL;MAEO,SAAS+B,eAAeA,CAACnE,KAAU,EAAQ;QAC9C,IAAIA,KAAK,CAACD,OAAO,KAAKP,SAAS,EAAE;UAC7BQ,KAAK,CAACD,OAAO,GAAG,KAAK;;QAEzB,IAAIC,KAAK,CAACa,QAAQ,KAAKrB,SAAS,EAAE;UAC9BQ,KAAK,CAACa,QAAQ,GAAG,IAAI;;QAEzB,IAAIb,KAAK,CAACoC,SAAS,KAAK5C,SAAS,EAAE;UAC/BQ,KAAK,CAACoC,SAAS,GAAG,GAAG;;MAE7B;MAQO,SAASgC,QAAQA,CAAAA,EAAS;QAC7B,OAAO;UACHrE,OAAO,EAAE,KAAK;UACdc,QAAQ,EAAE;SACb;MACL;MAEO,SAASwD,gBAAgBA,CAACrE,KAAW,EAAQ;QAChD,IAAIA,KAAK,CAACD,OAAO,KAAKP,SAAS,EAAE;UAC7BQ,KAAK,CAACD,OAAO,GAAG,KAAK;;QAEzB,IAAIC,KAAK,CAACa,QAAQ,KAAKrB,SAAS,EAAE;UAC9BQ,KAAK,CAACa,QAAQ,GAAG,IAAI;;MAE7B;MAOO,SAASyD,eAAeA,CAAAA,EAAgB;QAC3C,OAAO;UACHzD,QAAQ,EAAE;SACb;MACL;MAEO,SAAS0D,uBAAuBA,CAACvE,KAAkB,EAAQ;QAC9D,IAAIA,KAAK,CAACa,QAAQ,KAAKrB,SAAS,EAAE;UAC9BQ,KAAK,CAACa,QAAQ,GAAG,IAAI;;MAE7B;MAcO,SAASgC,oBAAoBA,CAAAA,EAAqB;QACrD,OAAO;UACH/C,IAAI,EAAEsD,QAAQ,EAAE;UAChB5C,kBAAkB,EAAE,KAAK;UACzBC,YAAY,EAAE,GAAG;UACjBE,KAAK,EAAEmD,SAAS,EAAE;UAClBxB,WAAW,EAAEgC,eAAe,EAAE;UAC9B/C,YAAY,EAAEyC,gBAAgB,EAAE;UAChC/B,GAAG,EAAEiC,OAAO,EAAE;UACdpC,IAAI,EAAEsC,QAAQ;SACjB;MACL;MAEO,SAASvF,4BAA4BA,CAACmB,KAAuB,EAAQ;QACxE,IAAI,CAACA,KAAK,CAACF,IAAI,EAAE;UACZE,KAAK,CAACF,IAAI,GAAYsD,QAAQ,EAAE;SACpC,MAAM;UACHE,gBAAgB,CAACtD,KAAK,CAACF,IAAI,CAAC;;QAEhC,IAAIE,KAAK,CAACQ,kBAAkB,KAAKhB,SAAS,EAAE;UACxCQ,KAAK,CAACQ,kBAAkB,GAAG,KAAK;;QAEpC,IAAIR,KAAK,CAACS,YAAY,KAAKjB,SAAS,EAAE;UAClCQ,KAAK,CAACS,YAAY,GAAG,GAAG;;QAE5B,IAAI,CAACT,KAAK,CAACW,KAAK,EAAE;UACbX,KAAK,CAACW,KAAK,GAAamD,SAAS,EAAE;SACvC,MAAM;UACHC,iBAAiB,CAAC/D,KAAK,CAACW,KAAK,CAAC;;QAElC,IAAI,CAACX,KAAK,CAACsC,WAAW,EAAE;UACnBtC,KAAK,CAACsC,WAAW,GAAmBgC,eAAe,EAAE;SACzD,MAAM;UACHC,uBAAuB,CAACvE,KAAK,CAACsC,WAAW,CAAC;;QAE9C,IAAI,CAACtC,KAAK,CAACuB,YAAY,EAAE;UACpBvB,KAAK,CAACuB,YAAY,GAAoByC,gBAAgB,EAAE;SAC5D,MAAM;UACHC,wBAAwB,CAACjE,KAAK,CAACuB,YAAY,CAAC;;QAEhD,IAAI,CAACvB,KAAK,CAACiC,GAAG,EAAE;UACXjC,KAAK,CAACiC,GAAG,GAAWiC,OAAO,EAAE;SACjC,MAAM;UACHC,eAAe,CAACnE,KAAK,CAACiC,GAAG,CAAC;;QAE9B,IAAI,CAACjC,KAAK,CAAC8B,IAAI,EAAE;UACZ9B,KAAK,CAAC8B,IAAI,GAAYsC,QAAQ,EAAE;SACpC,MAAM;UACHC,gBAAgB,CAACrE,KAAK,CAAC8B,IAAI,CAAC;;MAEpC;cAAC,CAAAkB,GAAA,CAAAC,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCvQD,MAAM;QAAEuB,IAAI;QAAEC,MAAM;QAAEC;MAAU,CAAC,GAAGC,QAAQ;MAC5C,MAAM;QAAEC,YAAY;QAAEC,KAAK;QAAEC,MAAM;QAAEC,gBAAgB;QAAEC,MAAM;QAAEC,OAAO;QAAEC,WAAW;QAAEC;MAAS,CAAC,GAAGhC,GAAG;MACrG,MAAM;QAAEiC;MAAM,CAAC,GAAGC,QAAQ;MAC1B,MAAM;QAAEC,WAAW;QAAEC,QAAQ;QAAEC;MAAU,CAAC,GAAGJ,KAAK;MAElD,SAASK,qBAAqBA,CAACzG,MAA6B,EAAW;QACnE,OAAO,CAAC,EAAEA,MAAM,CAAC0G,SAAS,IAAId,YAAY,CAACe,KAAK,GAAIf,YAAY,CAACgB,OAAO,IAAI,CAAE,CAAC,CAAC;MACpF;MAEA,SAASC,uBAAuBA,CAC5BC,KAAsC,EACtCC,CAAS,EACTC,CAAS,EACTC,KAAa,EACbC,EAAgB,EAChBC,gBAAwB,EACpB;QACJ,IAAIL,KAAK,CAACM,eAAe,IAAIN,KAAK,CAACO,QAAQ,KAAKd,QAAQ,CAACe,OAAO,EAAE;UAC9DJ,EAAE,CAACK,IAAI,GAAG,CAAC;UACXL,EAAE,CAACM,GAAG,GAAG,CAAC;UACVN,EAAE,CAACO,KAAK,GAAGvG,IAAI,CAACwG,KAAK,CAACX,CAAC,CAAC;UACxBG,EAAE,CAACS,MAAM,GAAGzG,IAAI,CAACwG,KAAK,CAACV,CAAC,CAAC;SAC5B,MAAM;UACHE,EAAE,CAACK,IAAI,GAAGrG,IAAI,CAACwG,KAAK,CAACT,KAAK,GAAG,CAAC,GAAG,GAAG,GAAGF,CAAC,CAAC;UACzC,IAAII,gBAAgB,GAAG,CAAC,EAAE;YACtBD,EAAE,CAACM,GAAG,GAAGtG,IAAI,CAACwG,KAAK,CAAC,CAAC,CAAC,GAAGxG,IAAI,CAAC0G,KAAK,CAACX,KAAK,GAAG,CAAC,CAAC,IAAI,GAAG,GAAGD,CAAC,CAAC;WAC7D,MAAM;YACHE,EAAE,CAACM,GAAG,GAAGtG,IAAI,CAACwG,KAAK,CAACxG,IAAI,CAAC0G,KAAK,CAACX,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGD,CAAC,CAAC;;UAExDE,EAAE,CAACO,KAAK,GAAGvG,IAAI,CAACwG,KAAK,CAAC,GAAG,GAAGX,CAAC,CAAC;UAC9BG,EAAE,CAACS,MAAM,GAAGzG,IAAI,CAACwG,KAAK,CAAC,GAAG,GAAGV,CAAC,CAAC;;QAEnCE,EAAE,CAACK,IAAI,GAAGrG,IAAI,CAACG,GAAG,CAAC,CAAC,EAAE6F,EAAE,CAACK,IAAI,CAAC;QAC9BL,EAAE,CAACM,GAAG,GAAGtG,IAAI,CAACG,GAAG,CAAC,CAAC,EAAE6F,EAAE,CAACM,GAAG,CAAC;QAC5BN,EAAE,CAACO,KAAK,GAAGvG,IAAI,CAACG,GAAG,CAAC,CAAC,EAAE6F,EAAE,CAACO,KAAK,CAAC;QAChCP,EAAE,CAACS,MAAM,GAAGzG,IAAI,CAACG,GAAG,CAAC,CAAC,EAAE6F,EAAE,CAACS,MAAM,CAAC;MACtC;MAEO,MAAME,eAAe,CAAC;QAAAxI;UAAA,KACzByI,KAAK,GAAG,KAAK;UAAA,KACbC,QAAQ,GAAG,KAAK;UAAA,KAChBC,QAAQ,GAAG,KAAK;UAAA,KAChBC,QAAQ,GAAG,KAAK;UAAA,KAChBC,KAAK,GAAG,KAAK;UAAA,KACbC,cAAc,GAAG,KAAK;UAAA,KACtBC,eAAe,GAAG,CAAC;;UAAE,KACrBC,aAAa,GAAG,KAAK;UAAA,KACrBC,eAAe,GAAGxC,MAAM,CAACyC,IAAI;UAAA,KAC7BC,aAAa,GAAG,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UAAA,KAC9BC,eAAe,GAAG,KAAK;UAAA,KACvBvB,gBAAgB,GAAG,CAAC;UAAA,KACpBwB,kBAAkB,GAAG,KAAK;UAAA,KAC1BC,4BAA4B,GAAG,CAAC;UAAA,KAEhCC,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MACnC;;MAEA,SAASC,oBAAoBA,CACzBC,GAA4B,EAC5BC,OAAwB,EACpB;QACJ,MAAMC,aAAa,GAAGnD,gBAAgB,CAACoD,eAAe,GAAGpD,gBAAgB,CAACqD,aAAa;QACvF,MAAMC,MAAM,GAAGL,GAAG,CAACK,MAAM;;QAEzBJ,OAAO,CAACnB,KAAK,GAAG,CAACwB,GAAG,CAACC,QAAQ;QAC7BN,OAAO,CAAClB,QAAQ,GAAGsB,MAAM,CAACG,MAAM,KAAKrF,GAAG,CAACsF,GAAG,CAACC,KAAK;QAClDT,OAAO,CAACjB,QAAQ,GAAGqB,MAAM,CAACG,MAAM,KAAKrF,GAAG,CAACsF,GAAG,CAACE,MAAM;QACnDV,OAAO,CAAChB,QAAQ,GAAGqB,GAAG,CAACrB,QAAQ;;;QAG/BgB,OAAO,CAACf,KAAK,GAAGc,GAAG,CAACY,iBAAiB,CAAC1B,KAAK,CAAC;QAC5Ce,OAAO,CAACd,cAAc,GAAGa,GAAG,CAACa,YAAY,CAAC,qBAAqB,CAAC;QAChEZ,OAAO,CAACb,eAAe,GAAGY,GAAG,CAACY,iBAAiB,CAACE,YAAY,CAAC1B,eAAe;;QAE5E,MAAM2B,UAAU,GAAGf,GAAG,CAACY,iBAAiB,CAACI,OAAO;QAChDf,OAAO,CAACZ,aAAa,GAAG0B,UAAU,CAAChJ,OAAO;QAC1CkI,OAAO,CAACX,eAAe,GAAG2B,QAAQ,CAACC,uBAAuB,CAAClB,GAAG,CAACK,MAAM,CAAC,GAAGvD,MAAM,CAACyC,IAAI,GAAGzC,MAAM,CAACqE,KAAK;QACnGlB,OAAO,CAACT,aAAa,CAAC4B,GAAG,CAACL,UAAU,CAACM,IAAI,CAAC;QAC1CpB,OAAO,CAACP,eAAe,GAAGqB,UAAU,CAAChJ,OAAO,IAAIgJ,UAAU,CAACvN,IAAI,KAAK6J,QAAQ,CAACD,KAAK,CAACkE,UAAU,CAACC,MAAM;;QAEpGtB,OAAO,CAAC9B,gBAAgB,GAAG6B,GAAG,CAACK,MAAM,CAACmB,YAAY,CAACrD,gBAAgB;QACnE8B,OAAO,CAACN,kBAAkB,GAAG,CAACK,GAAG,CAACK,MAAM,CAACoB,iBAAiB,CAAC3E,MAAM,CAAC4E,aAAa,CAAC,GAAGxB,aAAa,MAAMA,aAAa;;QAEnH,MAAM/B,gBAAgB,GAAGkC,MAAM,CAACmB,YAAY,CAACrD,gBAAgB;QAC7D8B,OAAO,CAACJ,QAAQ,CAAC8B,CAAC,GAAG1B,OAAO,CAAChB,QAAQ,GAAG,GAAG,GAAG,GAAG;QACjDgB,OAAO,CAACJ,QAAQ,CAAC9B,CAAC,GAAII,gBAAgB,GAAG,GAAG,GAAG,GAAG,IAAK,CAAC,GAAIkC,MAAM,CAACmB,YAAY,CAACI,cAAc,GAAG,GAAG,GAAG,GAAI;MAC/G;MAMA,MAAMC,eAAe,GAAGhH,oBAAoB,EAAE;MAEvC,MAAMiH,aAAa,CAAC;QAAAzL;UAAA,KACvB0L,QAAQ,GAAqBF,eAAe;;UAC5C,KACAG,gBAAgB,GAAG,KAAK;UAAA,KACxBC,cAAc,GAAG,CAAC;;UAClB,KACAC,SAAS,GAAG,EAAE;UAAA,KACdC,gBAAgB,GAAG,EAAE;;UACrB,KACAC,kBAAkB,GAAG,KAAK;UAAA,KAC1BC,cAAc,GAAG,KAAK;UAAA,KACtBC,eAAe,GAAG,CAAC;;UACnB,KACA9J,kBAAkB,GAAG,KAAK;UAAA,KAC1BC,YAAY,GAAG,GAAG;UAAA,KAClB8J,WAAW,GAAG,CAAC;UAAA,KACfC,YAAY,GAAG,CAAC;UAAA,KAChB/D,KAAK,GAAG,CAAC;;UAAE,KACXE,MAAM,GAAG,CAAC;;;UACV,KACA8D,SAAS,GAAG,KAAK;UAAA,KACjBC,cAAc,GAAGvH,GAAG,CAAC2B,MAAM,CAACqE,KAAK;;UACjC,KACAwB,sBAAsB,GAAoB,IAAI;;;UAE9C,KACAC,qBAAqB,GAAG,KAAK;;MACjC;;MAEA,MAAMC,2BAA2B,GAAG,IAAIhG,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAEzD,SAASiG,qCAAqCA,CAACC,YAA6C,EAAQ;QAChGA,YAAY,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACxB,OAAOD,CAAC,CAACE,cAAc,EAAE,GAAGD,CAAC,CAACC,cAAc,EAAE;SACjD,CAAC;MACN;MAEA,SAASC,qCAAqCA,CAACL,YAA6C,EAAQ;QAChGA,YAAY,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACxB,OAAOD,CAAC,CAACI,cAAc,EAAE,GAAGH,CAAC,CAACG,cAAc,EAAE;SACjD,CAAC;MACN;MAEA,SAASC,mBAAmBA,CACxBtD,GAA4B,EAC5BuD,UAAqC,EACrCC,aAA4B,EAC5BC,KAAa,EACmB;QAChCC,MAAM,CAAC,CAAC,CAACF,aAAa,CAACb,sBAAsB,CAAC;QAC9C,MAAMgB,IAAI,GAAG3D,GAAG,CAAC4D,aAAa,CAC1BJ,aAAa,CAACjB,WAAW,EACzBiB,aAAa,CAAChB,YAAY,EAC1B,iBAAiB,CAAC;QACtBmB,IAAI,CAACE,eAAe,CAChBL,aAAa,CAACtB,SAAS,EACvBlF,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAAC8G,KAAK,EAC3BlB,2BAA2B,CAAC;QAChCc,IAAI,CAACK,UAAU,CAACP,KAAK,EAAE,cAAc,CAAC;QACtCE,IAAI,CAACM,OAAO,CAAC,YAAY,EAAEV,UAAU,CAAC1D,QAAQ,CAAC;QAC/C8D,IAAI,CAACO,QAAQ,CAAC3M,SAAS,CAAC4M,SAAS,CAACC,MAAM,CAAC,CACpCC,iBAAiB,CAACb,aAAa,CAACb,sBAAsB,EAAE,CAAC,CAAC;QAC/D,OAAOgB,IAAI;MACf;MAEO,SAASW,uBAAuBA,CAACC,QAAgB,EAAEC,MAAc,EAAEnQ,EAAU,EAAU;QAC1F,IAAIkQ,QAAQ,CAACE,UAAU,CAACD,MAAM,CAAC,EAAE;UAC7B,UAAUA,SAAS,CAAC,GAAGE,MAAM,CAACH,QAAQ,CAACI,MAAM,CAACH,MAAM,CAACI,MAAM,CAAC,KAAKvQ,IAAI;SACxE,MAAM;UACH,UAAUmQ,WAAWnQ,IAAI;;MAEjC;MAOA,MAAMwQ,eAAe,CAAC;QAAAxO;;UAClB,KACiByO,MAAM,GAA2B,EAAE;;UACpD,KACiBC,uBAAuB,GAA+B,EAAE;;UAEzE,KACiBC,OAAO,GAAGvI,MAAM,CAACwI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAAA,KACnCC,YAAY,GAAG,IAAI1I,IAAI,EAAE;UAAA,KACzB2I,0BAA0B,GAAG,IAAI3I,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;;;;;QAK7E4I,UAAUA,CAAChI,KAA2B,EAAEiI,OAAyB,EAAEC,SAAgB,EAAQ;;UAE9F,IAAI,CAACR,MAAM,CAACF,MAAM,GAAG,CAAC;UACtB,IAAI,CAACG,uBAAuB,CAACH,MAAM,GAAG,CAAC;;UAEvC,KAAK,MAAM9G,KAAK,IAAIV,KAAK,CAACmI,UAAU,EAAE;YAClC,IAAIzH,KAAK,CAAC0H,KAAK,EAAE;cACb;;YAEJ/I,MAAM,CAAC2E,GAAG,CAAC,IAAI,CAAC4D,OAAO,EAAElH,KAAK,CAAC2H,QAAQ,CAAC9D,CAAC,EAAE7D,KAAK,CAAC2H,QAAQ,CAACC,CAAC,EAAE5H,KAAK,CAAC2H,QAAQ,CAACE,CAAC,EAAE7H,KAAK,CAACpJ,KAAK,CAAC;YAC3F,IAAIgI,SAAS,CAACkJ,aAAa,CAAC,IAAI,CAACZ,OAAO,EAAEK,OAAO,CAAC,EAAE;cAChD,IAAIvH,KAAK,CAACuB,aAAa,EAAE;gBACrB,IAAI,CAAC0F,uBAAuB,CAACc,IAAI,CAAC/H,KAAK,CAAC;eAC3C,MAAM;gBACH,IAAI,CAACgH,MAAM,CAACe,IAAI,CAAC/H,KAAK,CAAC;;;;;UAKnC,KAAK,MAAMA,KAAK,IAAIV,KAAK,CAAC0I,YAAY,EAAE;YACpC,IAAIhI,KAAK,CAAC0H,KAAK,EAAE;cACb;;YAEJ/I,MAAM,CAAC2E,GAAG,CAAC,IAAI,CAAC4D,OAAO,EAAElH,KAAK,CAAC2H,QAAQ,CAAC9D,CAAC,EAAE7D,KAAK,CAAC2H,QAAQ,CAACC,CAAC,EAAE5H,KAAK,CAAC2H,QAAQ,CAACE,CAAC,EAAE7H,KAAK,CAACpJ,KAAK,CAAC;YAC3F,IAAIgI,SAAS,CAACkJ,aAAa,CAAC,IAAI,CAACZ,OAAO,EAAEK,OAAO,CAAC,EAAE;cAChD,IAAI,CAACP,MAAM,CAACe,IAAI,CAAC/H,KAAK,CAAC;;;;UAI/B,KAAK,MAAMA,KAAK,IAAIV,KAAK,CAAC2I,WAAW,EAAE;YACnC,IAAIjI,KAAK,CAAC0H,KAAK,EAAE;cACb;;YAEJ/I,MAAM,CAAC2E,GAAG,CAAC,IAAI,CAAC4D,OAAO,EAAElH,KAAK,CAAC2H,QAAQ,CAAC9D,CAAC,EAAE7D,KAAK,CAAC2H,QAAQ,CAACC,CAAC,EAAE5H,KAAK,CAAC2H,QAAQ,CAACE,CAAC,EAAE7H,KAAK,CAACpJ,KAAK,CAAC;YAC3F,IAAIgI,SAAS,CAACkJ,aAAa,CAAC,IAAI,CAACZ,OAAO,EAAEK,OAAO,CAAC,EAAE;cAChD,IAAI,CAACP,MAAM,CAACe,IAAI,CAAC/H,KAAK,CAAC;;;;UAI/B,KAAK,MAAMA,KAAK,IAAIV,KAAK,CAAC4I,eAAe,EAAE;YACvCxJ,IAAI,CAACyJ,SAAS,CAAC,IAAI,CAACf,YAAY,EAAE,IAAI,CAACC,0BAA0B,EAAErH,KAAK,CAACoI,IAAI,CAAEC,cAAc,EAAE,CAAC;YAChG,IAAIzJ,SAAS,CAAC0J,WAAW,CAAC,IAAI,CAAClB,YAAY,EAAEG,OAAO,CAAC,EAAE;cACnD,IAAI,CAACP,MAAM,CAACe,IAAI,CAAC/H,KAAK,CAAC;;;UAI/B,IAAIwH,SAAS,EAAE;YACX,IAAI,CAACP,uBAAuB,CAAC/B,IAAI,CAC7B,CAACqD,GAAG,EAAEC,GAAG,KAAKC,IAAI,CAACC,eAAe,CAAClB,SAAS,EAAEe,GAAG,CAACZ,QAAQ,CAAC,GAAGc,IAAI,CAACC,eAAe,CAAClB,SAAS,EAAEgB,GAAG,CAACb,QAAQ,CAC9G,CAAC;;;QAGDgB,eAAeA,CAACzP,MAA6B,EAAE2M,IAAsC,EAAQ;UACjG,KAAK,MAAM7F,KAAK,IAAI,IAAI,CAACgH,MAAM,EAAE;YAC7B,MAAM4B,KAAK,GAAG/C,IAAI,CAACO,QAAQ,CAAC3M,SAAS,CAAC4M,SAAS,CAACwC,KAAK,EAAE,aAAa,CAAC;YACrE,QAAQ7I,KAAK,CAACtK,IAAI;cACd,KAAKgK,SAAS,CAACoJ,MAAM;gBACjBF,KAAK,CAACpS,IAAI,GAAG,cAAc;gBAC3B;cACJ,KAAKkJ,SAAS,CAACqJ,IAAI;gBACfH,KAAK,CAACpS,IAAI,GAAG,YAAY;gBACzB;cACJ,KAAKkJ,SAAS,CAACsJ,KAAK;gBAChBJ,KAAK,CAACpS,IAAI,GAAG,aAAa;gBAC1B;cACJ,KAAKkJ,SAAS,CAACuJ,kBAAkB;gBAC7BL,KAAK,CAACpS,IAAI,GAAG,0BAA0B;gBACvC;cACJ;gBACIoS,KAAK,CAACpS,IAAI,GAAG,eAAe;;YAEpCoS,KAAK,CAACM,QAAQ,CACVhQ,MAAM,EACNO,SAAS,CAAC0P,UAAU,CAACN,KAAK,EAC1B7I,KACJ,CAAC;;;QAGFoJ,wBAAwBA,CAC3BlH,GAA4B,EAC5BhJ,MAA6B,EAC7BmQ,gBAAwB,EACpB;UACJ,IAAIC,CAAC,GAAG,CAAC;UACT,KAAK,MAAMtJ,KAAK,IAAI,IAAI,CAACiH,uBAAuB,EAAE;YAC9C,MAAMvF,aAAa,GAAGQ,GAAG,CAACY,iBAAiB,CAACI,OAAO,CAACK,IAAI;YACxD,MAAMgG,UAAU,GAAGrH,GAAG,CAAC4D,aAAa,CAACpE,aAAa,CAACmC,CAAC,EAAEnC,aAAa,CAACkG,CAAC,EAAE,SAAS,CAAC;YACjF2B,UAAU,CAAC/S,IAAI,yBAAyB8S,GAAG;YAC3CC,UAAU,CAACxD,eAAe,iBAAiBuD,GAAG,EAAEpK,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAAC8G,KAAK,EAAE,IAAIlH,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACnGwK,UAAU,CAACC,eAAe,mBAAmBF,GAAG,EAAEpK,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAACsK,OAAO,CAAC;YAChFF,UAAU,CAACnD,QAAQ,CAAC3M,SAAS,CAAC4M,SAAS,CAACqD,IAAI,EAAE,eAAe,CAAC,CACzDR,QAAQ,CAAChQ,MAAM,EAAEO,SAAS,CAAC0P,UAAU,CAAC7C,MAAM,GAAG7M,SAAS,CAAC0P,UAAU,CAACQ,IAAI,GAAGlQ,SAAS,CAAC0P,UAAU,CAACS,aAAa,CAAC,CAC9GC,eAAe,CAAC7J,KAAK,CAAC;YAC3B,EAAEsJ,CAAC;YACH,IAAIA,CAAC,IAAID,gBAAgB,EAAE;cACvB;;;;QAILS,cAAcA,CAACjE,IAAsC,EACxD3M,MAA6B,EAAEmQ,gBAAwB,EAAQ;UAC/D,IAAI,CAACV,eAAe,CAACzP,MAAM,EAAE2M,IAAI,CAAC;UAClC,IAAIyD,CAAC,GAAG,CAAC;UACT,KAAK,MAAMtJ,KAAK,IAAI,IAAI,CAACiH,uBAAuB,EAAE;;;;YAI9CpB,IAAI,CAACK,UAAU,iBAAiBoD,GAAG,EAAE,kBAAkB,CAAC;YACxD,MAAMV,KAAK,GAAG/C,IAAI,CAACO,QAAQ,CAAC3M,SAAS,CAAC4M,SAAS,CAACwC,KAAK,EAAE,aAAa,CAAC;YACrED,KAAK,CAACM,QAAQ,CAAChQ,MAAM,EAAEO,SAAS,CAAC0P,UAAU,CAACN,KAAK,EAAE7I,KAAK,CAAC;YACzD,EAAEsJ,CAAC;YACH,IAAIA,CAAC,IAAID,gBAAgB,EAAE;cACvB;;;;;;;;QAQLU,cAAcA,CACjB3F,SAAiB,EACjBC,gBAAwB,EACxB2F,mBAAgC,EAChCzT,EAAU;;QACVoK,KAAa,EACbE,MAAc,EACd3H,MAA6B,EAC7B+Q,QAAsB,EACtB/H,GAA4B,EAC5B2D,IAAsC,EACN;UAChC,IAAI,CAAC8C,eAAe,CAACzP,MAAM,EAAE2M,IAAI,CAAC;UAElC,IAAIqE,KAAK,GAAG,CAAC;UACb,MAAMxI,aAAa,GAAGQ,GAAG,CAACY,iBAAiB,CAACI,OAAO,CAACK,IAAI;UACxD,KAAK,MAAMvD,KAAK,IAAI,IAAI,CAACiH,uBAAuB,EAAE;YAC9C,MAAMsC,UAAU,GAAGrH,GAAG,CAAC4D,aAAa,CAACpE,aAAa,CAACmC,CAAC,EAAEnC,aAAa,CAACkG,CAAC,EAAE,SAAS,CAAC;YACjF2B,UAAU,CAAC/S,IAAI,GAAG,qBAAqB;;YAEvC+S,UAAU,CAACxD,eAAe,aAAaxP,IAAI,EAAE2I,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAAC8G,KAAK,EAAE,IAAIlH,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAChGwK,UAAU,CAACC,eAAe,eAAejT,IAAI,EAAE2I,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAACsK,OAAO,CAAC;YAC7EF,UAAU,CAACnD,QAAQ,CAAC3M,SAAS,CAAC4M,SAAS,CAACqD,IAAI,EAAE,eAAe,CAAC,CACzDR,QAAQ,CAAChQ,MAAM,EAAEO,SAAS,CAAC0P,UAAU,CAAC7C,MAAM,GAAG7M,SAAS,CAAC0P,UAAU,CAACQ,IAAI,GAAGlQ,SAAS,CAAC0P,UAAU,CAACS,aAAa,CAAC,CAC9GC,eAAe,CAAC7J,KAAK,CAAC;;;;YAI3B,EAAEkK,KAAK;YACP,MAAMC,OAAO,GAAGD,KAAK,KAAK,IAAI,CAACjD,uBAAuB,CAACH,MAAM,GACvDkD,mBAAmB,GACnB7K,OAAO,CAAC8G,KAAK;YAEnBJ,IAAI,GAAG3D,GAAG,CAAC4D,aAAa,CAACnF,KAAK,EAAEE,MAAM,EAAE,SAAS,CAAC;YAClDgF,IAAI,CAACrP,IAAI,GAAG,wBAAwB;YACpCqP,IAAI,CAACuE,WAAW,CAACH,QAAQ,CAAC;YAC1BpE,IAAI,CAACE,eAAe,CAAC3B,SAAS,EAAElF,MAAM,CAACmL,IAAI,CAAC;YAC5CxE,IAAI,CAAC2D,eAAe,CAACnF,gBAAgB,EAAEnF,MAAM,CAACmL,IAAI,EAAEF,OAAO,CAAC;YAC5DtE,IAAI,CAACK,UAAU,aAAa3P,IAAI,EAAE,kBAAkB,CAAC;YACrD,MAAMqS,KAAK,GAAG/C,IAAI,CAACO,QAAQ,CAAC3M,SAAS,CAAC4M,SAAS,CAACwC,KAAK,EAAE,aAAa,CAAC;YACrED,KAAK,CAACM,QAAQ,CACVhQ,MAAM,EACNO,SAAS,CAAC0P,UAAU,CAACN,KAAK,EAC1B7I,KACJ,CAAC;;UAEL,OAAO6F,IAAI;;QAGRyE,2BAA2BA,CAAAA,EAAY;UAC1C,OAAO,IAAI,CAACrD,uBAAuB,CAACH,MAAM,GAAG,CAAC;;MAEtD;MAUO,MAAMyD,yBAAyB,CAA0C;QAAAhS;UAAA,KAyjB3DiS,eAAe,GAAG,IAAIzD,eAAe,EAAE;UAAA,KACvC0D,SAAS,GAAG,IAAIpL,QAAQ,EAAE;UAAA,KAC1BqL,WAAW,GAAG,IAAI3L,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAAA,KACnC4L,0BAA0B,GAAG,IAAIlC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAzjB/DpD,cAAcA,CAAAA,EAAW;UACrB,OAAOkF,yBAAyB,CAACK,WAAW;;QAEhDrF,cAAcA,CAAAA,EAAW;UACrB,OAAOgF,yBAAyB,CAACM,WAAW;;QAEhDC,YAAYA,CACR5R,MAAuC,EACvC6R,eAA0C,EAC1CrF,aAAiD,EAAQ;;UAEzDA,aAAa,CAACsF,wBAAwB,GAAGD,eAAe,CAACxJ,aAAa,IAC/D,CAACwJ,eAAe,CAACnJ,eAAe,IAChC,CAAC,CAAC1I,MAAM,CAACoG,KAAK,IACd,CAAC,CAACpG,MAAM,CAACoG,KAAK,CAAC2L,SAAS,IACxB/R,MAAM,CAACoG,KAAK,CAAC2L,SAAS,CAAC1J,aAAa;UAE3CmE,aAAa,CAACwF,8BAA8B,GAAGH,eAAe,CAACxJ,aAAa,IACrEwJ,eAAe,CAACnJ,eAAe,IAC/B,CAAC,CAAC1I,MAAM,CAACoG,KAAK,IACd,CAAC,CAACpG,MAAM,CAACoG,KAAK,CAAC2L,SAAS,IACxB/R,MAAM,CAACoG,KAAK,CAAC2L,SAAS,CAAC1J,aAAa;;;UAG3CmE,aAAa,CAACyF,2BAA2B,GACrCzF,aAAa,CAACxB,gBAAgB,IAAIhL,MAAM,CAACkS,WAAW,KAAK5L,WAAW,CAAC6L,UAAU;;;UAGnF3F,aAAa,CAAC4F,UAAU,GAAG5F,aAAa,CAACzB,QAAQ,CAACjK,IAAI,CAACC,OAAO,IACvD,CAACyL,aAAa,CAACZ,qBAAqB;aACpC,CAACiG,eAAe,CAAC/J,KAAK;aACtB,CAAC+J,eAAe,CAAC9J,QAAQ;;;UAGhCyE,aAAa,CAAC6F,uBAAuB,GAC/BR,eAAe,CAAC5J,QAAQ,IAAIuE,aAAa,CAAC4F,UAAU;UAE1D,EAAE5F,aAAa,CAAClB,eAAe;;QAEnCgH,YAAYA,CACRtJ,GAA4B,EAC5BuD,UAAqC,EACrCC,aAA2D,EAC3D+F,MAA6B,EAC7BvS,MAA6B,EAC7BuL,WAAmB,EACnBC,YAAoB,EAAQ;UAC5B,MAAMgH,aAAa,GAAGjS,SAAS,CAACiS,aAAa;UAC7C,MAAMC,iBAAiB,GAAGlS,SAAS,CAACkS,iBAAiB;UACrD,MAAMpV,EAAE,GAAGkV,MAAM,CAACtH,cAAc;UAChC,MAAMF,QAAQ,GAAGyB,aAAa,CAACzB,QAAQ;UAEvC,MAAMtD,KAAK,GAAG+E,aAAa,CAAChL,kBAAkB,GACxCN,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAAC2D,WAAW,GAAGiB,aAAa,CAAC/K,YAAY,CAAC,EAAE,CAAC,CAAC,GACjE8J,WAAW;UACjB,MAAM5D,MAAM,GAAG6E,aAAa,CAAChL,kBAAkB,GACzCN,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAAC4D,YAAY,GAAGgB,aAAa,CAAC/K,YAAY,CAAC,EAAE,CAAC,CAAC,GAClE+J,YAAY;;;UAGlB,IAAIgB,aAAa,CAAC4F,UAAU,EAAE;;;;YAI1B,IAAI5F,aAAa,CAACf,SAAS,EAAE;cACzBzC,GAAG,CAACgE,UAAU,gBAAgB3P,IAAI,EAAE6I,WAAW,CAACwM,KAAK,EAAElG,aAAa,CAACd,cAAc,EAAEjE,KAAK,EAAEE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EACvGoD,QAAQ,CAACjK,IAAI,CAACQ,WAAW,EAAEkR,aAAa,CAACG,gBAAgB,EAAEF,iBAAiB,CAACG,UAAU,CAAC;aAC/F,MAAM;cACH5J,GAAG,CAACgE,UAAU,gBAAgB3P,IAAI,EAAE6I,WAAW,CAACwM,KAAK,EAAE5M,MAAM,CAACqE,KAAK,EAAE1C,KAAK,EAAEE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EACvFoD,QAAQ,CAACjK,IAAI,CAACQ,WAAW,EAAEkR,aAAa,CAACG,gBAAgB,EAAEF,iBAAiB,CAACG,UAAU,CAAC;;YAEhG5J,GAAG,CAACgE,UAAU,oBAAoB3P,IAAI,EAAE6I,WAAW,CAACwM,KAAK,EAAE5M,MAAM,CAAC4E,aAAa,EAAEjD,KAAK,EAAEE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EACnGoD,QAAQ,CAACjK,IAAI,CAACQ,WAAW,EAAEkR,aAAa,CAACK,wBAAwB,EAAEJ,iBAAiB,CAACG,UAAU,CAAC;;;;UAIxG5J,GAAG,CAAC6D,eAAe,aACHxP,IAAI,EAChBkP,UAAU,CAACjE,eAAe,EAC1BiE,UAAU,CAAC/D,aAAa,CAACmC,CAAC,EAC1B4B,UAAU,CAAC/D,aAAa,CAACkG,CAC7B,CAAC;UACD1F,GAAG,CAACsH,eAAe,eACDjT,IAAI,EAClByI,MAAM,CAAC4E,aAAa,EACpB6B,UAAU,CAAC/D,aAAa,CAACmC,CAAC,EAC1B4B,UAAU,CAAC/D,aAAa,CAACkG,CAC7B,CAAC;;;UAGD,IAAIlC,aAAa,CAAC6F,uBAAuB,EAAE;YACvC,MAAMrB,KAAK,GAAGzE,UAAU,CAAC3D,4BAA4B;YACrD,KAAK,IAAIwH,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAKY,KAAK,EAAE,EAAEZ,CAAC,EAAE;cAC9BpH,GAAG,CAAC6D,eAAe,iBACCuD,GAAG,EACnB7D,UAAU,CAACjE,eAAe,EAC1BiE,UAAU,CAAC/D,aAAa,CAACmC,CAAC,EAC1B4B,UAAU,CAAC/D,aAAa,CAACkG,CAC7B,CAAC;cACD1F,GAAG,CAACsH,eAAe,mBACGF,GAAG,EACrBtK,MAAM,CAAC4E,aAAa,EACpB6B,UAAU,CAAC/D,aAAa,CAACmC,CAAC,EAC1B4B,UAAU,CAAC/D,aAAa,CAACkG,CAC7B,CAAC;;;;QAIboE,KAAKA,CACD9J,GAA4B,EAC5BuD,UAAqC,EACrCC,aAAiD,EACjDxM,MAA6B,EAC7B+S,OAAwB,EAAgD;UACxE,MAAM1V,EAAE,GAAG2C,MAAM,CAACuS,MAAM,CAACtH,cAAc;UAEvC,MAAM7E,KAAK,GAAGpG,MAAM,CAACoG,KAAM;UAC3B,MAAM2L,SAAS,GAAG3L,KAAK,CAAC2L,SAAS;UAEjC,EAAEvF,aAAa,CAAClB,eAAe;UAC/BoB,MAAM,CAACF,aAAa,CAAClB,eAAe,IAAI,CAAC,CAAC;;;UAG1C,IAAI,CAACgG,eAAe,CAAClD,UAAU,CAAChI,KAAK,EAAEpG,MAAM,CAACqO,OAAO,CAAC;;;UAGtD,IAAI7B,aAAa,CAACsF,wBAAwB,EAAE;YACxCpF,MAAM,CAAC,CAAC,CAACqF,SAAS,CAAC;YACnB,IAAI,CAACiB,yBAAyB,CAAChK,GAAG,EAAEuD,UAAU,EAAElP,EAAE,EAAE0U,SAAS,EAAE/R,MAAM,CAAC;;;;UAI1E,IAAIwM,aAAa,CAAC6F,uBAAuB,EAAE;;;YAGvC,IAAI,CAACf,eAAe,CAACpB,wBAAwB,CACzClH,GAAG,EAAEhJ,MAAM,EAAEuM,UAAU,CAAC3D,4BAA4B,CAAC;;UAG7D,IAAI,CAACqK,4BAA4B,CAACjK,GAAG,EAAEwD,aAAa,EAAEnP,EAAE,EAAE0U,SAAS,EAAE/R,MAAM,CAACoG,KAAK,CAAC;UAElF,IAAIoG,aAAa,CAAClB,eAAe,GAAG,CAAC,IAAIkB,aAAa,CAAChL,kBAAkB,EAAE;YACvEuR,OAAO,CAAC7H,SAAS,GAAGsB,aAAa,CAAChL,kBAAkB,sBAC3BnE,IAAI,gBACVA,IAAI;YACvB0V,OAAO,CAAC5H,gBAAgB,GAAGqB,aAAa,CAAChL,kBAAkB,uBACjCnE,IAAI,iBACVA,IAAI;WAC3B,MAAM;YACH0V,OAAO,CAAC7H,SAAS,GAAGsB,aAAa,CAACtB,SAAS;YAC3C6H,OAAO,CAAC5H,gBAAgB,GAAGqB,aAAa,CAACrB,gBAAgB;;UAG7D,MAAMwB,IAAI,GAAG,IAAI,CAACuG,yBAAyB,CACvClK,GAAG,EAAEuD,UAAU,EAAEC,aAAa,EAAEnP,EAAE,EAAE2C,MAAM,EAC1CwM,aAAa,CAAC/E,KAAK,EAAE+E,aAAa,CAAC7E,MAAM,EAAEoK,SAAS,EACpDgB,OAAO,CAAC7H,SAAS,EAAE6H,OAAO,CAAC5H,gBAAgB,EAC3C,CAACqB,aAAa,CAAC4F,UAAU,EACzB5F,aAAa,CAACZ,qBAAqB,GAAG3F,OAAO,CAAC8G,KAAK,GAAG9G,OAAO,CAACsK,OAAO,CAAC;UAE1E,IAAI,CAAC/D,aAAa,CAACZ,qBAAqB,EAAE;YACtCmH,OAAO,CAAC5H,gBAAgB,GAAG,EAAE;;UAGjC,IAAIqB,aAAa,CAAClB,eAAe,KAAK,CAAC,IAAIkB,aAAa,CAAChL,kBAAkB,EAAE;YACzE,OAAO8K,mBAAmB,CAACtD,GAAG,EAAEuD,UAAU,EAAEC,aAAa,EAAEuG,OAAO,CAAC7H,SAAS,CAAC;WAChF,MAAM;YACH,OAAOyB,IAAI;;;QAGXqG,yBAAyBA,CAC7BhK,GAA4B,EAC5BuD,UAAqC,EACrClP,EAAU,EACVyJ,KAAsC,EACtC9G,MAA6B,EACzB;UACJ,MAAMmN,SAAS,GAAG5M,SAAS,CAAC4M,SAAS;UACrC,MAAM8C,UAAU,GAAG1P,SAAS,CAAC0P,UAAU;;;;UAIvC,MAAMkD,UAAU,GAAGnK,GAAG,CAACY,iBAAiB,CAACI,OAAO,CAACK,IAAI;UACrD,MAAM5C,KAAK,GAAG0L,UAAU,CAACxI,CAAC;UAC1B,MAAMhD,MAAM,GAAGwL,UAAU,CAACzE,CAAC;UAE3B,MAAMqC,QAAQ,GAAG,IAAI,CAACQ,SAAS;UAC/BR,QAAQ,CAACxJ,IAAI,GAAGwJ,QAAQ,CAACvJ,GAAG,GAAG,CAAC;UAChCuJ,QAAQ,CAACtJ,KAAK,GAAGA,KAAK;UACtBsJ,QAAQ,CAACpJ,MAAM,GAAGA,MAAM;;;;;UAKxB,MAAMgF,IAAI,GAAG3D,GAAG,CAAC4D,aAAa,CAACnF,KAAK,EAAEE,MAAM,EAAE,SAAS,CAAC;UACxDgF,IAAI,CAACrP,IAAI,GAAG,mBAAmB;UAC/BqP,IAAI,CAACE,eAAe,aAAaxP,IAAI,EAAE2I,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAAC8G,KAAK,EAAE,IAAIlH,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UAC1F8G,IAAI,CAAC2D,eAAe,eAAejT,IAAI,EAAE2I,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAACsK,OAAO,CAAC;UACvE,MAAMlJ,QAAQ,GAAG2B,GAAG,CAACY,iBAAiB,CAACwJ,YAAY,GAAGtM,KAAK,CAACO,QAAQ,GAAG,CAAC;;;UAGxE,KAAK,IAAIJ,KAAK,GAAG,CAAC,EAAEA,KAAK,KAAKI,QAAQ,EAAE,EAAEJ,KAAK,EAAE;YAC7CJ,uBAAuB,CAACC,KAAK,EAAEW,KAAK,EAAEE,MAAM,EAAEV,KAAK,EAAE,IAAI,CAACsK,SAAS,EAAEhF,UAAU,CAACpF,gBAAgB,CAAC;YACjG,MAAMuI,KAAK,GAAG/C,IAAI,CAACO,QAAQ,CAACC,SAAS,CAACqD,IAAI,EAAE,eAAe,CAAC;YAC5D,IAAI,CAACjE,UAAU,CAACvE,QAAQ,EAAE;;cACtB0H,KAAK,CAACwB,WAAW,CAAC,IAAI,CAACK,SAAS,CAAC;;YAErC7B,KAAK,CACAM,QAAQ,CAAChQ,MAAM,EAAEiQ,UAAU,CAAC7C,MAAM,GAAG6C,UAAU,CAACQ,IAAI,GAAGR,UAAU,CAACS,aAAa,CAAC,CAChFC,eAAe,CAAC7J,KAAK,EAAEG,KAAK,CAAC;;;QAGlCgM,4BAA4BA,CAChCjK,GAA4B,EAC5BwD,aAA2D,EAC3DnP,EAAU,EACV0U,SAAiD,EACjD3L,KAAkC,EAC9B;UACJ,MAAMiN,sBAAsB,GAAGC,QAAQ,CAACC,QAAQ,CAACF,sBAA4D;UAC7G,IAAI,CAACA,sBAAsB,EAAE;YACzB;;UAEJ,MAAMZ,iBAAiB,GAAGlS,SAAS,CAACkS,iBAAiB;UACrD,MAAMe,MAAM,GAAGH,sBAAsB,CAACI,SAAS,EAAE;UACjD,MAAMC,aAAa,GAAG,CAAC;UACvB,IAAIC,OAAO,GAAG,CAAC;UACf,KAAK,MAAMC,KAAK,IAAIJ,MAAM,EAAE;YACxB,IAAI,CAACI,KAAK,CAACC,UAAU,EAAE;cACnB;;YAEJ,MAAMC,IAAI,GAAGF,KAAK,CAACG,UAAU,EAAE;YAC/B,MAAMtM,KAAK,GAAGvG,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAACkM,IAAI,CAACnJ,CAAC,CAAC,EAAE,CAAC,CAAC;YAC7C,MAAMhD,MAAM,GAAGzG,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAACkM,IAAI,CAACpF,CAAC,CAAC,EAAE,CAAC,CAAC;YAE9C,IAAIkF,KAAK,CAACI,SAAS,KAAK3N,QAAQ,CAACD,KAAK,CAAC6N,SAAS,CAACC,MAAM,EAAE;cACrD,IAAI,CAAC1H,aAAa,CAACyF,2BAA2B,EAAE;gBAC5C;;cAEJ,MAAMM,MAA6B,GAAGqB,KAAK,CAACO,qBAAqB,CAAE5B,MAAO;cAC1E,MAAMrH,SAAS,mBAAmByI,SAAS;cAC3C,MAAMxI,gBAAgB,mBAAmBwI,SAAS;;cAElD3K,GAAG,CAACoL,eAAe,CAAClJ,SAAS,EACzBsB,aAAa,CAACd,cAAc,EAAEjE,KAAK,EAAEE,MAAM,EAAE4K,MAAM,CAAC;cACxDvJ,GAAG,CAACsH,eAAe,CAACnF,gBAAgB,EAChChH,GAAG,CAAC2B,MAAM,CAAC4E,aAAa,EAAEjD,KAAK,EAAEE,MAAM,EAAE8K,iBAAiB,CAACG,UAAU,CAAC;;;cAG1E,MAAMyB,SAAS,GAAGrL,GAAG,CAAC4D,aAAa,CAACnF,KAAK,EAAEE,MAAM,EAAE,SAAS,CAAC;cAC7D0M,SAAS,CAAC/W,IAAI,2BAA2BqW,SAAS;cAClD,IAAI,CAACW,yBAAyB,CAACD,SAAS,EAAE7H,aAAa,EAAEnP,EAAE,EAAEuW,KAAK,CAAC5T,MAAM,EACrEkL,SAAS,EAAEC,gBAAgB,EAAE4G,SAAS,EAAE3L,KAAK,CAAC;;YAqBtD,EAAEuN,OAAO;YACT,IAAIA,OAAO,KAAKD,aAAa,EAAE;cAC3B;;;;QAIJY,yBAAyBA,CAC7B3H,IAAsC,EACtCH,aAA2D,EAC3DnP,EAAU,EACV2C,MAA6B,EAC7BkL,SAAiB,EACjBC,gBAAwB,EACxB4G,SAAiD,EACjD3L,KAAkC,GAAG,IAAI,EACrC;UACJ,MAAM+G,SAAS,GAAG5M,SAAS,CAAC4M,SAAS;UACrC,MAAM8C,UAAU,GAAG1P,SAAS,CAAC0P,UAAU;;UAEvC,MAAMsE,YAAY,GAAG/H,aAAa,CAAC4F,UAAU,GAAGnM,OAAO,CAACsK,OAAO,GAAGtK,OAAO,CAAC8G,KAAK;;;UAG/E,IAAItG,qBAAqB,CAACzG,MAAM,CAAC,EAAE;YAC/B,IAAI,CAACyR,0BAA0B,CAAC9G,CAAC,GAAG3K,MAAM,CAACwU,UAAU,CAAC7J,CAAC;YACvD,IAAI,CAAC8G,0BAA0B,CAAC/C,CAAC,GAAG1O,MAAM,CAACwU,UAAU,CAAC9F,CAAC;YACvD,IAAI,CAAC+C,0BAA0B,CAAC9C,CAAC,GAAG3O,MAAM,CAACwU,UAAU,CAAC7F,CAAC;YACvD,MAAM6F,UAAU,GAAGjU,SAAS,CAACkU,QAAQ,CAAC,IAAI,CAAChD,0BAA0B,CAAC;YACtE,IAAI,CAACD,WAAW,CAAC7G,CAAC,GAAG6J,UAAU,CAAC7J,CAAC;YACjC,IAAI,CAAC6G,WAAW,CAAC9C,CAAC,GAAG8F,UAAU,CAAC9F,CAAC;YACjC,IAAI,CAAC8C,WAAW,CAAC7C,CAAC,GAAG6F,UAAU,CAAC7F,CAAC;YACjC,IAAI,CAAC6C,WAAW,CAACzK,CAAC,GAAGyN,UAAU,CAACzN,CAAC;YACjC4F,IAAI,CAACE,eAAe,CAAC3B,SAAS,EAAElF,MAAM,CAAC8G,KAAK,EAAEyH,YAAY,EAAE,IAAI,CAAC/C,WAAW,CAAC;WAChF,MAAM;YACH7E,IAAI,CAACE,eAAe,CAAC3B,SAAS,EAAElF,MAAM,CAACmL,IAAI,EAAEoD,YAAY,CAAC;;;;UAI9D,IAAIvU,MAAM,CAAC0G,SAAS,GAAGd,YAAY,CAAC8E,aAAa,EAAE;YAC/CiC,IAAI,CAAC2D,eAAe,CAChBnF,gBAAgB,EAChBnF,MAAM,CAAC8G,KAAK,EACZ7G,OAAO,CAACsK,OAAO,EACfvQ,MAAM,CAAC0U,UAAU,EACjB1U,MAAM,CAAC2U,YAAY,EACnB3U,MAAM,CAAC0G,SAAS,GAAGd,YAAY,CAAC8E,aACpC,CAAC;WACJ,MAAM;YACHiC,IAAI,CAAC2D,eAAe,CAACnF,gBAAgB,EAAEnF,MAAM,CAACmL,IAAI,EAAElL,OAAO,CAACsK,OAAO,CAAC;;;;UAIxE,IAAI/D,aAAa,CAACsF,wBAAwB,EAAE;YACxCnF,IAAI,CAACK,UAAU,aAAa3P,IAAI,EAAE,cAAc,CAAC;;;;;;UAMrDsP,IAAI,CAACO,QAAQ,CAACC,SAAS,CAACqD,IAAI,EAAE,aAAa,CAAC;WACvCR,QAAQ,CAAChQ,MAAM,EACZiQ,UAAU,CAAC7C,MAAM,GAAG6C,UAAU,CAACQ,IAAI,GAAGR,UAAU,CAAC2E,gBAAgB,EACjE7C,SAAS,IAAIvR,SAAS,EACtB4F,KAAK,GAAGA,KAAK,GAAG5F,SAAS,CAAC;;QAE9B0S,yBAAyBA,CAC7BlK,GAA4B,EAC5BuD,UAAqC,EACrCC,aAA2D,EAC3DnP,EAAU,EACV2C,MAA6B,EAC7ByH,KAAa,EACbE,MAAc,EACdoK,SAAiD,EACjD7G,SAAiB,EACjBC,gBAAwB,EACxB0J,WAAoB,GAAG,KAAK,EAC5B/D,mBAAgC,GAAG7K,OAAO,CAACsK,OAAO,EAClB;UAChC,MAAMpD,SAAS,GAAG5M,SAAS,CAAC4M,SAAS;UACrC,MAAM8C,UAAU,GAAG1P,SAAS,CAAC0P,UAAU;;;;;UAKvC,MAAMuE,UAAU,GAAGxU,MAAM,CAACwU,UAAU,CAAC;UACrC,IAAI,CAAChD,WAAW,CAAC7G,CAAC,GAAG6J,UAAU,CAAC7J,CAAC;UACjC,IAAI,CAAC6G,WAAW,CAAC9C,CAAC,GAAG8F,UAAU,CAAC9F,CAAC;UACjC,IAAI,CAAC8C,WAAW,CAAC7C,CAAC,GAAG6F,UAAU,CAAC7F,CAAC;UACjC,IAAI,CAAC6C,WAAW,CAACzK,CAAC,GAAGyN,UAAU,CAACzN,CAAC;;;UAGjC,MAAMgK,QAAQ,GAAG/Q,MAAM,CAAC+Q,QAAQ,CAAC;UACjC,IAAI,CAACQ,SAAS,CAAChK,IAAI,GAAGrG,IAAI,CAAC4T,KAAK,CAAC/D,QAAQ,CAACpG,CAAC,GAAGlD,KAAK,CAAC;UACpD,IAAI,CAAC8J,SAAS,CAAC/J,GAAG,GAAGtG,IAAI,CAAC4T,KAAK,CAAC/D,QAAQ,CAACrC,CAAC,GAAG/G,MAAM,CAAC;;;UAGpD,IAAI,CAAC4J,SAAS,CAAC9J,KAAK,GAAGvG,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC4T,KAAK,CAAC/D,QAAQ,CAACtJ,KAAK,GAAGA,KAAK,CAAC,EAAE,CAAC,CAAC;UACtE,IAAI,CAAC8J,SAAS,CAAC5J,MAAM,GAAGzG,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC4T,KAAK,CAAC/D,QAAQ,CAACpJ,MAAM,GAAGA,MAAM,CAAC,EAAE,CAAC,CAAC;;;UAGzE,MAAMyK,UAAU,GAAG,CAACyC,WAAW,IAAIrI,aAAa,CAAC4F,UAAU;UAC3D1F,MAAM,CAAC,CAAC0F,UAAU,IAAI5F,aAAa,CAAC6F,uBAAuB,CAAC;;;;;UAK5D,MAAM1F,IAAI,GAAGH,aAAa,CAAC6F,uBAAuB,GAC5C,IAAI,CAAC0C,6BAA6B,CAAC/L,GAAG,EAAEuD,UAAU,EAAEC,aAAa,EAC/DnP,EAAE,EAAE2C,MAAM,EAAEoS,UAAU,EAAE3K,KAAK,EAAEE,MAAM,EAAEoK,SAAS,EAChD7G,SAAS,EAAEC,gBAAgB,EAAE2F,mBAAmB,CAAC,GACnD,IAAI,CAACkE,iCAAiC,CAAChM,GAAG,EAAEwD,aAAa,EACvDnP,EAAE,EAAE2C,MAAM,EAAEyH,KAAK,EAAEE,MAAM,EAAEoK,SAAS,EACpC7G,SAAS,EAAEC,gBAAgB,EAAE2F,mBAAmB,CAAC;;;UAGzD,IAAItE,aAAa,CAACwF,8BAA8B,EAAE;YAC9C,IAAI,CAACiD,qBAAqB,CAACjV,MAAM,EAAE+R,SAAS,EAAEpF,IAAI,CAAC;;;;;;;;UAQvD,MAAMuI,UAAU,GAAGjF,UAAU,CAACN,KAAK,IAC9B3P,MAAM,CAACmV,gBAAgB,GAClBlF,UAAU,CAACmF,QAAQ,GACnBnF,UAAU,CAACO,IAAI,CAAC;UAE1B7D,IAAI,CACCO,QAAQ,CAACC,SAAS,CAACwC,KAAK,CAAC,CACzBK,QAAQ,CAAChQ,MAAM,EAAEkV,UAAU,EAAEnD,SAAS,IAAIvR,SAAS,CAAC;UAEzD,OAAOmM,IAAI;;QAEPoI,6BAA6BA,CACjC/L,GAA4B,EAC5BuD,UAAqC,EACrCC,aAA2D,EAC3DnP,EAAU,EACV2C,MAA6B,EAC7BoS,UAAmB,EACnB3K,KAAa,EACbE,MAAc,EACdoK,SAAiD,EACjD7G,SAAiB,EACjBC,gBAAwB,EACxB2F,mBAAgC,EACA;UAChCpE,MAAM,CAACF,aAAa,CAAC6F,uBAAuB,CAAC;;;;UAI7C,IAAI1F,IAAsC;UAC1C,IAAIyF,UAAU,EAAE;YACZ,MAAMiD,gBAAgB,kBAAkBhY,IAAI;YAC5C,MAAMiY,oBAAoB,sBAAsBjY,IAAI;YACpD,MAAMiE,WAAW,GAAGkL,aAAa,CAACzB,QAAQ,CAACjK,IAAI,CAACQ,WAAW;YAE3D,MAAMiU,MAAM,GAAGvM,GAAG,CAACwM,wBAAwB,CAAC/N,KAAK,EAAEE,MAAM,EAAErG,WAAW,EAAE,CAAC,EAAE,SAAS,CAAC;YACrFiU,MAAM,CAACjY,IAAI,GAAG,iBAAiB;;;YAG/B,IAAI,CAACmY,0BAA0B,CAACF,MAAM,EAAE/I,aAAa,EAAEnP,EAAE,EAAE2C,MAAM,EAC7DqV,gBAAgB,EAAEC,oBAAoB,EAAErP,OAAO,CAACsK,OAAO,EAAEwB,SAAS,CAAC;YAEvEwD,MAAM,CAACG,mBAAmB,CAACL,gBAAgB,EAAEnK,SAAS,CAAC;YAEvDyB,IAAI,GAAG4I,MAAM;WAChB,MAAM;YACH5I,IAAI,GAAG3D,GAAG,CAAC4D,aAAa,CAACnF,KAAK,EAAEE,MAAM,EAAE,SAAS,CAAC;YAClDgF,IAAI,CAACrP,IAAI,GAAG,aAAa;YAEzB,IAAI,CAACmY,0BAA0B,CAAC9I,IAAI,EAAEH,aAAa,EAAEnP,EAAE,EAAE2C,MAAM,EAC3DkL,SAAS,EAAEC,gBAAgB,EAAE2F,mBAAmB,EAAEiB,SAAS,CAAC;;UAEpErF,MAAM,CAACC,IAAI,KAAKnM,SAAS,CAAC;;;UAG1B,IAAI,CAAC8Q,eAAe,CAACV,cAAc,CAC/BjE,IAAI,EACJ3M,MAAM,EACNuM,UAAU,CAAC3D,4BACf,CAAC;UAED,OAAO+D,IAAI;;QAEPqI,iCAAiCA,CACrChM,GAA4B,EAC5BwD,aAA2D,EAC3DnP,EAAU,EACV2C,MAA6B,EAC7ByH,KAAa,EACbE,MAAc,EACdoK,SAAiD,EACjD7G,SAAiB,EACjBC,gBAAwB,EACxB2F,mBAAgC,EACA;UAChCpE,MAAM,CAAC,CAACF,aAAa,CAAC6F,uBAAuB,CAAC;;;UAG9C,IAAI1F,IAAI,GAAG3D,GAAG,CAAC4D,aAAa,CAACnF,KAAK,EAAEE,MAAM,EAAE,SAAS,CAAC;UACtDgF,IAAI,CAACrP,IAAI,GAAG,aAAa;UAEzB,MAAMqY,YAAY,GAAG,IAAI,CAACrE,eAAe,CAACF,2BAA2B,EAAE,GACjEnL,OAAO,CAAC8G,KAAK,GACb+D,mBAAmB;UAEzB,IAAI,CAAC2E,0BAA0B,CAAC9I,IAAI,EAAEH,aAAa,EAC/CnP,EAAE,EAAE2C,MAAM,EAAEkL,SAAS,EAAEC,gBAAgB,EAAEwK,YAAY,EAAE5D,SAAS,CAAC;;;UAGrEpF,IAAI,GAAG,IAAI,CAAC2E,eAAe,CACtBT,cAAc,CAAC3F,SAAS,EAAEC,gBAAgB,EAAE2F,mBAAmB,EAC5DzT,EAAE,EAAEoK,KAAK,EAAEE,MAAM,EAAE3H,MAAM,EAAE,IAAI,CAACuR,SAAS,EAAEvI,GAAG,EAAE2D,IAAI,CAAC;UAE7D,OAAOA,IAAI;;QAEP8I,0BAA0BA,CAC9B9I,IAAsC,EACtCH,aAA2D,EAC3DnP,EAAU,EACV2C,MAA6B,EAC7BkL,SAAiB,EACjBC,gBAAwB,EACxB2F,mBAAgC,EAChCiB,SAAiD,EACjD3L,KAAkC,GAAG,IAAI,EACrC;UACJ,MAAM+G,SAAS,GAAG5M,SAAS,CAAC4M,SAAS;UACrC,MAAM8C,UAAU,GAAG1P,SAAS,CAAC0P,UAAU;;UAEvCtD,IAAI,CAACuE,WAAW,CAAC,IAAI,CAACK,SAAS,CAAC;UAEhC,MAAMgD,YAAY,GAAG/H,aAAa,CAAC4F,UAAU,GAAGnM,OAAO,CAACsK,OAAO,GAAGtK,OAAO,CAAC8G,KAAK;;;UAG/E,IAAItG,qBAAqB,CAACzG,MAAM,CAAC,EAAE;YAC/B2M,IAAI,CAACE,eAAe,CAAC3B,SAAS,EAAElF,MAAM,CAAC8G,KAAK,EAAEyH,YAAY,EAAE,IAAI,CAAC/C,WAAW,CAAC;WAChF,MAAM;YACH7E,IAAI,CAACE,eAAe,CAAC3B,SAAS,EAAElF,MAAM,CAACmL,IAAI,EAAEoD,YAAY,CAAC;;;;UAInD;YACP,IAAIrJ,SAAS,KAAKsB,aAAa,CAACtB,SAAS,IACrCC,gBAAgB,KAAKqB,aAAa,CAACrB,gBAAgB,EAAE;cACrDyK,IAAI,CAAC,4DAA4D,CAAC;;;UAI1E,IAAI5V,MAAM,CAAC0G,SAAS,GAAGd,YAAY,CAAC8E,aAAa,EAAE;YAC/CiC,IAAI,CAAC2D,eAAe,CAChBnF,gBAAgB,EAChBnF,MAAM,CAAC8G,KAAK,EACZgE,mBAAmB,EACnB9Q,MAAM,CAAC0U,UAAU,EACjB1U,MAAM,CAAC2U,YAAY,EACnB3U,MAAM,CAAC0G,SAAS,GAAGd,YAAY,CAAC8E,aACpC,CAAC;WACJ,MAAM;YACHiC,IAAI,CAAC2D,eAAe,CAACnF,gBAAgB,EAAEnF,MAAM,CAACmL,IAAI,EAAEL,mBAAmB,CAAC;;;;UAI5E,IAAItE,aAAa,CAACsF,wBAAwB,EAAE;YACxCnF,IAAI,CAACK,UAAU,aAAa3P,IAAI,EAAE,cAAc,CAAC;;;;;;UAMrDsP,IAAI,CAACO,QAAQ,CAACC,SAAS,CAACqD,IAAI,CAAC;WACxBR,QAAQ,CAAChQ,MAAM,EACZiQ,UAAU,CAAC7C,MAAM,GAAG6C,UAAU,CAACQ,IAAI,EACnCsB,SAAS,IAAIvR,SAAS,EACtB4F,KAAK,GAAGA,KAAK,GAAG5F,SAAS,CAAC;;QAE9ByU,qBAAqBA,CACzBjV,MAA6B,EAC7B+R,SAAiD,EACjDpF,IAAsC,EACxC;UACE,MAAMQ,SAAS,GAAG5M,SAAS,CAAC4M,SAAS;UACrC,MAAM8C,UAAU,GAAG1P,SAAS,CAAC0P,UAAU;UACvCtD,IAAI,CAACO,QAAQ,CAACC,SAAS,CAACwC,KAAK,EAAE,eAAe,CAAC,CAC1CK,QAAQ,CACLhQ,MAAM,EACNiQ,UAAU,CAACS,aAAa,GAAGT,UAAU,CAAC4F,aAAa,GAAG5F,UAAU,CAACN,KAAK,EACtEoC,SAAS,IAAIvR,SACjB,CAAC;;MAMb;;MA7jBa6Q,yBAAyB,CAC3BK,WAAW,GAAG,GAAG;MADfL,yBAAyB,CAE3BM,WAAW,GAAG,GAAG;MAikBrB,MAAMmE,uBAAuB,CAA0C;QAAAzW;;UAkK1E,KACiB0W,2BAA2B,GAAG,IAAIlQ,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAAA,KACnDmQ,YAAY,GAAG,IAAIlN,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAAA,KACnCmN,aAAa,GAAG,IAAInN,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAAA,KACpCoN,YAAY,GAAkB,EAAE;UAAA,KAChCC,aAAa,GAAkB,EAAE;UAAA,KACjCC,cAAc,GAAkB,EAAE;;QAvKnDjK,cAAcA,CAAAA,EAAW;UACrB,OAAO,CAAC;;QAEZE,cAAcA,CAAAA,EAAW;UACrB,OAAO,GAAG;;QAEduF,YAAYA,CACR5R,MAAuC,EACvC6R,eAA0C,EAC1CrF,aAA+C,EAAQ;UACvDA,aAAa,CAAC6J,WAAW,GACnB7J,aAAa,CAACzB,QAAQ,CAACpJ,KAAK,CAACZ,OAAO,IACnC,CAAC,CAACyL,aAAa,CAACzB,QAAQ,CAACpJ,KAAK,CAACE,QAAQ;UAC9C,IAAI2K,aAAa,CAAC6J,WAAW,EAAE;YAC3B,EAAE7J,aAAa,CAAClB,eAAe;;;QAGvCgH,YAAYA,CACRtJ,GAA4B,EAC5BuD,UAAqC,EACrCC,aAA+C,EAC/C+F,MAA6B,EAAQ;UACrC,IAAI/F,aAAa,CAAC6J,WAAW,EAAE;YAC3B,MAAMhZ,EAAE,GAAGkV,MAAM,CAACtH,cAAc;YAChC,IAAIqL,UAAU,GAAG9J,aAAa,CAAC/E,KAAK;YACpC,IAAI8O,WAAW,GAAG/J,aAAa,CAAC7E,MAAM;YACtC,KAAK,IAAIyI,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAK5D,aAAa,CAACzB,QAAQ,CAACpJ,KAAK,CAACM,UAAU,GAAG,CAAC,EAAE,EAAEmO,CAAC,EAAE;cACpEkG,UAAU,GAAGpV,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAAC0O,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;cACpDC,WAAW,GAAGrV,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAAC2O,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;cACtDvN,GAAG,CAAC6D,eAAe,YAAYxP,MAAM+S,GAAG,EACpC5D,aAAa,CAACd,cAAc,EAAE4K,UAAU,EAAEC,WAAW,CAAC;;;;QAKtEzD,KAAKA,CACD9J,GAA4B,EAC5BuD,UAAqC,EACrCC,aAA+C,EAC/CxM,MAA6B,EAC7B+S,OAAwB,EACxByD,cAAiD,EACF;UAC/C,IAAI,CAAChK,aAAa,CAAC6J,WAAW,EAAE;YAC5B,OAAOG,cAAc;;UAGzB,EAAEhK,aAAa,CAAClB,eAAe;UAC/BoB,MAAM,CAACF,aAAa,CAAClB,eAAe,IAAI,CAAC,CAAC;UAC1C,MAAMjO,EAAE,GAAG2C,MAAM,CAACuS,MAAM,CAACtH,cAAc;UACvCyB,MAAM,CAAC,CAAC,CAACF,aAAa,CAACzB,QAAQ,CAACpJ,KAAK,CAACE,QAAQ,CAAC;UAC/C,OAAO,IAAI,CAAC4U,+BAA+B,CACvCzN,GAAG,EAAEuD,UAAU,EACfC,aAAa,EACbA,aAAa,CAACzB,QAAQ,EACtByB,aAAa,CAACzB,QAAQ,CAACpJ,KAAK,CAACE,QAAQ,EACrCxE,EAAE,EACFmP,aAAa,CAAC/E,KAAK,EACnB+E,aAAa,CAAC7E,MAAM,EACpBoL,OAAO,CAAC7H,SAAS,CAAC;;QAGlBuL,+BAA+BA,CACnCzN,GAA4B,EAC5BuD,UAAqC,EACrCC,aAAyD,EACzDzB,QAA0B,EAC1BnJ,aAAuB,EACvBvE,EAAU,EACVoK,KAAa,EACbE,MAAc,EACd+O,YAAoB,EACY;UAChC,MAAMvJ,SAAS,GAAG5M,SAAS,CAAC4M,SAAS;;;;;;UAMrC,MAAMlL,UAAU,GAAG8I,QAAQ,CAACpJ,KAAK,CAACM,UAAU;UAC5C,MAAM0U,SAAS,GAAG1U,UAAU,GAAG,CAAC;UAChC,IAAI,CAACiU,YAAY,CAACtI,MAAM,GAAG+I,SAAS;UACpC,IAAI,CAACR,aAAa,CAACvI,MAAM,GAAG+I,SAAS;UACrC,IAAI,CAACT,YAAY,CAAC,CAAC,CAAC,GAAGhV,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;UACzD,IAAI,CAAC0O,aAAa,CAAC,CAAC,CAAC,GAAGjV,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAACD,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;UAC3D,KAAK,IAAIyI,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAKuG,SAAS,EAAE,EAAEvG,CAAC,EAAE;YAClC,IAAI,CAAC8F,YAAY,CAAC9F,CAAC,CAAC,GAAGlP,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAAC,IAAI,CAACsO,YAAY,CAAC9F,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5E,IAAI,CAAC+F,aAAa,CAAC/F,CAAC,CAAC,GAAGlP,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAAC,IAAI,CAACuO,aAAa,CAAC/F,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;;;UAIlF,IAAI,CAACgG,cAAc,CAACxI,MAAM,GAAG+I,SAAS;UACtC,KAAK,IAAIvG,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAKuG,SAAS,EAAE,EAAEvG,CAAC,EAAE;YAClC,IAAI,CAACgG,cAAc,CAAChG,CAAC,CAAC,cAAc/S,MAAM+S,GAAG;;;;UAIjD,IAAI,CAAC4F,YAAY,CAACrL,CAAC,GAAG4B,UAAU,CAACpE,cAAc,GAAG,CAAC,GAAG,CAAC;UACvD,IAAI,CAAC6N,YAAY,CAACrL,CAAC,GAAG,CAAC,CAAC;UACxB,IAAI,CAACqL,YAAY,CAACrH,CAAC,GAAG5D,QAAQ,CAACpJ,KAAK,CAACQ,SAAS;UAC9C,IAAI,CAAC6T,YAAY,CAACjP,CAAC,GAAGgE,QAAQ,CAACpJ,KAAK,CAACI,eAAe,GAAG,CAAC,GAAG,CAAC;;;UAG5D,MAAM6U,aAAa,GAAG5N,GAAG,CAAC4D,aAAa,CAAC,IAAI,CAACsJ,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,EAAE,oBAAoB,CAAC;UAC1GS,aAAa,CAAC/J,eAAe,CACzB,IAAI,CAACuJ,cAAc,CAAC,CAAC,CAAC,EACtBpQ,MAAM,CAAC8G,KAAK,EACZ7G,OAAO,CAAC8G,KAAK,EACb,IAAI,CAACgJ,2BACT,CAAC;UACDa,aAAa,CAAC5J,UAAU,CAAC0J,YAAY,EAAE,cAAc,CAAC;UACtDE,aAAa,CAAC3J,OAAO,CAAC,YAAY,EAAEV,UAAU,CAAC1D,QAAQ,CAAC;UACxD+N,aAAa,CAAC3J,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC+I,YAAY,CAAC;UACvDY,aAAa,CACR1J,QAAQ,CAACC,SAAS,CAACC,MAAM,CAAC,CAC1BC,iBAAiB,CAACzL,aAAa,EAAE,CAAC,CAAC;;;UAGxC,KAAK,IAAIwO,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAKuG,SAAS,EAAE,EAAEvG,CAAC,EAAE;YAClC,MAAMyG,QAAQ,GAAG7N,GAAG,CAAC4D,aAAa,CAAC,IAAI,CAACsJ,YAAY,CAAC9F,CAAC,CAAC,EAAE,IAAI,CAAC+F,aAAa,CAAC/F,CAAC,CAAC,EAAE,qBAAqB,CAAC;YACtGyG,QAAQ,CAAChK,eAAe,CAAC,IAAI,CAACuJ,cAAc,CAAChG,CAAC,CAAC,EAAEpK,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAAC8G,KAAK,EAAE,IAAI,CAACgJ,2BAA2B,CAAC;YAC/Gc,QAAQ,CAAC7J,UAAU,CAAC,IAAI,CAACoJ,cAAc,CAAChG,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC;YAC/D,IAAI,CAAC6F,aAAa,CAACtL,CAAC,GAAG,IAAI,CAACuL,YAAY,CAAC9F,CAAC,GAAG,CAAC,CAAC;YAC/C,IAAI,CAAC6F,aAAa,CAACvH,CAAC,GAAG,IAAI,CAACyH,aAAa,CAAC/F,CAAC,GAAG,CAAC,CAAC;YAChDyG,QAAQ,CAAC5J,OAAO,CAAC,YAAY,EAAEV,UAAU,CAAC1D,QAAQ,CAAC;YACnDgO,QAAQ,CAAC5J,OAAO,CAAC,cAAc,EAAE,IAAI,CAACgJ,aAAa,CAAC;YACpDY,QAAQ,CACH3J,QAAQ,CAACC,SAAS,CAACC,MAAM,CAAC,CAC1BC,iBAAiB,CAACzL,aAAa,EAAE,CAAC,CAAC;;;;UAI5C,KAAK,IAAIwO,CAAC,GAAGnO,UAAU,EAAEmO,CAAC,EAAE,GAAG,CAAC,GAAG;YAC/B,MAAM0G,MAAM,GAAG9N,GAAG,CAAC4D,aAAa,CAAC,IAAI,CAACsJ,YAAY,CAAC9F,CAAC,CAAC,EAAE,IAAI,CAAC+F,aAAa,CAAC/F,CAAC,CAAC,EAAE,mBAAmB,CAAC;YAClG0G,MAAM,CAACjK,eAAe,CAAC,IAAI,CAACuJ,cAAc,CAAChG,CAAC,CAAC,EAAEpK,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAAC8G,KAAK,EAAE,IAAI,CAACgJ,2BAA2B,CAAC;YAC7Ge,MAAM,CAAC9J,UAAU,CAAC,IAAI,CAACoJ,cAAc,CAAChG,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC;YAC7D,IAAI,CAAC6F,aAAa,CAACtL,CAAC,GAAG,IAAI,CAACuL,YAAY,CAAC9F,CAAC,GAAG,CAAC,CAAC;YAC/C,IAAI,CAAC6F,aAAa,CAACvH,CAAC,GAAG,IAAI,CAACyH,aAAa,CAAC/F,CAAC,GAAG,CAAC,CAAC;YAChD0G,MAAM,CAAC7J,OAAO,CAAC,YAAY,EAAEV,UAAU,CAAC1D,QAAQ,CAAC;YACjDiO,MAAM,CAAC7J,OAAO,CAAC,cAAc,EAAE,IAAI,CAACgJ,aAAa,CAAC;YAClDa,MAAM,CACD5J,QAAQ,CAACC,SAAS,CAACC,MAAM,CAAC,CAC1BC,iBAAiB,CAACzL,aAAa,EAAE,CAAC,CAAC;;;;UAI5C,MAAMmV,WAAW,GAAG/N,GAAG,CAAC4D,aAAa,CAACnF,KAAK,EAAEE,MAAM,EAAE,kBAAkB,CAAC;UACxEoP,WAAW,CAAClK,eAAe,CAAC6J,YAAY,EAAE1Q,MAAM,CAACmL,IAAI,EAAElL,OAAO,CAAC8G,KAAK,CAAC;UACrEgK,WAAW,CAAC/J,UAAU,CAAC,IAAI,CAACoJ,cAAc,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC;UAC9DW,WAAW,CAAC9J,OAAO,CAAC,YAAY,EAAEV,UAAU,CAAC1D,QAAQ,CAAC;UACtDkO,WAAW,CAAC9J,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC+I,YAAY,CAAC;UACrDe,WAAW,CACN7J,QAAQ,CAACC,SAAS,CAACwC,KAAK,CAAC,CACzBtC,iBAAiB,CAACzL,aAAa,EAAE,CAAC,CAAC;UAExC,IAAI4K,aAAa,CAAClB,eAAe,KAAK,CAAC,EAAE;YACrC,OAAOgB,mBAAmB,CAACtD,GAAG,EAAEuD,UAAU,EAAEC,aAAa,EAAEkK,YAAY,CAAC;WAC3E,MAAM;YACH,OAAOK,WAAW;;;MAU9B;;MAOO,MAAMC,6BAA6B,CAA0C;QAAA3X;UAAA,KAsH/D4X,oBAAoB,GAAG,IAAIxO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;;QArHtD0D,cAAcA,CAAAA,EAAW;UACrB,OAAO,CAAC;;QAEZE,cAAcA,CAAAA,EAAW;UACrB,OAAO,GAAG;;QAEduF,YAAYA,CACR5R,MAAuC,EACvCuM,UAAqC,EACrCC,aAAqD,EAAQ;UAC7D,MAAMzB,QAAQ,GAAGyB,aAAa,CAACzB,QAAQ;UAEvCyB,aAAa,CAAC0K,kBAAkB,GAC1BnM,QAAQ,CAACxI,YAAY,CAACxB,OAAO,IAC5B,CAAC,CAACgK,QAAQ,CAACxI,YAAY,CAACV,QAAQ,IAChC,CAAC,CAACkJ,QAAQ,CAACxI,YAAY,CAACI,eAAe;UAE9C6J,aAAa,CAAC2K,iBAAiB,GACzB3K,aAAa,CAACf,SAAS;aACtBe,aAAa,CAAC0K,kBAAkB,CAAC;;UAExC,IAAI1K,aAAa,CAAC2K,iBAAiB,EAAE;YACjC,EAAE3K,aAAa,CAAClB,eAAe;;;QAGvCgH,YAAYA,CACRtJ,GAA4B,EAC5BuD,UAAqC,EACrCC,aAAqD,EAAQ;UAC7D,IAAIA,aAAa,CAAC0K,kBAAkB,EAAE;YAClCxK,MAAM,CAAC,CAAC,CAACF,aAAa,CAACzB,QAAQ,CAACxI,YAAY,CAACV,QAAQ,CAAC;YACtD2K,aAAa,CAACzB,QAAQ,CAACxI,YAAY,CAACV,QAAQ,CAACuV,WAAW,CACpD,iBAAiB,EACjB5K,aAAa,CAACzB,QAAQ,CAACxI,YAAY,CAACI,eAAe,CAAC;;;QAGhEmQ,KAAKA,CACD9J,GAA4B,EAC5BuD,UAAqC,EACrCC,aAAqD,EACrDxM,MAA6B,EAC7B+S,OAAwB,EACxByD,cAAiD,EACF;UAC/C,IAAI,CAAChK,aAAa,CAAC2K,iBAAiB,EAAE;YAClC,OAAOX,cAAc;;UAGzB,EAAEhK,aAAa,CAAClB,eAAe;UAC/BoB,MAAM,CAACF,aAAa,CAAClB,eAAe,IAAI,CAAC,CAAC;UAC1C,IAAIkB,aAAa,CAAClB,eAAe,KAAK,CAAC,EAAE;YACrC,OAAO,IAAI,CAAC+L,sBAAsB,CAACrO,GAAG,EAAEuD,UAAU,EAAEC,aAAa,EAC7DA,aAAa,CAACjB,WAAW,EAAEiB,aAAa,CAAChB,YAAY,EACrDuH,OAAO,CAAC7H,SAAS,EAAEsB,aAAa,CAACtB,SAAS,CAAC;WAClD,MAAM;YACH,MAAM7N,EAAE,GAAGmP,aAAa,CAACvB,cAAc;YACvC,MAAMqM,cAAc,GAAG9K,aAAa,CAAChL,kBAAkB,mBACjC,aACN;YAEhB,MAAM+V,YAAY,GAAGjK,uBAAuB,CAACyF,OAAO,CAAC7H,SAAS,EAAEoM,cAAc,EAAEja,EAAE,CAAC;YACnF,MAAMqZ,YAAY,GAAG3D,OAAO,CAAC7H,SAAS;YACtC6H,OAAO,CAAC7H,SAAS,GAAGqM,YAAY;YAEhC,OAAO,IAAI,CAACF,sBAAsB,CAACrO,GAAG,EAAEuD,UAAU,EAAEC,aAAa,EAC7DA,aAAa,CAAC/E,KAAK,EAAE+E,aAAa,CAAC7E,MAAM,EACzC+O,YAAY,EAAEa,YAAY,CAAC;;;QAG/BF,sBAAsBA,CAC1BrO,GAA4B,EAC5BuD,UAAqC,EACrCC,aAAqD,EACrD/E,KAAa,EACbE,MAAc,EACd+O,YAAoB,EACpBxL,SAAiB,EACe;UAChC,IAAIyB,IAAsC;UAC1C,MAAM5B,QAAQ,GAAGyB,aAAa,CAACzB,QAAQ;UACvC,IAAIyB,aAAa,CAAC0K,kBAAkB,EAAE;YAClCxK,MAAM,CAAC,CAAC,CAAC3B,QAAQ,CAACxI,YAAY,CAACV,QAAQ,CAAC;YACxC6K,MAAM,CAAC,CAAC,CAAC3B,QAAQ,CAACxI,YAAY,CAACI,eAAe,CAAC;YAE/C,MAAM6U,MAAM,GAAGzM,QAAQ,CAACxI,YAAY,CAACI,eAAe;YACpD,IAAI,CAACsU,oBAAoB,CAACtM,CAAC,GAAG6M,MAAM,CAAC/P,KAAK;YAC1C,IAAI,CAACwP,oBAAoB,CAACvI,CAAC,GAAG8I,MAAM,CAAC7P,MAAM;YAE3C,MAAM8P,WAAW,GAAGD,MAAM,CAAC/P,KAAK,KAAK+P,MAAM,CAAC7P,MAAM;YAClD,IAAI8P,WAAW,EAAE;cACb9K,IAAI,GAAG3D,GAAG,CAAC4D,aAAa,CAACnF,KAAK,EAAEE,MAAM,EAAE,sBAAsB,CAAC;aAClE,MAAM;cACHgF,IAAI,GAAG3D,GAAG,CAAC4D,aAAa,CAACnF,KAAK,EAAEE,MAAM,EAAE,sBAAsB,CAAC;;YAEnEgF,IAAI,CAACE,eAAe,CAAC3B,SAAS,EAAElF,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAAC8G,KAAK,EAAElB,2BAA2B,CAAC;YACzFc,IAAI,CAACK,UAAU,CAAC0J,YAAY,EAAE,eAAe,CAAC;YAC9C/J,IAAI,CAACM,OAAO,CAAC,YAAY,EAAEV,UAAU,CAAC1D,QAAQ,CAAC;YAC/C8D,IAAI,CAAC+K,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAACT,oBAAoB,CAAC;YACzDtK,IAAI,CAACgL,QAAQ,CAAC,YAAY,EAAE5M,QAAQ,CAACxI,YAAY,CAACG,UAAU,CAAC;YAC7DiK,IAAI,CAACO,QAAQ,CAAC3M,SAAS,CAAC4M,SAAS,CAACC,MAAM,CAAC,CACpCC,iBAAiB,CAACtC,QAAQ,CAACxI,YAAY,CAACV,QAAQ,EAAE4V,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;WAC9E,MAAM;YACH9K,IAAI,GAAG3D,GAAG,CAAC4D,aAAa,CAACnF,KAAK,EAAEE,MAAM,EAAE,iBAAiB,CAAC;YAC1DgF,IAAI,CAACE,eAAe,CAAC3B,SAAS,EAAElF,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAAC8G,KAAK,EAAElB,2BAA2B,CAAC;YACzFc,IAAI,CAACK,UAAU,CAAC0J,YAAY,EAAE,cAAc,CAAC;YAC7C/J,IAAI,CAACM,OAAO,CAAC,YAAY,EAAEV,UAAU,CAAC1D,QAAQ,CAAC;YAC/C,IAAIkC,QAAQ,CAACzH,WAAW,CAACzB,QAAQ,EAAE;cAC/B8K,IAAI,CAACO,QAAQ,CAAC3M,SAAS,CAAC4M,SAAS,CAACC,MAAM,CAAC,CACpCC,iBAAiB,CAACtC,QAAQ,CAACzH,WAAW,CAACzB,QAAQ,EAAE,CAAC,CAAC;aAC3D,MAAM;cACH6K,MAAM,CAAC,CAAC,CAACF,aAAa,CAACb,sBAAsB,CAAC;cAC9CgB,IAAI,CAACO,QAAQ,CAAC3M,SAAS,CAAC4M,SAAS,CAACC,MAAM,CAAC,CACpCC,iBAAiB,CAACb,aAAa,CAACb,sBAAsB,EAAE,CAAC,CAAC;;;UAGvE,OAAOgB,IAAI;;MAGnB;;MAMO,MAAMiL,sBAAsB,CAA0C;QAAAvY;;UA6FzE,KACiBwY,WAAW,GAAG,IAAI/O,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QA7FnDqD,cAAcA,CAAAA,EAAW;UACrB,OAAO,CAAC;;QAEZE,cAAcA,CAAAA,EAAW;UACrB,OAAO,GAAG;;QAEduF,YAAYA,CACR5R,MAAuC,EACvCuM,UAAqC,EACrCC,aAA8C,EAAQ;UACtDA,aAAa,CAACsL,UAAU,GAClBtL,aAAa,CAACzB,QAAQ,CAACjI,IAAI,CAAC/B,OAAO,IAClC,CAAC,CAACyL,aAAa,CAACzB,QAAQ,CAACjI,IAAI,CAACjB,QAAQ;UAC7C,IAAI2K,aAAa,CAACsL,UAAU,EAAE;YAC1B,EAAEtL,aAAa,CAAClB,eAAe;;;QAGvCwH,KAAKA,CACD9J,GAA4B,EAC5BuD,UAAqC,EACrCC,aAA8C,EAC9CxM,MAA6B,EAC7B+S,OAAwB,EACxByD,cAAiD,EACF;UAC/C,IAAI,CAAChK,aAAa,CAACsL,UAAU,EAAE;YAC3B,OAAOtB,cAAc;;UAEzB,EAAEhK,aAAa,CAAClB,eAAe;UAC/BoB,MAAM,CAACF,aAAa,CAAClB,eAAe,IAAI,CAAC,CAAC;UAE1C,MAAMjO,EAAE,GAAGmP,aAAa,CAACvB,cAAc;UACvC,MAAMqM,cAAc,GAAG9K,aAAa,CAAChL,kBAAkB,mBACjC,aACN;UAChB,MAAM+V,YAAY,GAAGjK,uBAAuB,CAACyF,OAAO,CAAC7H,SAAS,EAAEoM,cAAc,EAAEja,EAAE,CAAC;UAEnFqP,MAAM,CAAC,CAAC,CAACF,aAAa,CAACzB,QAAQ,CAACjI,IAAI,CAACjB,QAAQ,CAAC;UAC9C,IAAI2K,aAAa,CAAClB,eAAe,KAAK,CAAC,EAAE;YACrC,IAAIkB,aAAa,CAAChL,kBAAkB,EAAE;cAClC,IAAI,CAACuW,YAAY,CAAC/O,GAAG,EAAEuD,UAAU,EAC7BC,aAAa,CAACzB,QAAQ,CAACjI,IAAI,CAACjB,QAAQ,EACpC2K,aAAa,CAAC/E,KAAK,EACnB+E,aAAa,CAAC7E,MAAM,EACpBoL,OAAO,CAAC7H,SAAS,EACjBqM,YAAY,CAAC;cACjB,OAAOjL,mBAAmB,CAACtD,GAAG,EAAEuD,UAAU,EAAEC,aAAa,EAAE+K,YAAY,CAAC;aAC3E,MAAM;cACH7K,MAAM,CAACF,aAAa,CAAC/E,KAAK,KAAK+E,aAAa,CAACjB,WAAW,CAAC;cACzDmB,MAAM,CAACF,aAAa,CAAC7E,MAAM,KAAK6E,aAAa,CAAChB,YAAY,CAAC;cAC3D,OAAO,IAAI,CAACuM,YAAY,CAAC/O,GAAG,EAAEuD,UAAU,EACpCC,aAAa,CAACzB,QAAQ,CAACjI,IAAI,CAACjB,QAAQ,EACpC2K,aAAa,CAAC/E,KAAK,EACnB+E,aAAa,CAAC7E,MAAM,EACpBoL,OAAO,CAAC7H,SAAS,EACjBsB,aAAa,CAACtB,SAAS,CAAC;;WAEnC,MAAM;YACH,MAAM8M,cAAc,GAAGjF,OAAO,CAAC7H,SAAS;YACxC6H,OAAO,CAAC7H,SAAS,GAAGqM,YAAY;YAChC,MAAMU,QAAQ,GAAG,IAAI,CAACF,YAAY,CAAC/O,GAAG,EAAEuD,UAAU,EAC9CC,aAAa,CAACzB,QAAQ,CAACjI,IAAI,CAACjB,QAAQ,EACpC2K,aAAa,CAAC/E,KAAK,EACnB+E,aAAa,CAAC7E,MAAM,EACpBqQ,cAAc,EACdT,YAAY,CAAC;YACjB,OAAOU,QAAQ;;;QAGfF,YAAYA,CAChB/O,GAA4B,EAC5BuD,UAAqC,EACrCxJ,YAAsB,EACtB0E,KAAa,EACbE,MAAc,EACd4P,YAAoB,EACpBrM,SAAiB,EACe;UAChC,IAAI,CAAC2M,WAAW,CAAClN,CAAC,GAAGlD,KAAK;UAC1B,IAAI,CAACoQ,WAAW,CAACnJ,CAAC,GAAG/G,MAAM;UAC3B,IAAI,CAACkQ,WAAW,CAAClJ,CAAC,GAAG,CAAC,GAAGlH,KAAK;UAC9B,IAAI,CAACoQ,WAAW,CAAC9Q,CAAC,GAAG,CAAC,GAAGY,MAAM;UAE/B,MAAMgF,IAAI,GAAG3D,GAAG,CAAC4D,aAAa,CAACnF,KAAK,EAAEE,MAAM,EAAE,SAAS,CAAC;UACxDgF,IAAI,CAACE,eAAe,CAAC3B,SAAS,EAAElF,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAAC8G,KAAK,EAAElB,2BAA2B,CAAC;UACzFc,IAAI,CAACK,UAAU,CAACuK,YAAY,EAAE,eAAe,CAAC;UAC9C5K,IAAI,CAACM,OAAO,CAAC,YAAY,EAAEV,UAAU,CAAC1D,QAAQ,CAAC;UAC/C8D,IAAI,CAACM,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC4K,WAAW,CAAC;UACzClL,IAAI,CAACO,QAAQ,CAAC3M,SAAS,CAAC4M,SAAS,CAACC,MAAM,CAAC,CACpCC,iBAAiB,CAACtK,YAAY,EAAE,CAAC,CAAC;UACvC,OAAO4J,IAAI;;MAInB;;MAMO,MAAMuL,qBAAqB,CAA0C;QAAA7Y;;UAkGxE,KACiB8Y,UAAU,GAAG,IAAIrP,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAAA,KACjCsP,WAAW,GAAG,IAAItP,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAnGnDqD,cAAcA,CAAAA,EAAW;UACrB,OAAO,CAAC;;QAEZE,cAAcA,CAAAA,EAAW;UACrB,OAAO,GAAG;;QAEduF,YAAYA,CACR5R,MAAuC,EACvCuM,UAAqC,EACrCC,aAA6C,EAAQ;;UAErDA,aAAa,CAAC6L,SAAS,GAAG7L,aAAa,CAACzB,QAAQ,CAAC9H,GAAG,CAAClC,OAAO,IACrD,CAAC,CAACyL,aAAa,CAACzB,QAAQ,CAAC9H,GAAG,CAACpB,QAAQ,IACrC2K,aAAa,CAAChL,kBAAkB,IAChCgL,aAAa,CAAC/K,YAAY,GAAG,GAAG;UAEvC,IAAI+K,aAAa,CAAC6L,SAAS,EAAE;YACzB,EAAE7L,aAAa,CAAClB,eAAe;;;QAGvCwH,KAAKA,CACD9J,GAA4B,EAC5BuD,UAAqC,EACrCC,aAA6C,EAC7CxM,MAA6B,EAC7B+S,OAAwB,EACxByD,cAAiD,EACF;UAC/C,IAAI,CAAChK,aAAa,CAAC6L,SAAS,EAAE;YAC1B,OAAO7B,cAAc;;UAEzB,EAAEhK,aAAa,CAAClB,eAAe;UAE/B,MAAM0M,cAAc,GAAGjF,OAAO,CAAC7H,SAAS;UACxC,MAAMoN,eAAe,GACf9L,aAAa,CAAClB,eAAe,KAAK,CAAC,GAC/BkB,aAAa,CAACtB,SAAS,GACvBoC,uBAAuB,CAACyF,OAAO,CAAC7H,SAAS,EAAE,SAAS,EAAEsB,aAAa,CAACvB,cAAc,CAAC;UAC7F8H,OAAO,CAAC7H,SAAS,GAAGoN,eAAe;UAEnC5L,MAAM,CAAC,CAAC,CAACF,aAAa,CAACzB,QAAQ,CAAC9H,GAAG,CAACpB,QAAQ,CAAC;UAC7C,OAAO,IAAI,CAAC0W,WAAW,CAACvP,GAAG,EAAEuD,UAAU,EAAEC,aAAa,EAClDA,aAAa,CAACzB,QAAQ,EACtByB,aAAa,CAACzB,QAAQ,CAAC9H,GAAG,CAACpB,QAAQ,EACnC2K,aAAa,CAACvB,cAAc,EAC5BuB,aAAa,CAAC/E,KAAK,EACnB+E,aAAa,CAAC7E,MAAM,EACpBqQ,cAAc,EACdxL,aAAa,CAACjB,WAAW,EACzBiB,aAAa,CAAChB,YAAY,EAC1B8M,eAAe,CAAC;;QAEhBC,WAAWA,CACfvP,GAA4B,EAC5BuD,UAAqC,EACrCC,aAA6C,EAC7CzB,QAA0B,EAC1B7H,WAAqB,EACrB7F,EAAU,EACVoK,KAAa,EACbE,MAAc,EACdqQ,cAAsB,EACtBzM,WAAmB,EACnBC,YAAoB,EACpB8M,eAAuB,EACS;UAChC,IAAI,CAACF,WAAW,CAACzN,CAAC,GAAGlD,KAAK;UAC1B,IAAI,CAAC2Q,WAAW,CAAC1J,CAAC,GAAG/G,MAAM;UAC3B,IAAI,CAACyQ,WAAW,CAACzJ,CAAC,GAAGpD,WAAW;UAChC,IAAI,CAAC6M,WAAW,CAACrR,CAAC,GAAGyE,YAAY;UACjC,IAAI,CAAC2M,UAAU,CAACxN,CAAC,GAAG6N,KAAK,CAAC,GAAG,GAAGzN,QAAQ,CAAC9H,GAAG,CAACG,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC;UAEnE,MAAMqV,aAAa,GAAG,SAAS;UAE/B,MAAMC,YAAY,GAAGpL,uBAAuB,CAACgL,eAAe,EAAEG,aAAa,EAAEpb,EAAE,CAAC;UAEhF,MAAMsb,QAAQ,GAAG3P,GAAG,CAAC4D,aAAa,CAACrB,WAAW,EAAEC,YAAY,EAAE,aAAa,CAAC;UAC5EmN,QAAQ,CAAC9L,eAAe,CAAC6L,YAAY,EAAE1S,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAAC8G,KAAK,EAAElB,2BAA2B,CAAC;UAChG8M,QAAQ,CAAC3L,UAAU,CAACgL,cAAc,EAAE,iBAAiB,CAAC;UACtDW,QAAQ,CAAC1L,OAAO,CAAC,YAAY,EAAEV,UAAU,CAAC1D,QAAQ,CAAC;UACnD8P,QAAQ,CAAC1L,OAAO,CAAC,YAAY,EAAE,IAAI,CAACmL,WAAW,CAAC;UAChDO,QAAQ,CACHzL,QAAQ,CAAC3M,SAAS,CAAC4M,SAAS,CAACC,MAAM,CAAC,CACpCC,iBAAiB,CAACnK,WAAW,EAAE,CAAC,CAAC;UAEtC,MAAM0V,QAAQ,GAAG5P,GAAG,CAAC4D,aAAa,CAACrB,WAAW,EAAEC,YAAY,EAAE,aAAa,CAAC;UAC5EoN,QAAQ,CAAC/L,eAAe,CAACyL,eAAe,EAAEtS,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAAC8G,KAAK,EAAElB,2BAA2B,CAAC;UACnG+M,QAAQ,CAAC5L,UAAU,CAAC0L,YAAY,EAAE,iBAAiB,CAAC;UACpDE,QAAQ,CAAC3L,OAAO,CAAC,YAAY,EAAEV,UAAU,CAAC1D,QAAQ,CAAC;UACnD+P,QAAQ,CAAC3L,OAAO,CAAC,YAAY,EAAE,IAAI,CAACmL,WAAW,CAAC;UAChDQ,QAAQ,CAAC3L,OAAO,CAAC,WAAW,EAAE,IAAI,CAACkL,UAAU,CAAC;UAC9CS,QAAQ,CACH1L,QAAQ,CAAC3M,SAAS,CAAC4M,SAAS,CAACC,MAAM,CAAC,CACpCC,iBAAiB,CAACnK,WAAW,EAAE,CAAC,CAAC;UAEtC,OAAO0V,QAAQ;;MAKvB;;MAEO,MAAMC,oBAAoB,CAA0C;QACvE1M,cAAcA,CAAAA,EAAW;UACrB,OAAO,CAAC;;QAEZE,cAAcA,CAAAA,EAAW;UACrB,OAAO,IAAI;;QAEfyG,KAAKA,CACD9J,GAA4B,EAC5BuD,UAAqC,EACrCC,aAA6C,EAC7CxM,MAA6B,EAC7B+S,OAAwB,EACxByD,cAAiD,EACF;UAC/C9J,MAAM,CAAC,CAAC,CAAC8J,cAAc,CAAC;UAExB,IAAIsC,KAAK,GAAGvY,SAAS,CAAC0P,UAAU,CAAC8I,EAAE;UACnC,IAAIvM,aAAa,CAACnB,cAAc,EAAE;YAC9ByN,KAAK,IAAIvY,SAAS,CAAC0P,UAAU,CAAC+I,QAAQ;YACtCxC,cAAc,CAACyC,cAAc,GAAG,IAAI;;UAExCzC,cAAc,CACTtJ,QAAQ,CAAC3M,SAAS,CAAC4M,SAAS,CAACwC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CACzDK,QAAQ,CAAChQ,MAAM,EAAE8Y,KAAK,CAAC;UAE5B,OAAOtC,cAAc;;MAE7B;;MAEA,IAAIjW,SAAS,EAAE;QAEX,MAAM;UAAE4M,SAAS;UAAE8C;SAAY,GAAG1P,SAAS;QAE3C,MAAM2Y,sBAAsB,CAAsC;UAAA7Z;YAAA,KAC7C8Z,cAAc,GAA2B7F,QAAQ,CAAC8F,QAAQ,CAACC,IAAI,CAACC,aAAa;YAAA,KAC7EC,YAAY,GAAG,IAAIlI,yBAAyB,EAAE;YAAA,KAC9CmI,UAAU,GAAG,IAAI1D,uBAAuB,EAAE;YAAA,KAC1C2D,gBAAgB,GAAG,IAAIzC,6BAA6B,EAAE;YAAA,KACtD0C,SAAS,GAAG,IAAI9B,sBAAsB,EAAE;YAAA,KACxC+B,QAAQ,GAAG,IAAIzB,qBAAqB,EAAE;YAAA,KACtC0B,OAAO,GAAG,IAAIf,oBAAoB,EAAE;;YACrD,KACiBrH,WAAW,GAAG,IAAI3L,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAAA,KACnC0L,SAAS,GAAG,IAAIpL,QAAQ,EAAE;YAAA,KAC1B0T,QAAQ,GAAG,IAAIhS,eAAe,EAAE;YAAA,KAChCiS,cAAc,GAAG,IAAIhP,aAAa,EAAE;;YACrD,KACiBiP,uBAAuB,GAAG,IAAI7b,QAAQ,EAAE;;YAEzD,KACQ8b,YAAY,GAAG,KAAK;;YAAE,KACtBC,aAAa,GAAoC,EAAE;;UAEnDC,qBAAqBA,CACzBla,MAA6B,EAC7BwM,aAA4B,EAAE;YAC9B,MAAM2N,YAAqB,GACrBna,MAAM,CAACkS,WAAW,KAAK5L,WAAW,CAAC6L,UAAU,IAC5CnS,MAAM,CAACkS,WAAW,KAAK5L,WAAW,CAAC8T,OAAO;YAEjD,IAAID,YAAY,EAAE;cACd,MAAME,cAAc,GAAG9Z,SAAS,CAACK,yBAAyB,EAA6B;cACvF,IAAIyZ,cAAc,EAAE;gBAChB7N,aAAa,CAACzB,QAAQ,GAAGsP,cAAc;eAC1C,MAAM;gBACH7N,aAAa,CAACzB,QAAQ,GAAGF,eAAe;;aAE/C,MAAM;cACH,IAAI7K,MAAM,CAACC,gBAAgB,EAAE;gBACzBuM,aAAa,CAACzB,QAAQ,GAAG/K,MAAM,CAACC,gBAAoC;eACvE,MAAM;gBACHuM,aAAa,CAACzB,QAAQ,GAAGF,eAAe;;;;UAK5CyP,sBAAsBA,CAAC9N,aAA4B,EAAQ;YAC/D,MAAMT,YAAY,GAAG,IAAI,CAACkO,aAAa;YACvClO,YAAY,CAAC6B,MAAM,GAAG,CAAC;YAEvB,MAAM7C,QAAQ,GAAGyB,aAAa,CAACzB,QAA6B;YAC5D,IAAIA,QAAQ,CAACwP,OAAO,EAAE;cAClB,KAAK,MAAM5N,IAAI,IAAI5B,QAAQ,CAACwP,OAAO,EAAE;gBACjCxO,YAAY,CAAC8C,IAAI,CAAClC,IAAI,CAAC;;cAE3BD,MAAM,CAACX,YAAY,CAAC6B,MAAM,KAAK7C,QAAQ,CAACwP,OAAO,CAAC3M,MAAM,CAAC;;YAG3D7B,YAAY,CAAC8C,IAAI,CAAC,IAAI,CAAC0K,YAAY,CAAC;YAEpC,IAAIxO,QAAQ,CAACpJ,KAAK,CAACZ,OAAO,EAAE;cACxBgL,YAAY,CAAC8C,IAAI,CAAC,IAAI,CAAC2K,UAAU,CAAC;;YAGtCzN,YAAY,CAAC8C,IAAI,CAAC,IAAI,CAAC4K,gBAAgB,CAAC;YAExC,IAAI1O,QAAQ,CAACjI,IAAI,CAAC/B,OAAO,EAAE;cACvBgL,YAAY,CAAC8C,IAAI,CAAC,IAAI,CAAC6K,SAAS,CAAC;;YAGrC,IAAI3O,QAAQ,CAAC9H,GAAG,CAAClC,OAAO,EAAE;cACtBgL,YAAY,CAAC8C,IAAI,CAAC,IAAI,CAAC8K,QAAQ,CAAC;;YAEpC5N,YAAY,CAAC8C,IAAI,CAAC,IAAI,CAAC+K,OAAO,CAAC;;UAG3BY,0BAA0BA,CAC9Bxa,MAA6B,EAC7B6R,eAAgC,EAChCrF,aAA4B,EAC9B;YACE,MAAM+F,MAAM,GAAGvS,MAAM,CAACuS,MAAM;YAC5B,MAAMvH,gBAAyB,GAAGhL,MAAM,CAACkS,WAAW,KAAK5L,WAAW,CAACmU,IAAI,IAAI,CAAC,CAAClI,MAAM,CAACmI,SAAS;;;YAG/FlO,aAAa,CAACxB,gBAAgB,GAAGA,gBAAgB;YACjDwB,aAAa,CAACvB,cAAc,GAAGsH,MAAM,CAACtH,cAAc;;;YAGpDuB,aAAa,CAACtB,SAAS,GAAGqH,MAAM,CAACrH,SAAS;YAC1CsB,aAAa,CAACrB,gBAAgB,GAAGoH,MAAM,CAACpH,gBAAgB;;;YAGxDqB,aAAa,CAACpB,kBAAkB,GAAG,CAACpL,MAAM,CAAC2a,UAAU,GAAIC,MAAM,CAACC,IAAI,CAACC,OAAQ,MAAM,CAAC;YACpFtO,aAAa,CAACnB,cAAc,GAAYL,gBAAgB;YACxDwB,aAAa,CAAClB,eAAe,GAAG,CAAC;;;YAGjCkB,aAAa,CAAC/K,YAAY,GAAG+K,aAAa,CAACzB,QAAQ,CAACtJ,YAAY;YAChE+K,aAAa,CAAChL,kBAAkB,GAAGgL,aAAa,CAACzB,QAAQ,CAACvJ,kBAAkB,IACrEgL,aAAa,CAAC/K,YAAY,KAAK,GAAG;YAEzC+K,aAAa,CAACjB,WAAW,GAAGrK,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAAC2K,MAAM,CAAC9K,KAAK,CAAC,EAAE,CAAC,CAAC;YACjE+E,aAAa,CAAChB,YAAY,GAAGtK,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAAC2K,MAAM,CAAC5K,MAAM,CAAC,EAAE,CAAC,CAAC;YAEnE6E,aAAa,CAAC/E,KAAK,GAAG+E,aAAa,CAAChL,kBAAkB,GAChDN,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAAC4E,aAAa,CAACjB,WAAW,GAAGiB,aAAa,CAAC/K,YAAY,CAAC,EAAE,CAAC,CAAC,GAC/E+K,aAAa,CAACjB,WAAW;YAC/BiB,aAAa,CAAC7E,MAAM,GAAG6E,aAAa,CAAChL,kBAAkB,GACjDN,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAAC4E,aAAa,CAAChB,YAAY,GAAGgB,aAAa,CAAC/K,YAAY,CAAC,EAAE,CAAC,CAAC,GAChF+K,aAAa,CAAChB,YAAY;;;YAGhCgB,aAAa,CAACf,SAAS,GAAGe,aAAa,CAACpB,kBAAkB,IACnDyG,eAAe,CAAC1J,cAAc;YACrCqE,aAAa,CAACd,cAAc,GAAGc,aAAa,CAACf,SAAS,GAChDtH,GAAG,CAAC2B,MAAM,CAACiV,OAAO,GAAG5W,GAAG,CAAC2B,MAAM,CAACqE,KAAK;;;YAG3CqC,aAAa,CAACb,sBAAsB,GAAG,IAAI,CAACoO,uBAAuB;;;YAGnEvN,aAAa,CAACZ,qBAAqB,GAAG,KAAK;;UAGvCoP,mBAAmBA,CACvBhb,MAA6B,EAC7B6R,eAAgC,EAChCrF,aAA4B,EACxB;YACJ,IAAI,CAAC0N,qBAAqB,CAACla,MAAM,EAAEwM,aAAa,CAAC;YAEjD,IAAI,CAAC8N,sBAAsB,CAAC9N,aAAa,CAAC;YAE1CV,qCAAqC,CAAC,IAAI,CAACmO,aAAa,CAAC;YAEzD,IAAI,CAACO,0BAA0B,CAACxa,MAAM,EAAE6R,eAAe,EAAErF,aAAa,CAAC;YAEvE,KAAK,MAAMyO,OAAO,IAAI,IAAI,CAAChB,aAAa,EAAE;cACtC,IAAIgB,OAAO,CAACrJ,YAAY,EAAE;gBACtBqJ,OAAO,CAACrJ,YAAY,CAAC5R,MAAM,EAAE6R,eAAe,EAAErF,aAAa,CAAC;;;;;;;;UAQxE8F,YAAYA,CACRtJ,GAA4B,EAC5BuJ,MAA6B,EAC7BvS,MAA6B,EAC7BuL,WAAmB,EACnBC,YAAoB,EAChB;YACJzC,oBAAoB,CAACC,GAAG,EAAE,IAAI,CAAC6Q,QAAQ,CAAC;YAExC,IAAI,CAACmB,mBAAmB,CAAChb,MAAM,EAAE,IAAI,CAAC6Z,QAAQ,EAAE,IAAI,CAACC,cAAc,CAAC;;;YAGpE,MAAMzc,EAAE,GAAGkV,MAAM,CAACtH,cAAc;YAEhCjC,GAAG,CAACoL,eAAe,CAAC,IAAI,CAAC0F,cAAc,CAAC5O,SAAS,EAC7CpF,MAAM,CAACqE,KAAK,EAAEoB,WAAW,EAAEC,YAAY,EAAE+G,MAAM,EAC/C,IAAI,CAACuH,cAAc,CAAC3O,gBAAgB,CAAC;YAEzC,MAAM1D,KAAK,GAAG,IAAI,CAACqS,cAAc,CAACrS,KAAK;YACvC,MAAME,MAAM,GAAG,IAAI,CAACmS,cAAc,CAACnS,MAAM;YAEzC,IAAI,IAAI,CAACmS,cAAc,CAACtY,kBAAkB,EAAE;cACxCwH,GAAG,CAACsH,eAAe,qBAAqBjT,IAAI,EAAEyI,MAAM,CAAC4E,aAAa,EAAEjD,KAAK,EAAEE,MAAM,CAAC;cAClFqB,GAAG,CAAC6D,eAAe,oBAAoBxP,IAAI,EAAE,IAAI,CAACyc,cAAc,CAACpO,cAAc,EAAEjE,KAAK,EAAEE,MAAM,CAAC;cAC/FqB,GAAG,CAAC6D,eAAe,oBAAoBxP,IAAI,EAAE,IAAI,CAACyc,cAAc,CAACpO,cAAc,EAAEjE,KAAK,EAAEE,MAAM,CAAC;cAC/FqB,GAAG,CAAC6D,eAAe,oBAAoBxP,IAAI,EAAEyI,MAAM,CAACqE,KAAK,EAAE1C,KAAK,EAAEE,MAAM,CAAC;cACzEqB,GAAG,CAAC6D,eAAe,oBAAoBxP,IAAI,EAAEyI,MAAM,CAACqE,KAAK,EAAE1C,KAAK,EAAEE,MAAM,CAAC;aAC5E,MAAM;cACHqB,GAAG,CAACsH,eAAe,eAAejT,IAAI,EAAEyI,MAAM,CAAC4E,aAAa,EAAEjD,KAAK,EAAEE,MAAM,CAAC;cAC5EqB,GAAG,CAAC6D,eAAe,cAAcxP,IAAI,EAAE,IAAI,CAACyc,cAAc,CAACpO,cAAc,EAAEjE,KAAK,EAAEE,MAAM,CAAC;cACzFqB,GAAG,CAAC6D,eAAe,cAAcxP,IAAI,EAAE,IAAI,CAACyc,cAAc,CAACpO,cAAc,EAAEjE,KAAK,EAAEE,MAAM,CAAC;cACzFqB,GAAG,CAAC6D,eAAe,cAAcxP,IAAI,EAAEyI,MAAM,CAACqE,KAAK,EAAE1C,KAAK,EAAEE,MAAM,CAAC;cACnEqB,GAAG,CAAC6D,eAAe,cAAcxP,IAAI,EAAEyI,MAAM,CAACqE,KAAK,EAAE1C,KAAK,EAAEE,MAAM,CAAC;;YAEvEqB,GAAG,CAAC6D,eAAe,aAAaxP,IAAI,EAAEyI,MAAM,CAACqE,KAAK,EAAEoB,WAAW,EAAEC,YAAY,CAAC;YAC9ExC,GAAG,CAAC6D,eAAe,aAAaxP,IAAI,EAAEyI,MAAM,CAACqE,KAAK,EAAEoB,WAAW,EAAEC,YAAY,CAAC;YAE9E,KAAK,MAAMyP,OAAO,IAAI,IAAI,CAAChB,aAAa,EAAE;cACtC,IAAIgB,OAAO,CAAC3I,YAAY,EAAE;gBACtB2I,OAAO,CAAC3I,YAAY,CAACtJ,GAAG,EAAE,IAAI,CAAC6Q,QAAQ,EAAE,IAAI,CAACC,cAAc,EAAEvH,MAAM,EAAEvS,MAAM,EAAEuL,WAAW,EAAEC,YAAY,CAAC;;;;UAIpHsH,KAAKA,CAACoI,OAAgC,EAAElS,GAA4B,EAAQ;;YAExE,IAAI,IAAI,CAACmS,cAAc,CAACnS,GAAG,CAAC,EAAE;cAC1B;;;;YAIJ,KAAK,MAAMhJ,MAAM,IAAIkb,OAAO,EAAE;;cAE1B,IAAI,CAAClb,MAAM,CAACoG,KAAK,IAAI,CAACpG,MAAM,CAACuS,MAAM,EAAE;gBACjC;;;cAGJ,IAAI,CAACyI,mBAAmB,CAAChb,MAAM,EAAE,IAAI,CAAC6Z,QAAQ,EAAE,IAAI,CAACC,cAAc,CAAC;;;;cAIpE,IAAI,CAACX,cAAc,CAACiC,IAAI,CAACC,iBAAiB,CAACC,mBAAmB,EAAEtb,MAAM,CAAC;;;cAGvE,IAAI,IAAI,CAAC8Z,cAAc,CAAC1O,kBAAkB,EAAE;gBACxC,IAAI,CAACmQ,qBAAqB,CAACvS,GAAG,EAAEhJ,MAAM,EAAEA,MAAM,CAACoG,KAAK,EAAE,IAAI,CAAC6T,aAAa,CAAC;eAC5E,MAAM;gBACH,IAAI,CAACuB,oBAAoB,CAACxS,GAAG,EAAEhJ,MAAM,CAAC;;cAG1C,IAAI,CAACmZ,cAAc,CAACiC,IAAI,CAACC,iBAAiB,CAACI,iBAAiB,EAAEzb,MAAM,CAAC;;;;;;UAMrEwb,oBAAoBA,CACxBxS,GAA4B,EAC5BhJ,MAA6B,EACzB;YACJ,MAAMyH,KAAK,GAAGvG,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAAC5H,MAAM,CAACuS,MAAM,CAAC9K,KAAK,CAAC,EAAE,CAAC,CAAC;YAC1D,MAAME,MAAM,GAAGzG,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC0G,KAAK,CAAC5H,MAAM,CAACuS,MAAM,CAAC5K,MAAM,CAAC,EAAE,CAAC,CAAC;YAC5D,MAAMuD,SAAS,GAAG,IAAI,CAAC4O,cAAc,CAAC5O,SAAS;YAC/C,MAAMC,gBAAgB,GAAG,IAAI,CAAC2O,cAAc,CAAC3O,gBAAgB;YAE7D,MAAM4F,QAAQ,GAAG/Q,MAAM,CAAC+Q,QAAQ,CAAC;YACjC,IAAI,CAACQ,SAAS,CAAChK,IAAI,GAAGrG,IAAI,CAAC4T,KAAK,CAAC/D,QAAQ,CAACpG,CAAC,GAAGlD,KAAK,CAAC;YACpD,IAAI,CAAC8J,SAAS,CAAC/J,GAAG,GAAGtG,IAAI,CAAC4T,KAAK,CAAC/D,QAAQ,CAACrC,CAAC,GAAG/G,MAAM,CAAC;;;YAGpD,IAAI,CAAC4J,SAAS,CAAC9J,KAAK,GAAGvG,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC4T,KAAK,CAAC/D,QAAQ,CAACtJ,KAAK,GAAGA,KAAK,CAAC,EAAE,CAAC,CAAC;YACtE,IAAI,CAAC8J,SAAS,CAAC5J,MAAM,GAAGzG,IAAI,CAACG,GAAG,CAACH,IAAI,CAAC4T,KAAK,CAAC/D,QAAQ,CAACpJ,MAAM,GAAGA,MAAM,CAAC,EAAE,CAAC,CAAC;YAEzE,MAAM6M,UAAU,GAAGxU,MAAM,CAACwU,UAAU,CAAC;YACrC,IAAI,CAAChD,WAAW,CAAC7G,CAAC,GAAG6J,UAAU,CAAC7J,CAAC;YACjC,IAAI,CAAC6G,WAAW,CAAC9C,CAAC,GAAG8F,UAAU,CAAC9F,CAAC;YACjC,IAAI,CAAC8C,WAAW,CAAC7C,CAAC,GAAG6F,UAAU,CAAC7F,CAAC;YACjC,IAAI,CAAC6C,WAAW,CAACzK,CAAC,GAAGyN,UAAU,CAACzN,CAAC;YAEjC,MAAM4F,IAAI,GAAG3D,GAAG,CAAC4D,aAAa,CAACnF,KAAK,EAAEE,MAAM,EAAE,SAAS,CAAC;;;YAGxD,IAAIlB,qBAAqB,CAACzG,MAAM,CAAC,EAAE;cAC/B2M,IAAI,CAACE,eAAe,CAAC3B,SAAS,EAAElF,MAAM,CAAC8G,KAAK,EAAE7G,OAAO,CAAC8G,KAAK,EAAE,IAAI,CAACyE,WAAW,CAAC;aACjF,MAAM;cACH7E,IAAI,CAACE,eAAe,CAAC3B,SAAS,EAAElF,MAAM,CAACmL,IAAI,EAAElL,OAAO,CAAC8G,KAAK,CAAC;;;;YAI/D,IAAI/M,MAAM,CAAC0G,SAAS,GAAGd,YAAY,CAAC8E,aAAa,EAAE;cAC/CiC,IAAI,CAAC2D,eAAe,CAChBnF,gBAAgB,EAChBnF,MAAM,CAAC8G,KAAK,EACZ7G,OAAO,CAACsK,OAAO,EACfvQ,MAAM,CAAC0U,UAAU,EACjB1U,MAAM,CAAC2U,YAAY,EACnB3U,MAAM,CAAC0G,SAAS,GAAGd,YAAY,CAAC8E,aACpC,CAAC;aACJ,MAAM;cACHiC,IAAI,CAAC2D,eAAe,CAACnF,gBAAgB,EAAEnF,MAAM,CAACmL,IAAI,EAAElL,OAAO,CAACsK,OAAO,CAAC;;YAGxE5D,IAAI,CAACuE,WAAW,CAAC,IAAI,CAACK,SAAS,CAAC;;;YAGhC5E,IAAI,CAACO,QAAQ,CAACC,SAAS,CAACC,MAAM,CAAC,CAC1B4C,QAAQ,CAAChQ,MAAM,EAAEiQ,UAAU,CAAC7C,MAAM,CAAC;;;YAGxC,IAAI0L,KAAK,GAAG7I,UAAU,CAACN,KAAK,GAAGM,UAAU,CAAC8I,EAAE;YAC5C,IAAI,IAAI,CAACe,cAAc,CAACzO,cAAc,EAAE;cACpCyN,KAAK,IAAI7I,UAAU,CAAC+I,QAAQ;cAC5BrM,IAAI,CAACsM,cAAc,GAAG,IAAI;;YAE9BtM,IAAI,CAACO,QAAQ,CAACC,SAAS,CAACwC,KAAK,CAAC,CACzBK,QAAQ,CAAChQ,MAAM,EAAE8Y,KAAK,CAAC;;UAGxByC,qBAAqBA,CACzBvS,GAA4B,EAC5BhJ,MAA6B,EAC7BoG,KAA2B,EAC3B2F,YAA6C,EACzC;YACJK,qCAAqC,CAACL,YAAY,CAAC;YAEnD,MAAMgH,OAAwB,GAAG;cAC7B7H,SAAS,EAAE,EAAE;cACbC,gBAAgB,EAAE;aACrB;YAED,IAAI8M,QAAsD,GAAGzX,SAAS;YAEtE,KAAK,MAAMya,OAAO,IAAIlP,YAAY,EAAE;cAChC,IAAIkP,OAAO,CAACnI,KAAK,EAAE;gBACfmF,QAAQ,GAAGgD,OAAO,CAACnI,KAAK,CAAC9J,GAAG,EAAE,IAAI,CAAC6Q,QAAQ,EAAE,IAAI,CAACC,cAAc,EAC5D9Z,MAAM,EAAE+S,OAAO,EAAEkF,QAAQ,CAAC;;;YAItCvL,MAAM,CAAC,IAAI,CAACoN,cAAc,CAACxO,eAAe,KAAK,CAAC,CAAC;;UAG7C6P,cAAcA,CAACnS,GAA4B,EAAU;YACzD,IAAI,IAAI,CAACgR,YAAY,EAAE;cACnB,OAAO,CAAC;;YAGZjR,oBAAoB,CAACC,GAAG,EAAE,IAAI,CAAC6Q,QAAQ,CAAC;;;YAGxC,IAAI,CAACE,uBAAuB,CAAC2B,KAAK,2CAA2C;YAC7E,IAAI,CAAC3B,uBAAuB,CAAC4B,UAAU,CAAC;cAAEC,UAAU,EAAE;aAAsC,CAAC;YAE7F,IAAI,IAAI,CAAC7B,uBAAuB,CAAC8B,WAAW,EAAE;cAC1C,IAAI,CAAC7B,YAAY,GAAG,IAAI;;YAG5B,OAAO,IAAI,CAACA,YAAY,GAAG,CAAC,GAAG,CAAC;;;QAIxCzZ,SAAS,CAACub,iBAAiB,CAAC,SAAS,EAAE,IAAI5C,sBAAsB,EAAE,CAAC;MAExE,CAAC;cAAC,CAAAlV,GAAA,CAAAC,GAAA", "file": "all.js", "sourcesContent": ["/*\n Copyright (c) 2021-2024 Xiamen Yaji Software Co., Ltd.\n\n https://www.cocos.com/\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights to\n use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\n of the Software, and to permit persons to whom the Software is furnished to do so,\n subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n*/\n\nimport {\n    _decorator, Camera, CCBoolean, CCFloat, CCInteger, Component,\n    Material, rendering, Texture2D,\n} from 'cc';\n\nimport { EDITOR } from 'cc/env';\n\nimport {\n    fillRequiredPipelineSettings, makePipelineSettings, PipelineSettings,\n} from './builtin-pipeline-types';\n\nconst { ccclass, disallowMultiple, executeInEditMode, menu, property, requireComponent, type } = _decorator;\n\n@ccclass('BuiltinPipelineSettings')\n@menu('Rendering/BuiltinPipelineSettings')\n@requireComponent(Camera)\n@disallowMultiple\n@executeInEditMode\nexport class BuiltinPipelineSettings extends Component {\n    @property\n    private readonly _settings: PipelineSettings = makePipelineSettings();\n\n    getPipelineSettings(): PipelineSettings {\n        return this._settings;\n    }\n\n    // Enable/Disable\n    onEnable(): void {\n        fillRequiredPipelineSettings(this._settings);\n        const cameraComponent = this.getComponent(Camera)!;\n        const camera = cameraComponent.camera;\n        camera.pipelineSettings = this._settings;\n\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    onDisable(): void {\n        const cameraComponent = this.getComponent(Camera)!;\n        const camera = cameraComponent.camera;\n        camera.pipelineSettings = null;\n\n        if (EDITOR) {\n            this._disableEditorPreview();\n        }\n    }\n\n    // Editor Preview\n    @property(CCBoolean)\n    protected _editorPreview = false;\n\n    @property({\n        displayName: 'Editor Preview (Experimental)',\n        type: CCBoolean,\n    })\n    get editorPreview(): boolean {\n        return this._editorPreview;\n    }\n    set editorPreview(v: boolean) {\n        this._editorPreview = v;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    public _tryEnableEditorPreview(): void {\n        if (rendering === undefined) {\n            return;\n        }\n        if (this._editorPreview) {\n            rendering.setEditorPipelineSettings(this._settings);\n        } else {\n            this._disableEditorPreview();\n        }\n    }\n    public _disableEditorPreview(): void {\n        if (rendering === undefined) {\n            return;\n        }\n        const current = rendering.getEditorPipelineSettings() as PipelineSettings | null;\n        if (current === this._settings) {\n            rendering.setEditorPipelineSettings(null);\n        }\n    }\n\n    // MSAA\n    @property({\n        group: { id: 'MSAA', name: 'Multisample Anti-Aliasing' },\n        type: CCBoolean,\n    })\n    get MsaaEnable(): boolean {\n        return this._settings.msaa.enabled;\n    }\n    set MsaaEnable(value: boolean) {\n        this._settings.msaa.enabled = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n\n    @property({\n        group: { id: 'MSAA', name: 'Multisample Anti-Aliasing', style: 'section' },\n        type: CCInteger,\n        range: [2, 4, 2],\n    })\n    set msaaSampleCount(value: number) {\n        value = 2 ** Math.ceil(Math.log2(Math.max(value, 2)));\n        value = Math.min(value, 4);\n        this._settings.msaa.sampleCount = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get msaaSampleCount(): number {\n        return this._settings.msaa.sampleCount;\n    }\n\n    // Shading Scale\n    @property({\n        group: { id: 'ShadingScale', name: 'ShadingScale', style: 'section' },\n        type: CCBoolean,\n    })\n    set shadingScaleEnable(value: boolean) {\n        this._settings.enableShadingScale = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get shadingScaleEnable(): boolean {\n        return this._settings.enableShadingScale;\n    }\n\n    @property({\n        tooltip: 'i18n:postprocess.shadingScale',\n        group: { id: 'ShadingScale', name: 'ShadingScale' },\n        type: CCFloat,\n        range: [0.01, 4, 0.01],\n        slide: true,\n    })\n    set shadingScale(value: number) {\n        this._settings.shadingScale = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get shadingScale(): number {\n        return this._settings.shadingScale;\n    }\n\n    // Bloom\n    @property({\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\n        type: CCBoolean,\n    })\n    set bloomEnable(value: boolean) {\n        this._settings.bloom.enabled = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get bloomEnable(): boolean {\n        return this._settings.bloom.enabled;\n    }\n\n    @property({\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\n        type: Material,\n    })\n    set bloomMaterial(value: Material) {\n        if (this._settings.bloom.material === value) {\n            return;\n        }\n        this._settings.bloom.material = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get bloomMaterial(): Material {\n        return this._settings.bloom.material!;\n    }\n\n    @property({\n        tooltip: 'i18n:bloom.enableAlphaMask',\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\n        type: CCBoolean,\n    })\n    set bloomEnableAlphaMask(value: boolean) {\n        this._settings.bloom.enableAlphaMask = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get bloomEnableAlphaMask(): boolean {\n        return this._settings.bloom.enableAlphaMask;\n    }\n\n    @property({\n        tooltip: 'i18n:bloom.iterations',\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\n        type: CCInteger,\n        range: [1, 6, 1],\n        slide: true,\n    })\n    set bloomIterations(value: number) {\n        this._settings.bloom.iterations = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get bloomIterations(): number {\n        return this._settings.bloom.iterations;\n    }\n\n    @property({\n        tooltip: 'i18n:bloom.threshold',\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\n        type: CCFloat,\n        min: 0,\n    })\n    set bloomThreshold(value: number) {\n        this._settings.bloom.threshold = value;\n    }\n    get bloomThreshold(): number {\n        return this._settings.bloom.threshold;\n    }\n\n    set bloomIntensity(value: number) {\n        this._settings.bloom.intensity = value;\n    }\n    get bloomIntensity(): number {\n        return this._settings.bloom.intensity;\n    }\n\n    // Color Grading (LDR)\n    @property({\n        group: { id: 'Color Grading', name: 'ColorGrading (LDR) (PostProcessing)', style: 'section' },\n        type: CCBoolean,\n    })\n    set colorGradingEnable(value: boolean) {\n        this._settings.colorGrading.enabled = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get colorGradingEnable(): boolean {\n        return this._settings.colorGrading.enabled;\n    }\n\n    @property({\n        group: { id: 'Color Grading', name: 'ColorGrading (LDR) (PostProcessing)', style: 'section' },\n        type: Material,\n    })\n    set colorGradingMaterial(value: Material) {\n        if (this._settings.colorGrading.material === value) {\n            return;\n        }\n        this._settings.colorGrading.material = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get colorGradingMaterial(): Material {\n        return this._settings.colorGrading.material!;\n    }\n\n    @property({\n        tooltip: 'i18n:color_grading.contribute',\n        group: { id: 'Color Grading', name: 'ColorGrading (LDR) (PostProcessing)', style: 'section' },\n        type: CCFloat,\n        range: [0, 1, 0.01],\n        slide: true,\n    })\n    set colorGradingContribute(value: number) {\n        this._settings.colorGrading.contribute = value;\n    }\n    get colorGradingContribute(): number {\n        return this._settings.colorGrading.contribute;\n    }\n\n    @property({\n        tooltip: 'i18n:color_grading.originalMap',\n        group: { id: 'Color Grading', name: 'ColorGrading (LDR) (PostProcessing)', style: 'section' },\n        type: Texture2D,\n    })\n    set colorGradingMap(val: Texture2D) {\n        this._settings.colorGrading.colorGradingMap = val;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get colorGradingMap(): Texture2D {\n        return this._settings.colorGrading.colorGradingMap!;\n    }\n\n    // FXAA\n    @property({\n        group: { id: 'FXAA', name: 'Fast Approximate Anti-Aliasing (PostProcessing)', style: 'section' },\n        type: CCBoolean,\n    })\n    set fxaaEnable(value: boolean) {\n        this._settings.fxaa.enabled = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get fxaaEnable(): boolean {\n        return this._settings.fxaa.enabled;\n    }\n\n    @property({\n        group: { id: 'FXAA', name: 'Fast Approximate Anti-Aliasing (PostProcessing)', style: 'section' },\n        type: Material,\n    })\n    set fxaaMaterial(value: Material) {\n        if (this._settings.fxaa.material === value) {\n            return;\n        }\n        this._settings.fxaa.material = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get fxaaMaterial(): Material {\n        return this._settings.fxaa.material!;\n    }\n\n    // FSR\n    @property({\n        group: { id: 'FSR', name: 'FidelityFX Super Resolution', style: 'section' },\n        type: CCBoolean,\n    })\n    set fsrEnable(value: boolean) {\n        this._settings.fsr.enabled = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get fsrEnable(): boolean {\n        return this._settings.fsr.enabled;\n    }\n\n    @property({\n        group: { id: 'FSR', name: 'FidelityFX Super Resolution', style: 'section' },\n        type: Material,\n    })\n    set fsrMaterial(value: Material) {\n        if (this._settings.fsr.material === value) {\n            return;\n        }\n        this._settings.fsr.material = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get fsrMaterial(): Material {\n        return this._settings.fsr.material!;\n    }\n\n    @property({\n        group: { id: 'FSR', name: 'FidelityFX Super Resolution', style: 'section' },\n        type: CCFloat,\n        range: [0, 1, 0.01],\n        slide: true,\n    })\n    set fsrSharpness(value: number) {\n        this._settings.fsr.sharpness = value;\n    }\n    get fsrSharpness(): number {\n        return this._settings.fsr.sharpness;\n    }\n\n    @property({\n        group: { id: 'ToneMapping', name: 'ToneMapping', style: 'section' },\n        type: Material,\n    })\n    set toneMappingMaterial(value: Material) {\n        if (this._settings.toneMapping.material === value) {\n            return;\n        }\n        this._settings.toneMapping.material = value;\n        if (EDITOR) {\n            this._tryEnableEditorPreview();\n        }\n    }\n    get toneMappingMaterial(): Material {\n        return this._settings.toneMapping.material!;\n    }\n}\n", "/*\n Copyright (c) 2021-2024 Xiamen Yaji Software Co., Ltd.\n\n https://www.cocos.com\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights to\n use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\n of the Software, and to permit persons to whom the Software is furnished to do so,\n subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n*/\n\n/**\n * ========================= !DO NOT CHANGE THE FOLLOWING SECTION MANUALLY! =========================\n * The following section is auto-generated.\n * ========================= !DO NOT CHANGE THE FOLLOWING SECTION MANUALLY! =========================\n */\n/* eslint-disable max-len */\nimport { Material, Texture2D, gfx } from 'cc';\n\nconst { SampleCount } = gfx;\n\nexport interface MSAA {\n    enabled: boolean; /* false */\n    sampleCount: gfx.SampleCount; /* SampleCount.X4 */\n    [name: string]: unknown;\n}\n\nexport function makeMSAA(): MSAA {\n    return {\n        enabled: false,\n        sampleCount: SampleCount.X4,\n    };\n}\n\nexport function fillRequiredMSAA(value: MSAA): void {\n    if (value.enabled === undefined) {\n        value.enabled = false;\n    }\n    if (value.sampleCount === undefined) {\n        value.sampleCount = SampleCount.X4;\n    }\n}\n\nexport interface HBAO {\n    enabled: boolean; /* false */\n    radiusScale: number; /* 1 */\n    angleBiasDegree: number; /* 10 */\n    blurSharpness: number; /* 3 */\n    aoSaturation: number; /* 1 */\n    needBlur: boolean; /* false */\n    [name: string]: unknown;\n}\n\nexport function makeHBAO(): HBAO {\n    return {\n        enabled: false,\n        radiusScale: 1,\n        angleBiasDegree: 10,\n        blurSharpness: 3,\n        aoSaturation: 1,\n        needBlur: false,\n    };\n}\n\nexport function fillRequiredHBAO(value: HBAO): void {\n    if (value.enabled === undefined) {\n        value.enabled = false;\n    }\n    if (value.radiusScale === undefined) {\n        value.radiusScale = 1;\n    }\n    if (value.angleBiasDegree === undefined) {\n        value.angleBiasDegree = 10;\n    }\n    if (value.blurSharpness === undefined) {\n        value.blurSharpness = 3;\n    }\n    if (value.aoSaturation === undefined) {\n        value.aoSaturation = 1;\n    }\n    if (value.needBlur === undefined) {\n        value.needBlur = false;\n    }\n}\n\nexport interface Bloom {\n    enabled: boolean; /* false */\n    /* refcount */ material: Material | null;\n    enableAlphaMask: boolean; /* false */\n    iterations: number; /* 3 */\n    threshold: number; /* 0.8 */\n    intensity: number; /* 2.3 */\n    [name: string]: unknown;\n}\n\nexport function makeBloom(): Bloom {\n    return {\n        enabled: false,\n        material: null,\n        enableAlphaMask: false,\n        iterations: 3,\n        threshold: 0.8,\n        intensity: 2.3,\n    };\n}\n\nexport function fillRequiredBloom(value: Bloom): void {\n    if (value.enabled === undefined) {\n        value.enabled = false;\n    }\n    if (value.material === undefined) {\n        value.material = null;\n    }\n    if (value.enableAlphaMask === undefined) {\n        value.enableAlphaMask = false;\n    }\n    if (value.iterations === undefined) {\n        value.iterations = 3;\n    }\n    if (value.threshold === undefined) {\n        value.threshold = 0.8;\n    }\n    if (value.intensity === undefined) {\n        value.intensity = 2.3;\n    }\n}\n\nexport interface ColorGrading {\n    enabled: boolean; /* false */\n    /* refcount */ material: Material | null;\n    contribute: number; /* 1 */\n    /* refcount */ colorGradingMap: Texture2D | null;\n    [name: string]: unknown;\n}\n\nexport function makeColorGrading(): ColorGrading {\n    return {\n        enabled: false,\n        material: null,\n        contribute: 1,\n        colorGradingMap: null,\n    };\n}\n\nexport function fillRequiredColorGrading(value: ColorGrading): void {\n    if (value.enabled === undefined) {\n        value.enabled = false;\n    }\n    if (value.material === undefined) {\n        value.material = null;\n    }\n    if (value.contribute === undefined) {\n        value.contribute = 1;\n    }\n    if (value.colorGradingMap === undefined) {\n        value.colorGradingMap = null;\n    }\n}\n\nexport interface FSR {\n    enabled: boolean; /* false */\n    /* refcount */ material: Material | null;\n    sharpness: number; /* 0.8 */\n    [name: string]: unknown;\n}\n\nexport function makeFSR(): FSR {\n    return {\n        enabled: false,\n        material: null,\n        sharpness: 0.8,\n    };\n}\n\nexport function fillRequiredFSR(value: FSR): void {\n    if (value.enabled === undefined) {\n        value.enabled = false;\n    }\n    if (value.material === undefined) {\n        value.material = null;\n    }\n    if (value.sharpness === undefined) {\n        value.sharpness = 0.8;\n    }\n}\n\nexport interface FXAA {\n    enabled: boolean; /* false */\n    /* refcount */ material: Material | null;\n    [name: string]: unknown;\n}\n\nexport function makeFXAA(): FXAA {\n    return {\n        enabled: false,\n        material: null,\n    };\n}\n\nexport function fillRequiredFXAA(value: FXAA): void {\n    if (value.enabled === undefined) {\n        value.enabled = false;\n    }\n    if (value.material === undefined) {\n        value.material = null;\n    }\n}\n\nexport interface ToneMapping {\n    /* refcount */ material: Material | null;\n    [name: string]: unknown;\n}\n\nexport function makeToneMapping(): ToneMapping {\n    return {\n        material: null,\n    };\n}\n\nexport function fillRequiredToneMapping(value: ToneMapping): void {\n    if (value.material === undefined) {\n        value.material = null;\n    }\n}\n\nexport interface PipelineSettings {\n    readonly msaa: MSAA;\n    enableShadingScale: boolean; /* false */\n    shadingScale: number; /* 0.5 */\n    readonly bloom: Bloom;\n    readonly toneMapping: ToneMapping;\n    readonly colorGrading: ColorGrading;\n    readonly fsr: FSR;\n    readonly fxaa: FXAA;\n    [name: string]: unknown;\n}\n\nexport function makePipelineSettings(): PipelineSettings {\n    return {\n        msaa: makeMSAA(),\n        enableShadingScale: false,\n        shadingScale: 0.5,\n        bloom: makeBloom(),\n        toneMapping: makeToneMapping(),\n        colorGrading: makeColorGrading(),\n        fsr: makeFSR(),\n        fxaa: makeFXAA(),\n    };\n}\n\nexport function fillRequiredPipelineSettings(value: PipelineSettings): void {\n    if (!value.msaa) {\n        (value.msaa as MSAA) = makeMSAA();\n    } else {\n        fillRequiredMSAA(value.msaa);\n    }\n    if (value.enableShadingScale === undefined) {\n        value.enableShadingScale = false;\n    }\n    if (value.shadingScale === undefined) {\n        value.shadingScale = 0.5;\n    }\n    if (!value.bloom) {\n        (value.bloom as Bloom) = makeBloom();\n    } else {\n        fillRequiredBloom(value.bloom);\n    }\n    if (!value.toneMapping) {\n        (value.toneMapping as ToneMapping) = makeToneMapping();\n    } else {\n        fillRequiredToneMapping(value.toneMapping);\n    }\n    if (!value.colorGrading) {\n        (value.colorGrading as ColorGrading) = makeColorGrading();\n    } else {\n        fillRequiredColorGrading(value.colorGrading);\n    }\n    if (!value.fsr) {\n        (value.fsr as FSR) = makeFSR();\n    } else {\n        fillRequiredFSR(value.fsr);\n    }\n    if (!value.fxaa) {\n        (value.fxaa as FXAA) = makeFXAA();\n    } else {\n        fillRequiredFXAA(value.fxaa);\n    }\n}\n", "/*\n Copyright (c) 2021-2024 Xiamen Yaji Software Co., Ltd.\n\n https://www.cocos.com/\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction, including without limitation the rights to\n use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\n of the Software, and to permit persons to whom the Software is furnished to do so,\n subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n THE SOFTWARE.\n*/\n\nimport {\n    assert, cclegacy, clamp, geometry, gfx, Layers, Material, pipeline,\n    PipelineEventProcessor, PipelineEventType, ReflectionProbeManager, renderer,\n    rendering, sys, Vec2, Vec3, Vec4, warn,\n} from 'cc';\n\nimport { DEBUG, EDITOR } from 'cc/env';\n\nimport {\n    makePipelineSettings,\n    PipelineSettings,\n} from './builtin-pipeline-types';\n\nconst { AABB, Sphere, intersect } = geometry;\nconst { ClearFlagBit, Color, Format, FormatFeatureBit, LoadOp, StoreOp, TextureType, Viewport } = gfx;\nconst { scene } = renderer;\nconst { CameraUsage, CSMLevel, LightType } = scene;\n\nfunction forwardNeedClearColor(camera: renderer.scene.Camera): boolean {\n    return !!(camera.clearFlag & (ClearFlagBit.COLOR | (ClearFlagBit.STENCIL << 1)));\n}\n\nfunction getCsmMainLightViewport(\n    light: renderer.scene.DirectionalLight,\n    w: number,\n    h: number,\n    level: number,\n    vp: gfx.Viewport,\n    screenSpaceSignY: number,\n): void {\n    if (light.shadowFixedArea || light.csmLevel === CSMLevel.LEVEL_1) {\n        vp.left = 0;\n        vp.top = 0;\n        vp.width = Math.trunc(w);\n        vp.height = Math.trunc(h);\n    } else {\n        vp.left = Math.trunc(level % 2 * 0.5 * w);\n        if (screenSpaceSignY > 0) {\n            vp.top = Math.trunc((1 - Math.floor(level / 2)) * 0.5 * h);\n        } else {\n            vp.top = Math.trunc(Math.floor(level / 2) * 0.5 * h);\n        }\n        vp.width = Math.trunc(0.5 * w);\n        vp.height = Math.trunc(0.5 * h);\n    }\n    vp.left = Math.max(0, vp.left);\n    vp.top = Math.max(0, vp.top);\n    vp.width = Math.max(1, vp.width);\n    vp.height = Math.max(1, vp.height);\n}\n\nexport class PipelineConfigs {\n    isWeb = false;\n    isWebGL1 = false;\n    isWebGPU = false;\n    isMobile = false;\n    isHDR = false;\n    useFloatOutput = false;\n    toneMappingType = 0; // 0: ACES, 1: None\n    shadowEnabled = false;\n    shadowMapFormat = Format.R32F;\n    shadowMapSize = new Vec2(1, 1);\n    usePlanarShadow = false;\n    screenSpaceSignY = 1;\n    supportDepthSample = false;\n    mobileMaxSpotLightShadowMaps = 1;\n\n    platform = new Vec4(0, 0, 0, 0);\n}\n\nfunction setupPipelineConfigs(\n    ppl: rendering.BasicPipeline,\n    configs: PipelineConfigs,\n): void {\n    const sampleFeature = FormatFeatureBit.SAMPLED_TEXTURE | FormatFeatureBit.LINEAR_FILTER;\n    const device = ppl.device;\n    // Platform\n    configs.isWeb = !sys.isNative;\n    configs.isWebGL1 = device.gfxAPI === gfx.API.WEBGL;\n    configs.isWebGPU = device.gfxAPI === gfx.API.WEBGPU;\n    configs.isMobile = sys.isMobile;\n\n    // Rendering\n    configs.isHDR = ppl.pipelineSceneData.isHDR; // Has tone mapping\n    configs.useFloatOutput = ppl.getMacroBool('CC_USE_FLOAT_OUTPUT');\n    configs.toneMappingType = ppl.pipelineSceneData.postSettings.toneMappingType;\n    // Shadow\n    const shadowInfo = ppl.pipelineSceneData.shadows;\n    configs.shadowEnabled = shadowInfo.enabled;\n    configs.shadowMapFormat = pipeline.supportsR32FloatTexture(ppl.device) ? Format.R32F : Format.RGBA8;\n    configs.shadowMapSize.set(shadowInfo.size);\n    configs.usePlanarShadow = shadowInfo.enabled && shadowInfo.type === renderer.scene.ShadowType.Planar;\n    // Device\n    configs.screenSpaceSignY = ppl.device.capabilities.screenSpaceSignY;\n    configs.supportDepthSample = (ppl.device.getFormatFeatures(Format.DEPTH_STENCIL) & sampleFeature) === sampleFeature;\n    // Constants\n    const screenSpaceSignY = device.capabilities.screenSpaceSignY;\n    configs.platform.x = configs.isMobile ? 1.0 : 0.0;\n    configs.platform.w = (screenSpaceSignY * 0.5 + 0.5) << 1 | (device.capabilities.clipSpaceSignY * 0.5 + 0.5);\n}\n\nexport interface PipelineSettings2 extends PipelineSettings {\n    _passes?: rendering.PipelinePassBuilder[];\n}\n\nconst defaultSettings = makePipelineSettings();\n\nexport class CameraConfigs {\n    settings: PipelineSettings = defaultSettings;\n    // Window\n    isMainGameWindow = false;\n    renderWindowId = 0;\n    // Camera\n    colorName = '';\n    depthStencilName = '';\n    // Pipeline\n    enableFullPipeline = false;\n    enableProfiler = false;\n    remainingPasses = 0;\n    // Shading Scale\n    enableShadingScale = false;\n    shadingScale = 1.0;\n    nativeWidth = 1;\n    nativeHeight = 1;\n    width = 1; // Scaled width\n    height = 1; // Scaled height\n    // Radiance\n    enableHDR = false;\n    radianceFormat = gfx.Format.RGBA8;\n    // Tone Mapping\n    copyAndTonemapMaterial: Material | null = null;\n    // Depth\n    /** @en mutable */\n    enableStoreSceneDepth = false;\n}\n\nconst sClearColorTransparentBlack = new Color(0, 0, 0, 0);\n\nfunction sortPipelinePassBuildersByConfigOrder(passBuilders: rendering.PipelinePassBuilder[]): void {\n    passBuilders.sort((a, b) => {\n        return a.getConfigOrder() - b.getConfigOrder();\n    });\n}\n\nfunction sortPipelinePassBuildersByRenderOrder(passBuilders: rendering.PipelinePassBuilder[]): void {\n    passBuilders.sort((a, b) => {\n        return a.getRenderOrder() - b.getRenderOrder();\n    });\n}\n\nfunction addCopyToScreenPass(\n    ppl: rendering.BasicPipeline,\n    pplConfigs: Readonly<PipelineConfigs>,\n    cameraConfigs: CameraConfigs,\n    input: string,\n): rendering.BasicRenderPassBuilder {\n    assert(!!cameraConfigs.copyAndTonemapMaterial);\n    const pass = ppl.addRenderPass(\n        cameraConfigs.nativeWidth,\n        cameraConfigs.nativeHeight,\n        'cc-tone-mapping');\n    pass.addRenderTarget(\n        cameraConfigs.colorName,\n        LoadOp.CLEAR, StoreOp.STORE,\n        sClearColorTransparentBlack);\n    pass.addTexture(input, 'inputTexture');\n    pass.setVec4('g_platform', pplConfigs.platform);\n    pass.addQueue(rendering.QueueHint.OPAQUE)\n        .addFullscreenQuad(cameraConfigs.copyAndTonemapMaterial, 1);\n    return pass;\n}\n\nexport function getPingPongRenderTarget(prevName: string, prefix: string, id: number): string {\n    if (prevName.startsWith(prefix)) {\n        return `${prefix}${1 - Number(prevName.charAt(prefix.length))}_${id}`;\n    } else {\n        return `${prefix}0_${id}`;\n    }\n}\n\nexport interface PipelineContext {\n    colorName: string;\n    depthStencilName: string;\n}\n\nclass ForwardLighting {\n    // Active lights\n    private readonly lights: renderer.scene.Light[] = [];\n    // Active spot lights with shadows (Mutually exclusive with `lights`)\n    private readonly shadowEnabledSpotLights: renderer.scene.SpotLight[] = [];\n\n    // Internal cached resources\n    private readonly _sphere = Sphere.create(0, 0, 0, 1);\n    private readonly _boundingBox = new AABB();\n    private readonly _rangedDirLightBoundingBox = new AABB(0.0, 0.0, 0.0, 0.5, 0.5, 0.5);\n\n    // ----------------------------------------------------------------\n    // Interface\n    // ----------------------------------------------------------------\n    public cullLights(scene: renderer.RenderScene, frustum: geometry.Frustum, cameraPos?: Vec3): void {\n        // TODO(zhouzhenglong): Make light culling native\n        this.lights.length = 0;\n        this.shadowEnabledSpotLights.length = 0;\n        // spot lights\n        for (const light of scene.spotLights) {\n            if (light.baked) {\n                continue;\n            }\n            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);\n            if (intersect.sphereFrustum(this._sphere, frustum)) {\n                if (light.shadowEnabled) {\n                    this.shadowEnabledSpotLights.push(light);\n                } else {\n                    this.lights.push(light);\n                }\n            }\n        }\n        // sphere lights\n        for (const light of scene.sphereLights) {\n            if (light.baked) {\n                continue;\n            }\n            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);\n            if (intersect.sphereFrustum(this._sphere, frustum)) {\n                this.lights.push(light);\n            }\n        }\n        // point lights\n        for (const light of scene.pointLights) {\n            if (light.baked) {\n                continue;\n            }\n            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);\n            if (intersect.sphereFrustum(this._sphere, frustum)) {\n                this.lights.push(light);\n            }\n        }\n        // ranged dir lights\n        for (const light of scene.rangedDirLights) {\n            AABB.transform(this._boundingBox, this._rangedDirLightBoundingBox, light.node!.getWorldMatrix());\n            if (intersect.aabbFrustum(this._boundingBox, frustum)) {\n                this.lights.push(light);\n            }\n        }\n\n        if (cameraPos) {\n            this.shadowEnabledSpotLights.sort(\n                (lhs, rhs) => Vec3.squaredDistance(cameraPos, lhs.position) - Vec3.squaredDistance(cameraPos, rhs.position),\n            );\n        }\n    }\n    private _addLightQueues(camera: renderer.scene.Camera, pass: rendering.BasicRenderPassBuilder): void {\n        for (const light of this.lights) {\n            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');\n            switch (light.type) {\n                case LightType.SPHERE:\n                    queue.name = 'sphere-light';\n                    break;\n                case LightType.SPOT:\n                    queue.name = 'spot-light';\n                    break;\n                case LightType.POINT:\n                    queue.name = 'point-light';\n                    break;\n                case LightType.RANGED_DIRECTIONAL:\n                    queue.name = 'ranged-directional-light';\n                    break;\n                default:\n                    queue.name = 'unknown-light';\n            }\n            queue.addScene(\n                camera,\n                rendering.SceneFlags.BLEND,\n                light,\n            );\n        }\n    }\n    public addSpotlightShadowPasses(\n        ppl: rendering.BasicPipeline,\n        camera: renderer.scene.Camera,\n        maxNumShadowMaps: number,\n    ): void {\n        let i = 0;\n        for (const light of this.shadowEnabledSpotLights) {\n            const shadowMapSize = ppl.pipelineSceneData.shadows.size;\n            const shadowPass = ppl.addRenderPass(shadowMapSize.x, shadowMapSize.y, 'default');\n            shadowPass.name = `SpotLightShadowPass${i}`;\n            shadowPass.addRenderTarget(`SpotShadowMap${i}`, LoadOp.CLEAR, StoreOp.STORE, new Color(1, 1, 1, 1));\n            shadowPass.addDepthStencil(`SpotShadowDepth${i}`, LoadOp.CLEAR, StoreOp.DISCARD);\n            shadowPass.addQueue(rendering.QueueHint.NONE, 'shadow-caster')\n                .addScene(camera, rendering.SceneFlags.OPAQUE | rendering.SceneFlags.MASK | rendering.SceneFlags.SHADOW_CASTER)\n                .useLightFrustum(light);\n            ++i;\n            if (i >= maxNumShadowMaps) {\n                break;\n            }\n        }\n    }\n    public addLightQueues(pass: rendering.BasicRenderPassBuilder,\n        camera: renderer.scene.Camera, maxNumShadowMaps: number): void {\n        this._addLightQueues(camera, pass);\n        let i = 0;\n        for (const light of this.shadowEnabledSpotLights) {\n            // Add spot-light pass\n            // Save last RenderPass to the `pass` variable\n            // TODO(zhouzhenglong): Fix per queue addTexture\n            pass.addTexture(`SpotShadowMap${i}`, 'cc_spotShadowMap');\n            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');\n            queue.addScene(camera, rendering.SceneFlags.BLEND, light);\n            ++i;\n            if (i >= maxNumShadowMaps) {\n                break;\n            }\n        }\n    }\n\n    // Notice: ForwardLighting cannot handle a lot of lights.\n    // If there are too many lights, the performance will be very poor.\n    // If many lights are needed, please implement a forward+ or deferred rendering pipeline.\n    public addLightPasses(\n        colorName: string,\n        depthStencilName: string,\n        depthStencilStoreOp: gfx.StoreOp,\n        id: number, // window id\n        width: number,\n        height: number,\n        camera: renderer.scene.Camera,\n        viewport: gfx.Viewport,\n        ppl: rendering.BasicPipeline,\n        pass: rendering.BasicRenderPassBuilder,\n    ): rendering.BasicRenderPassBuilder {\n        this._addLightQueues(camera, pass);\n\n        let count = 0;\n        const shadowMapSize = ppl.pipelineSceneData.shadows.size;\n        for (const light of this.shadowEnabledSpotLights) {\n            const shadowPass = ppl.addRenderPass(shadowMapSize.x, shadowMapSize.y, 'default');\n            shadowPass.name = 'SpotlightShadowPass';\n            // Reuse csm shadow map\n            shadowPass.addRenderTarget(`ShadowMap${id}`, LoadOp.CLEAR, StoreOp.STORE, new Color(1, 1, 1, 1));\n            shadowPass.addDepthStencil(`ShadowDepth${id}`, LoadOp.CLEAR, StoreOp.DISCARD);\n            shadowPass.addQueue(rendering.QueueHint.NONE, 'shadow-caster')\n                .addScene(camera, rendering.SceneFlags.OPAQUE | rendering.SceneFlags.MASK | rendering.SceneFlags.SHADOW_CASTER)\n                .useLightFrustum(light);\n\n            // Add spot-light pass\n            // Save last RenderPass to the `pass` variable\n            ++count;\n            const storeOp = count === this.shadowEnabledSpotLights.length\n                ? depthStencilStoreOp\n                : StoreOp.STORE;\n\n            pass = ppl.addRenderPass(width, height, 'default');\n            pass.name = 'SpotlightWithShadowMap';\n            pass.setViewport(viewport);\n            pass.addRenderTarget(colorName, LoadOp.LOAD);\n            pass.addDepthStencil(depthStencilName, LoadOp.LOAD, storeOp);\n            pass.addTexture(`ShadowMap${id}`, 'cc_spotShadowMap');\n            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');\n            queue.addScene(\n                camera,\n                rendering.SceneFlags.BLEND,\n                light,\n            );\n        }\n        return pass;\n    }\n\n    public isMultipleLightPassesNeeded(): boolean {\n        return this.shadowEnabledSpotLights.length > 0;\n    }\n}\n\nexport interface ForwardPassConfigs {\n    enableMainLightShadowMap: boolean;\n    enableMainLightPlanarShadowMap: boolean;\n    enablePlanarReflectionProbe: boolean;\n    enableMSAA: boolean;\n    enableSingleForwardPass: boolean;\n}\n\nexport class BuiltinForwardPassBuilder implements rendering.PipelinePassBuilder {\n    static ConfigOrder = 100;\n    static RenderOrder = 100;\n    getConfigOrder(): number {\n        return BuiltinForwardPassBuilder.ConfigOrder;\n    }\n    getRenderOrder(): number {\n        return BuiltinForwardPassBuilder.RenderOrder;\n    }\n    configCamera(\n        camera: Readonly<renderer.scene.Camera>,\n        pipelineConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & ForwardPassConfigs): void {\n        // Shadow\n        cameraConfigs.enableMainLightShadowMap = pipelineConfigs.shadowEnabled\n            && !pipelineConfigs.usePlanarShadow\n            && !!camera.scene\n            && !!camera.scene.mainLight\n            && camera.scene.mainLight.shadowEnabled;\n\n        cameraConfigs.enableMainLightPlanarShadowMap = pipelineConfigs.shadowEnabled\n            && pipelineConfigs.usePlanarShadow\n            && !!camera.scene\n            && !!camera.scene.mainLight\n            && camera.scene.mainLight.shadowEnabled;\n\n        // Reflection Probe\n        cameraConfigs.enablePlanarReflectionProbe =\n            cameraConfigs.isMainGameWindow || camera.cameraUsage === CameraUsage.SCENE_VIEW;\n\n        // MSAA\n        cameraConfigs.enableMSAA = cameraConfigs.settings.msaa.enabled\n            && !cameraConfigs.enableStoreSceneDepth // Cannot store MS depth, resolve depth is also not cross-platform\n            && !pipelineConfigs.isWeb // TODO(zhouzhenglong): remove this constraint\n            && !pipelineConfigs.isWebGL1;\n\n        // Forward rendering (Depend on MSAA and TBR)\n        cameraConfigs.enableSingleForwardPass\n            = pipelineConfigs.isMobile || cameraConfigs.enableMSAA;\n\n        ++cameraConfigs.remainingPasses;\n    }\n    windowResize(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\n        window: renderer.RenderWindow,\n        camera: renderer.scene.Camera,\n        nativeWidth: number,\n        nativeHeight: number): void {\n        const ResourceFlags = rendering.ResourceFlags;\n        const ResourceResidency = rendering.ResourceResidency;\n        const id = window.renderWindowId;\n        const settings = cameraConfigs.settings;\n\n        const width = cameraConfigs.enableShadingScale\n            ? Math.max(Math.floor(nativeWidth * cameraConfigs.shadingScale), 1)\n            : nativeWidth;\n        const height = cameraConfigs.enableShadingScale\n            ? Math.max(Math.floor(nativeHeight * cameraConfigs.shadingScale), 1)\n            : nativeHeight;\n\n        // MsaaRadiance\n        if (cameraConfigs.enableMSAA) {\n            // Notice: We never store multisample results.\n            // These samples are always resolved and discarded at the end of the render pass.\n            // So the ResourceResidency should be MEMORYLESS.\n            if (cameraConfigs.enableHDR) {\n                ppl.addTexture(`MsaaRadiance${id}`, TextureType.TEX2D, cameraConfigs.radianceFormat, width, height, 1, 1, 1,\n                    settings.msaa.sampleCount, ResourceFlags.COLOR_ATTACHMENT, ResourceResidency.MEMORYLESS);\n            } else {\n                ppl.addTexture(`MsaaRadiance${id}`, TextureType.TEX2D, Format.RGBA8, width, height, 1, 1, 1,\n                    settings.msaa.sampleCount, ResourceFlags.COLOR_ATTACHMENT, ResourceResidency.MEMORYLESS);\n            }\n            ppl.addTexture(`MsaaDepthStencil${id}`, TextureType.TEX2D, Format.DEPTH_STENCIL, width, height, 1, 1, 1,\n                settings.msaa.sampleCount, ResourceFlags.DEPTH_STENCIL_ATTACHMENT, ResourceResidency.MEMORYLESS);\n        }\n\n        // Mainlight ShadowMap\n        ppl.addRenderTarget(\n            `ShadowMap${id}`,\n            pplConfigs.shadowMapFormat,\n            pplConfigs.shadowMapSize.x,\n            pplConfigs.shadowMapSize.y,\n        );\n        ppl.addDepthStencil(\n            `ShadowDepth${id}`,\n            Format.DEPTH_STENCIL,\n            pplConfigs.shadowMapSize.x,\n            pplConfigs.shadowMapSize.y,\n        );\n\n        // Spot-light shadow maps\n        if (cameraConfigs.enableSingleForwardPass) {\n            const count = pplConfigs.mobileMaxSpotLightShadowMaps;\n            for (let i = 0; i !== count; ++i) {\n                ppl.addRenderTarget(\n                    `SpotShadowMap${i}`,\n                    pplConfigs.shadowMapFormat,\n                    pplConfigs.shadowMapSize.x,\n                    pplConfigs.shadowMapSize.y,\n                );\n                ppl.addDepthStencil(\n                    `SpotShadowDepth${i}`,\n                    Format.DEPTH_STENCIL,\n                    pplConfigs.shadowMapSize.x,\n                    pplConfigs.shadowMapSize.y,\n                );\n            }\n        }\n    }\n    setup(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & ForwardPassConfigs,\n        camera: renderer.scene.Camera,\n        context: PipelineContext): rendering.BasicRenderPassBuilder | undefined {\n        const id = camera.window.renderWindowId;\n\n        const scene = camera.scene!;\n        const mainLight = scene.mainLight;\n\n        --cameraConfigs.remainingPasses;\n        assert(cameraConfigs.remainingPasses >= 0);\n\n        // Forward Lighting (Light Culling)\n        this.forwardLighting.cullLights(scene, camera.frustum);\n\n        // Main Directional light CSM Shadow Map\n        if (cameraConfigs.enableMainLightShadowMap) {\n            assert(!!mainLight);\n            this._addCascadedShadowMapPass(ppl, pplConfigs, id, mainLight, camera);\n        }\n\n        // Spot light shadow maps (Mobile or MSAA)\n        if (cameraConfigs.enableSingleForwardPass) {\n            // Currently, only support 1 spot light with shadow map on mobile platform.\n            // TODO(zhouzhenglong): Relex this limitation.\n            this.forwardLighting.addSpotlightShadowPasses(\n                ppl, camera, pplConfigs.mobileMaxSpotLightShadowMaps);\n        }\n\n        this._tryAddReflectionProbePasses(ppl, cameraConfigs, id, mainLight, camera.scene);\n\n        if (cameraConfigs.remainingPasses > 0 || cameraConfigs.enableShadingScale) {\n            context.colorName = cameraConfigs.enableShadingScale\n                ? `ScaledRadiance0_${id}`\n                : `Radiance0_${id}`;\n            context.depthStencilName = cameraConfigs.enableShadingScale\n                ? `ScaledSceneDepth_${id}`\n                : `SceneDepth_${id}`;\n        } else {\n            context.colorName = cameraConfigs.colorName;\n            context.depthStencilName = cameraConfigs.depthStencilName;\n        }\n\n        const pass = this._addForwardRadiancePasses(\n            ppl, pplConfigs, cameraConfigs, id, camera,\n            cameraConfigs.width, cameraConfigs.height, mainLight,\n            context.colorName, context.depthStencilName,\n            !cameraConfigs.enableMSAA,\n            cameraConfigs.enableStoreSceneDepth ? StoreOp.STORE : StoreOp.DISCARD);\n\n        if (!cameraConfigs.enableStoreSceneDepth) {\n            context.depthStencilName = '';\n        }\n\n        if (cameraConfigs.remainingPasses === 0 && cameraConfigs.enableShadingScale) {\n            return addCopyToScreenPass(ppl, pplConfigs, cameraConfigs, context.colorName);\n        } else {\n            return pass;\n        }\n    }\n    private _addCascadedShadowMapPass(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        id: number,\n        light: renderer.scene.DirectionalLight,\n        camera: renderer.scene.Camera,\n    ): void {\n        const QueueHint = rendering.QueueHint;\n        const SceneFlags = rendering.SceneFlags;\n        // ----------------------------------------------------------------\n        // Dynamic states\n        // ----------------------------------------------------------------\n        const shadowSize = ppl.pipelineSceneData.shadows.size;\n        const width = shadowSize.x;\n        const height = shadowSize.y;\n\n        const viewport = this._viewport;\n        viewport.left = viewport.top = 0;\n        viewport.width = width;\n        viewport.height = height;\n\n        // ----------------------------------------------------------------\n        // CSM Shadow Map\n        // ----------------------------------------------------------------\n        const pass = ppl.addRenderPass(width, height, 'default');\n        pass.name = 'CascadedShadowMap';\n        pass.addRenderTarget(`ShadowMap${id}`, LoadOp.CLEAR, StoreOp.STORE, new Color(1, 1, 1, 1));\n        pass.addDepthStencil(`ShadowDepth${id}`, LoadOp.CLEAR, StoreOp.DISCARD);\n        const csmLevel = ppl.pipelineSceneData.csmSupported ? light.csmLevel : 1;\n\n        // Add shadow map viewports\n        for (let level = 0; level !== csmLevel; ++level) {\n            getCsmMainLightViewport(light, width, height, level, this._viewport, pplConfigs.screenSpaceSignY);\n            const queue = pass.addQueue(QueueHint.NONE, 'shadow-caster');\n            if (!pplConfigs.isWebGPU) { // Temporary workaround for WebGPU\n                queue.setViewport(this._viewport);\n            }\n            queue\n                .addScene(camera, SceneFlags.OPAQUE | SceneFlags.MASK | SceneFlags.SHADOW_CASTER)\n                .useLightFrustum(light, level);\n        }\n    }\n    private _tryAddReflectionProbePasses(\n        ppl: rendering.BasicPipeline,\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\n        id: number,\n        mainLight: renderer.scene.DirectionalLight | null,\n        scene: renderer.RenderScene | null,\n    ): void {\n        const reflectionProbeManager = cclegacy.internal.reflectionProbeManager as ReflectionProbeManager | undefined;\n        if (!reflectionProbeManager) {\n            return;\n        }\n        const ResourceResidency = rendering.ResourceResidency;\n        const probes = reflectionProbeManager.getProbes();\n        const maxProbeCount = 4;\n        let probeID = 0;\n        for (const probe of probes) {\n            if (!probe.needRender) {\n                continue;\n            }\n            const area = probe.renderArea();\n            const width = Math.max(Math.floor(area.x), 1);\n            const height = Math.max(Math.floor(area.y), 1);\n\n            if (probe.probeType === renderer.scene.ProbeType.PLANAR) {\n                if (!cameraConfigs.enablePlanarReflectionProbe) {\n                    continue;\n                }\n                const window: renderer.RenderWindow = probe.realtimePlanarTexture!.window!;\n                const colorName = `PlanarProbeRT${probeID}`;\n                const depthStencilName = `PlanarProbeDS${probeID}`;\n                // ProbeResource\n                ppl.addRenderWindow(colorName,\n                    cameraConfigs.radianceFormat, width, height, window);\n                ppl.addDepthStencil(depthStencilName,\n                    gfx.Format.DEPTH_STENCIL, width, height, ResourceResidency.MEMORYLESS);\n\n                // Rendering\n                const probePass = ppl.addRenderPass(width, height, 'default');\n                probePass.name = `PlanarReflectionProbe${probeID}`;\n                this._buildReflectionProbePass(probePass, cameraConfigs, id, probe.camera,\n                    colorName, depthStencilName, mainLight, scene);\n            } else if (EDITOR) {\n                for (let faceIdx = 0; faceIdx < probe.bakedCubeTextures.length; faceIdx++) {\n                    probe.updateCameraDir(faceIdx);\n                    const window: renderer.RenderWindow = probe.bakedCubeTextures[faceIdx].window!;\n                    const colorName = `CubeProbeRT${probeID}${faceIdx}`;\n                    const depthStencilName = `CubeProbeDS${probeID}${faceIdx}`;\n                    // ProbeResource\n                    ppl.addRenderWindow(colorName,\n                        cameraConfigs.radianceFormat, width, height, window);\n                    ppl.addDepthStencil(depthStencilName,\n                        gfx.Format.DEPTH_STENCIL, width, height, ResourceResidency.MEMORYLESS);\n\n                    // Rendering\n                    const probePass = ppl.addRenderPass(width, height, 'default');\n                    probePass.name = `CubeProbe${probeID}${faceIdx}`;\n                    this._buildReflectionProbePass(probePass, cameraConfigs, id, probe.camera,\n                        colorName, depthStencilName, mainLight, scene);\n                }\n                probe.needRender = false;\n            }\n            ++probeID;\n            if (probeID === maxProbeCount) {\n                break;\n            }\n        }\n    }\n    private _buildReflectionProbePass(\n        pass: rendering.BasicRenderPassBuilder,\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\n        id: number,\n        camera: renderer.scene.Camera,\n        colorName: string,\n        depthStencilName: string,\n        mainLight: renderer.scene.DirectionalLight | null,\n        scene: renderer.RenderScene | null = null,\n    ): void {\n        const QueueHint = rendering.QueueHint;\n        const SceneFlags = rendering.SceneFlags;\n        // set viewport\n        const colorStoreOp = cameraConfigs.enableMSAA ? StoreOp.DISCARD : StoreOp.STORE;\n\n        // bind output render target\n        if (forwardNeedClearColor(camera)) {\n            this._reflectionProbeClearColor.x = camera.clearColor.x;\n            this._reflectionProbeClearColor.y = camera.clearColor.y;\n            this._reflectionProbeClearColor.z = camera.clearColor.z;\n            const clearColor = rendering.packRGBE(this._reflectionProbeClearColor);\n            this._clearColor.x = clearColor.x;\n            this._clearColor.y = clearColor.y;\n            this._clearColor.z = clearColor.z;\n            this._clearColor.w = clearColor.w;\n            pass.addRenderTarget(colorName, LoadOp.CLEAR, colorStoreOp, this._clearColor);\n        } else {\n            pass.addRenderTarget(colorName, LoadOp.LOAD, colorStoreOp);\n        }\n\n        // bind depth stencil buffer\n        if (camera.clearFlag & ClearFlagBit.DEPTH_STENCIL) {\n            pass.addDepthStencil(\n                depthStencilName,\n                LoadOp.CLEAR,\n                StoreOp.DISCARD,\n                camera.clearDepth,\n                camera.clearStencil,\n                camera.clearFlag & ClearFlagBit.DEPTH_STENCIL,\n            );\n        } else {\n            pass.addDepthStencil(depthStencilName, LoadOp.LOAD, StoreOp.DISCARD);\n        }\n\n        // Set shadow map if enabled\n        if (cameraConfigs.enableMainLightShadowMap) {\n            pass.addTexture(`ShadowMap${id}`, 'cc_shadowMap');\n        }\n\n        // TODO(zhouzhenglong): Separate OPAQUE and MASK queue\n\n        // add opaque and mask queue\n        pass.addQueue(QueueHint.NONE, 'reflect-map') // Currently we put OPAQUE and MASK into one queue, so QueueHint is NONE\n            .addScene(camera,\n                SceneFlags.OPAQUE | SceneFlags.MASK | SceneFlags.REFLECTION_PROBE,\n                mainLight || undefined,\n                scene ? scene : undefined);\n    }\n    private _addForwardRadiancePasses(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\n        id: number,\n        camera: renderer.scene.Camera,\n        width: number,\n        height: number,\n        mainLight: renderer.scene.DirectionalLight | null,\n        colorName: string,\n        depthStencilName: string,\n        disableMSAA: boolean = false,\n        depthStencilStoreOp: gfx.StoreOp = StoreOp.DISCARD,\n    ): rendering.BasicRenderPassBuilder {\n        const QueueHint = rendering.QueueHint;\n        const SceneFlags = rendering.SceneFlags;\n        // ----------------------------------------------------------------\n        // Dynamic states\n        // ----------------------------------------------------------------\n        // Prepare camera clear color\n        const clearColor = camera.clearColor; // Reduce C++/TS interop\n        this._clearColor.x = clearColor.x;\n        this._clearColor.y = clearColor.y;\n        this._clearColor.z = clearColor.z;\n        this._clearColor.w = clearColor.w;\n\n        // Prepare camera viewport\n        const viewport = camera.viewport; // Reduce C++/TS interop\n        this._viewport.left = Math.round(viewport.x * width);\n        this._viewport.top = Math.round(viewport.y * height);\n        // Here we must use camera.viewport.width instead of camera.viewport.z, which\n        // is undefined on native platform. The same as camera.viewport.height.\n        this._viewport.width = Math.max(Math.round(viewport.width * width), 1);\n        this._viewport.height = Math.max(Math.round(viewport.height * height), 1);\n\n        // MSAA\n        const enableMSAA = !disableMSAA && cameraConfigs.enableMSAA;\n        assert(!enableMSAA || cameraConfigs.enableSingleForwardPass);\n\n        // ----------------------------------------------------------------\n        // Forward Lighting (Main Directional Light)\n        // ----------------------------------------------------------------\n        const pass = cameraConfigs.enableSingleForwardPass\n            ? this._addForwardSingleRadiancePass(ppl, pplConfigs, cameraConfigs,\n                id, camera, enableMSAA, width, height, mainLight,\n                colorName, depthStencilName, depthStencilStoreOp)\n            : this._addForwardMultipleRadiancePasses(ppl, cameraConfigs,\n                id, camera, width, height, mainLight,\n                colorName, depthStencilName, depthStencilStoreOp);\n\n        // Planar Shadow\n        if (cameraConfigs.enableMainLightPlanarShadowMap) {\n            this._addPlanarShadowQueue(camera, mainLight, pass);\n        }\n\n        // ----------------------------------------------------------------\n        // Forward Lighting (Blend)\n        // ----------------------------------------------------------------\n        // Add transparent queue\n\n        const sceneFlags = SceneFlags.BLEND |\n            (camera.geometryRenderer\n                ? SceneFlags.GEOMETRY\n                : SceneFlags.NONE);\n\n        pass\n            .addQueue(QueueHint.BLEND)\n            .addScene(camera, sceneFlags, mainLight || undefined);\n\n        return pass;\n    }\n    private _addForwardSingleRadiancePass(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\n        id: number,\n        camera: renderer.scene.Camera,\n        enableMSAA: boolean,\n        width: number,\n        height: number,\n        mainLight: renderer.scene.DirectionalLight | null,\n        colorName: string,\n        depthStencilName: string,\n        depthStencilStoreOp: gfx.StoreOp\n    ): rendering.BasicRenderPassBuilder {\n        assert(cameraConfigs.enableSingleForwardPass);\n        // ----------------------------------------------------------------\n        // Forward Lighting (Main Directional Light)\n        // ----------------------------------------------------------------\n        let pass: rendering.BasicRenderPassBuilder;\n        if (enableMSAA) {\n            const msaaRadianceName = `MsaaRadiance${id}`;\n            const msaaDepthStencilName = `MsaaDepthStencil${id}`;\n            const sampleCount = cameraConfigs.settings.msaa.sampleCount;\n\n            const msPass = ppl.addMultisampleRenderPass(width, height, sampleCount, 0, 'default');\n            msPass.name = 'MsaaForwardPass';\n\n            // MSAA always discards depth stencil\n            this._buildForwardMainLightPass(msPass, cameraConfigs, id, camera,\n                msaaRadianceName, msaaDepthStencilName, StoreOp.DISCARD, mainLight);\n\n            msPass.resolveRenderTarget(msaaRadianceName, colorName);\n\n            pass = msPass;\n        } else {\n            pass = ppl.addRenderPass(width, height, 'default');\n            pass.name = 'ForwardPass';\n\n            this._buildForwardMainLightPass(pass, cameraConfigs, id, camera,\n                colorName, depthStencilName, depthStencilStoreOp, mainLight);\n        }\n        assert(pass !== undefined);\n\n        // Forward Lighting (Additive Lights)\n        this.forwardLighting.addLightQueues(\n            pass,\n            camera,\n            pplConfigs.mobileMaxSpotLightShadowMaps,\n        );\n\n        return pass;\n    }\n    private _addForwardMultipleRadiancePasses(\n        ppl: rendering.BasicPipeline,\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\n        id: number,\n        camera: renderer.scene.Camera,\n        width: number,\n        height: number,\n        mainLight: renderer.scene.DirectionalLight | null,\n        colorName: string,\n        depthStencilName: string,\n        depthStencilStoreOp: gfx.StoreOp\n    ): rendering.BasicRenderPassBuilder {\n        assert(!cameraConfigs.enableSingleForwardPass);\n\n        // Forward Lighting (Main Directional Light)\n        let pass = ppl.addRenderPass(width, height, 'default');\n        pass.name = 'ForwardPass';\n\n        const firstStoreOp = this.forwardLighting.isMultipleLightPassesNeeded()\n            ? StoreOp.STORE\n            : depthStencilStoreOp;\n\n        this._buildForwardMainLightPass(pass, cameraConfigs,\n            id, camera, colorName, depthStencilName, firstStoreOp, mainLight);\n\n        // Forward Lighting (Additive Lights)\n        pass = this.forwardLighting\n            .addLightPasses(colorName, depthStencilName, depthStencilStoreOp,\n                id, width, height, camera, this._viewport, ppl, pass);\n\n        return pass;\n    }\n    private _buildForwardMainLightPass(\n        pass: rendering.BasicRenderPassBuilder,\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\n        id: number,\n        camera: renderer.scene.Camera,\n        colorName: string,\n        depthStencilName: string,\n        depthStencilStoreOp: gfx.StoreOp,\n        mainLight: renderer.scene.DirectionalLight | null,\n        scene: renderer.RenderScene | null = null,\n    ): void {\n        const QueueHint = rendering.QueueHint;\n        const SceneFlags = rendering.SceneFlags;\n        // set viewport\n        pass.setViewport(this._viewport);\n\n        const colorStoreOp = cameraConfigs.enableMSAA ? StoreOp.DISCARD : StoreOp.STORE;\n\n        // bind output render target\n        if (forwardNeedClearColor(camera)) {\n            pass.addRenderTarget(colorName, LoadOp.CLEAR, colorStoreOp, this._clearColor);\n        } else {\n            pass.addRenderTarget(colorName, LoadOp.LOAD, colorStoreOp);\n        }\n\n        // bind depth stencil buffer\n        if (DEBUG) {\n            if (colorName === cameraConfigs.colorName &&\n                depthStencilName !== cameraConfigs.depthStencilName) {\n                warn('Default framebuffer cannot use custom depth stencil buffer');\n            }\n        }\n\n        if (camera.clearFlag & ClearFlagBit.DEPTH_STENCIL) {\n            pass.addDepthStencil(\n                depthStencilName,\n                LoadOp.CLEAR,\n                depthStencilStoreOp,\n                camera.clearDepth,\n                camera.clearStencil,\n                camera.clearFlag & ClearFlagBit.DEPTH_STENCIL,\n            );\n        } else {\n            pass.addDepthStencil(depthStencilName, LoadOp.LOAD, depthStencilStoreOp);\n        }\n\n        // Set shadow map if enabled\n        if (cameraConfigs.enableMainLightShadowMap) {\n            pass.addTexture(`ShadowMap${id}`, 'cc_shadowMap');\n        }\n\n        // TODO(zhouzhenglong): Separate OPAQUE and MASK queue\n\n        // add opaque and mask queue\n        pass.addQueue(QueueHint.NONE) // Currently we put OPAQUE and MASK into one queue, so QueueHint is NONE\n            .addScene(camera,\n                SceneFlags.OPAQUE | SceneFlags.MASK,\n                mainLight || undefined,\n                scene ? scene : undefined);\n    }\n    private _addPlanarShadowQueue(\n        camera: renderer.scene.Camera,\n        mainLight: renderer.scene.DirectionalLight | null,\n        pass: rendering.BasicRenderPassBuilder,\n    ) {\n        const QueueHint = rendering.QueueHint;\n        const SceneFlags = rendering.SceneFlags;\n        pass.addQueue(QueueHint.BLEND, 'planar-shadow')\n            .addScene(\n                camera,\n                SceneFlags.SHADOW_CASTER | SceneFlags.PLANAR_SHADOW | SceneFlags.BLEND,\n                mainLight || undefined,\n            );\n    }\n    private readonly forwardLighting = new ForwardLighting();\n    private readonly _viewport = new Viewport();\n    private readonly _clearColor = new Color(0, 0, 0, 1);\n    private readonly _reflectionProbeClearColor = new Vec3(0, 0, 0);\n}\n\nexport interface BloomPassConfigs {\n    enableBloom: boolean;\n}\n\nexport class BuiltinBloomPassBuilder implements rendering.PipelinePassBuilder {\n    getConfigOrder(): number {\n        return 0;\n    }\n    getRenderOrder(): number {\n        return 200;\n    }\n    configCamera(\n        camera: Readonly<renderer.scene.Camera>,\n        pipelineConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & BloomPassConfigs): void {\n        cameraConfigs.enableBloom\n            = cameraConfigs.settings.bloom.enabled\n            && !!cameraConfigs.settings.bloom.material;\n        if (cameraConfigs.enableBloom) {\n            ++cameraConfigs.remainingPasses;\n        }\n    }\n    windowResize(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & BloomPassConfigs,\n        window: renderer.RenderWindow): void {\n        if (cameraConfigs.enableBloom) {\n            const id = window.renderWindowId;\n            let bloomWidth = cameraConfigs.width;\n            let bloomHeight = cameraConfigs.height;\n            for (let i = 0; i !== cameraConfigs.settings.bloom.iterations + 1; ++i) {\n                bloomWidth = Math.max(Math.floor(bloomWidth / 2), 1);\n                bloomHeight = Math.max(Math.floor(bloomHeight / 2), 1);\n                ppl.addRenderTarget(`BloomTex${id}_${i}`,\n                    cameraConfigs.radianceFormat, bloomWidth, bloomHeight);\n            }\n        }\n    }\n\n    setup(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & BloomPassConfigs,\n        camera: renderer.scene.Camera,\n        context: PipelineContext,\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\n        : rendering.BasicRenderPassBuilder | undefined {\n        if (!cameraConfigs.enableBloom) {\n            return prevRenderPass;\n        }\n\n        --cameraConfigs.remainingPasses;\n        assert(cameraConfigs.remainingPasses >= 0);\n        const id = camera.window.renderWindowId;\n        assert(!!cameraConfigs.settings.bloom.material);\n        return this._addKawaseDualFilterBloomPasses(\n            ppl, pplConfigs,\n            cameraConfigs,\n            cameraConfigs.settings,\n            cameraConfigs.settings.bloom.material,\n            id,\n            cameraConfigs.width,\n            cameraConfigs.height,\n            context.colorName);\n    }\n\n    private _addKawaseDualFilterBloomPasses(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & Readonly<BloomPassConfigs>,\n        settings: PipelineSettings,\n        bloomMaterial: Material,\n        id: number,\n        width: number,\n        height: number,\n        radianceName: string,\n    ): rendering.BasicRenderPassBuilder {\n        const QueueHint = rendering.QueueHint;\n        // Based on Kawase Dual Filter Blur. Saves bandwidth on mobile devices.\n        // eslint-disable-next-line max-len\n        // https://community.arm.com/cfs-file/__key/communityserver-blogs-components-weblogfiles/00-00-00-20-66/siggraph2015_2D00_mmg_2D00_marius_2D00_slides.pdf\n\n        // Size: [prefilter(1/2), downsample(1/4), downsample(1/8), downsample(1/16), ...]\n        const iterations = settings.bloom.iterations;\n        const sizeCount = iterations + 1;\n        this._bloomWidths.length = sizeCount;\n        this._bloomHeights.length = sizeCount;\n        this._bloomWidths[0] = Math.max(Math.floor(width / 2), 1);\n        this._bloomHeights[0] = Math.max(Math.floor(height / 2), 1);\n        for (let i = 1; i !== sizeCount; ++i) {\n            this._bloomWidths[i] = Math.max(Math.floor(this._bloomWidths[i - 1] / 2), 1);\n            this._bloomHeights[i] = Math.max(Math.floor(this._bloomHeights[i - 1] / 2), 1);\n        }\n\n        // Bloom texture names\n        this._bloomTexNames.length = sizeCount;\n        for (let i = 0; i !== sizeCount; ++i) {\n            this._bloomTexNames[i] = `BloomTex${id}_${i}`;\n        }\n\n        // Setup bloom parameters\n        this._bloomParams.x = pplConfigs.useFloatOutput ? 1 : 0;\n        this._bloomParams.x = 0; // unused\n        this._bloomParams.z = settings.bloom.threshold;\n        this._bloomParams.w = settings.bloom.enableAlphaMask ? 1 : 0;\n\n        // Prefilter pass\n        const prefilterPass = ppl.addRenderPass(this._bloomWidths[0], this._bloomHeights[0], 'cc-bloom-prefilter');\n        prefilterPass.addRenderTarget(\n            this._bloomTexNames[0],\n            LoadOp.CLEAR,\n            StoreOp.STORE,\n            this._clearColorTransparentBlack,\n        );\n        prefilterPass.addTexture(radianceName, 'inputTexture');\n        prefilterPass.setVec4('g_platform', pplConfigs.platform);\n        prefilterPass.setVec4('bloomParams', this._bloomParams);\n        prefilterPass\n            .addQueue(QueueHint.OPAQUE)\n            .addFullscreenQuad(bloomMaterial, 0);\n\n        // Downsample passes\n        for (let i = 1; i !== sizeCount; ++i) {\n            const downPass = ppl.addRenderPass(this._bloomWidths[i], this._bloomHeights[i], 'cc-bloom-downsample');\n            downPass.addRenderTarget(this._bloomTexNames[i], LoadOp.CLEAR, StoreOp.STORE, this._clearColorTransparentBlack);\n            downPass.addTexture(this._bloomTexNames[i - 1], 'bloomTexture');\n            this._bloomTexSize.x = this._bloomWidths[i - 1];\n            this._bloomTexSize.y = this._bloomHeights[i - 1];\n            downPass.setVec4('g_platform', pplConfigs.platform);\n            downPass.setVec4('bloomTexSize', this._bloomTexSize);\n            downPass\n                .addQueue(QueueHint.OPAQUE)\n                .addFullscreenQuad(bloomMaterial, 1);\n        }\n\n        // Upsample passes\n        for (let i = iterations; i-- > 0;) {\n            const upPass = ppl.addRenderPass(this._bloomWidths[i], this._bloomHeights[i], 'cc-bloom-upsample');\n            upPass.addRenderTarget(this._bloomTexNames[i], LoadOp.CLEAR, StoreOp.STORE, this._clearColorTransparentBlack);\n            upPass.addTexture(this._bloomTexNames[i + 1], 'bloomTexture');\n            this._bloomTexSize.x = this._bloomWidths[i + 1];\n            this._bloomTexSize.y = this._bloomHeights[i + 1];\n            upPass.setVec4('g_platform', pplConfigs.platform);\n            upPass.setVec4('bloomTexSize', this._bloomTexSize);\n            upPass\n                .addQueue(QueueHint.OPAQUE)\n                .addFullscreenQuad(bloomMaterial, 2);\n        }\n\n        // Combine pass\n        const combinePass = ppl.addRenderPass(width, height, 'cc-bloom-combine');\n        combinePass.addRenderTarget(radianceName, LoadOp.LOAD, StoreOp.STORE);\n        combinePass.addTexture(this._bloomTexNames[0], 'bloomTexture');\n        combinePass.setVec4('g_platform', pplConfigs.platform);\n        combinePass.setVec4('bloomParams', this._bloomParams);\n        combinePass\n            .addQueue(QueueHint.BLEND)\n            .addFullscreenQuad(bloomMaterial, 3);\n\n        if (cameraConfigs.remainingPasses === 0) {\n            return addCopyToScreenPass(ppl, pplConfigs, cameraConfigs, radianceName);\n        } else {\n            return combinePass;\n        }\n    }\n    // Bloom\n    private readonly _clearColorTransparentBlack = new Color(0, 0, 0, 0);\n    private readonly _bloomParams = new Vec4(0, 0, 0, 0);\n    private readonly _bloomTexSize = new Vec4(0, 0, 0, 0);\n    private readonly _bloomWidths: Array<number> = [];\n    private readonly _bloomHeights: Array<number> = [];\n    private readonly _bloomTexNames: Array<string> = [];\n}\n\nexport interface ToneMappingPassConfigs {\n    enableToneMapping: boolean;\n    enableColorGrading: boolean;\n}\n\nexport class BuiltinToneMappingPassBuilder implements rendering.PipelinePassBuilder {\n    getConfigOrder(): number {\n        return 0;\n    }\n    getRenderOrder(): number {\n        return 300;\n    }\n    configCamera(\n        camera: Readonly<renderer.scene.Camera>,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & ToneMappingPassConfigs): void {\n        const settings = cameraConfigs.settings;\n\n        cameraConfigs.enableColorGrading\n            = settings.colorGrading.enabled\n            && !!settings.colorGrading.material\n            && !!settings.colorGrading.colorGradingMap;\n\n        cameraConfigs.enableToneMapping\n            = cameraConfigs.enableHDR // From Half to RGBA8\n            || cameraConfigs.enableColorGrading; // Color grading\n\n        if (cameraConfigs.enableToneMapping) {\n            ++cameraConfigs.remainingPasses;\n        }\n    }\n    windowResize(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & ToneMappingPassConfigs): void {\n        if (cameraConfigs.enableColorGrading) {\n            assert(!!cameraConfigs.settings.colorGrading.material);\n            cameraConfigs.settings.colorGrading.material.setProperty(\n                'colorGradingMap',\n                cameraConfigs.settings.colorGrading.colorGradingMap);\n        }\n    }\n    setup(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & ToneMappingPassConfigs,\n        camera: renderer.scene.Camera,\n        context: PipelineContext,\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\n        : rendering.BasicRenderPassBuilder | undefined {\n        if (!cameraConfigs.enableToneMapping) {\n            return prevRenderPass;\n        }\n\n        --cameraConfigs.remainingPasses;\n        assert(cameraConfigs.remainingPasses >= 0);\n        if (cameraConfigs.remainingPasses === 0) {\n            return this._addCopyAndTonemapPass(ppl, pplConfigs, cameraConfigs,\n                cameraConfigs.nativeWidth, cameraConfigs.nativeHeight,\n                context.colorName, cameraConfigs.colorName);\n        } else {\n            const id = cameraConfigs.renderWindowId;\n            const ldrColorPrefix = cameraConfigs.enableShadingScale\n                ? `ScaledLdrColor`\n                : `LdrColor`;\n\n            const ldrColorName = getPingPongRenderTarget(context.colorName, ldrColorPrefix, id);\n            const radianceName = context.colorName;\n            context.colorName = ldrColorName;\n\n            return this._addCopyAndTonemapPass(ppl, pplConfigs, cameraConfigs,\n                cameraConfigs.width, cameraConfigs.height,\n                radianceName, ldrColorName);\n        }\n    }\n    private _addCopyAndTonemapPass(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & ToneMappingPassConfigs,\n        width: number,\n        height: number,\n        radianceName: string,\n        colorName: string,\n    ): rendering.BasicRenderPassBuilder {\n        let pass: rendering.BasicRenderPassBuilder;\n        const settings = cameraConfigs.settings;\n        if (cameraConfigs.enableColorGrading) {\n            assert(!!settings.colorGrading.material);\n            assert(!!settings.colorGrading.colorGradingMap);\n\n            const lutTex = settings.colorGrading.colorGradingMap;\n            this._colorGradingTexSize.x = lutTex.width;\n            this._colorGradingTexSize.y = lutTex.height;\n\n            const isSquareMap = lutTex.width === lutTex.height;\n            if (isSquareMap) {\n                pass = ppl.addRenderPass(width, height, 'cc-color-grading-8x8');\n            } else {\n                pass = ppl.addRenderPass(width, height, 'cc-color-grading-nx1');\n            }\n            pass.addRenderTarget(colorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\n            pass.addTexture(radianceName, 'sceneColorMap');\n            pass.setVec4('g_platform', pplConfigs.platform);\n            pass.setVec2('lutTextureSize', this._colorGradingTexSize);\n            pass.setFloat('contribute', settings.colorGrading.contribute);\n            pass.addQueue(rendering.QueueHint.OPAQUE)\n                .addFullscreenQuad(settings.colorGrading.material, isSquareMap ? 1 : 0);\n        } else {\n            pass = ppl.addRenderPass(width, height, 'cc-tone-mapping');\n            pass.addRenderTarget(colorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\n            pass.addTexture(radianceName, 'inputTexture');\n            pass.setVec4('g_platform', pplConfigs.platform);\n            if (settings.toneMapping.material) {\n                pass.addQueue(rendering.QueueHint.OPAQUE)\n                    .addFullscreenQuad(settings.toneMapping.material, 0);\n            } else {\n                assert(!!cameraConfigs.copyAndTonemapMaterial);\n                pass.addQueue(rendering.QueueHint.OPAQUE)\n                    .addFullscreenQuad(cameraConfigs.copyAndTonemapMaterial, 0);\n            }\n        }\n        return pass;\n    }\n    private readonly _colorGradingTexSize = new Vec2(0, 0);\n}\n\nexport interface FXAAPassConfigs {\n    enableFXAA: boolean;\n}\n\nexport class BuiltinFXAAPassBuilder implements rendering.PipelinePassBuilder {\n    getConfigOrder(): number {\n        return 0;\n    }\n    getRenderOrder(): number {\n        return 400;\n    }\n    configCamera(\n        camera: Readonly<renderer.scene.Camera>,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & FXAAPassConfigs): void {\n        cameraConfigs.enableFXAA\n            = cameraConfigs.settings.fxaa.enabled\n            && !!cameraConfigs.settings.fxaa.material;\n        if (cameraConfigs.enableFXAA) {\n            ++cameraConfigs.remainingPasses;\n        }\n    }\n    setup(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & FXAAPassConfigs,\n        camera: renderer.scene.Camera,\n        context: PipelineContext,\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\n        : rendering.BasicRenderPassBuilder | undefined {\n        if (!cameraConfigs.enableFXAA) {\n            return prevRenderPass;\n        }\n        --cameraConfigs.remainingPasses;\n        assert(cameraConfigs.remainingPasses >= 0);\n\n        const id = cameraConfigs.renderWindowId;\n        const ldrColorPrefix = cameraConfigs.enableShadingScale\n            ? `ScaledLdrColor`\n            : `LdrColor`;\n        const ldrColorName = getPingPongRenderTarget(context.colorName, ldrColorPrefix, id);\n\n        assert(!!cameraConfigs.settings.fxaa.material);\n        if (cameraConfigs.remainingPasses === 0) {\n            if (cameraConfigs.enableShadingScale) {\n                this._addFxaaPass(ppl, pplConfigs,\n                    cameraConfigs.settings.fxaa.material,\n                    cameraConfigs.width,\n                    cameraConfigs.height,\n                    context.colorName,\n                    ldrColorName);\n                return addCopyToScreenPass(ppl, pplConfigs, cameraConfigs, ldrColorName);\n            } else {\n                assert(cameraConfigs.width === cameraConfigs.nativeWidth);\n                assert(cameraConfigs.height === cameraConfigs.nativeHeight);\n                return this._addFxaaPass(ppl, pplConfigs,\n                    cameraConfigs.settings.fxaa.material,\n                    cameraConfigs.width,\n                    cameraConfigs.height,\n                    context.colorName,\n                    cameraConfigs.colorName);\n            }\n        } else {\n            const inputColorName = context.colorName;\n            context.colorName = ldrColorName;\n            const lastPass = this._addFxaaPass(ppl, pplConfigs,\n                cameraConfigs.settings.fxaa.material,\n                cameraConfigs.width,\n                cameraConfigs.height,\n                inputColorName,\n                ldrColorName);\n            return lastPass;\n        }\n    }\n    private _addFxaaPass(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        fxaaMaterial: Material,\n        width: number,\n        height: number,\n        ldrColorName: string,\n        colorName: string,\n    ): rendering.BasicRenderPassBuilder {\n        this._fxaaParams.x = width;\n        this._fxaaParams.y = height;\n        this._fxaaParams.z = 1 / width;\n        this._fxaaParams.w = 1 / height;\n\n        const pass = ppl.addRenderPass(width, height, 'cc-fxaa');\n        pass.addRenderTarget(colorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\n        pass.addTexture(ldrColorName, 'sceneColorMap');\n        pass.setVec4('g_platform', pplConfigs.platform);\n        pass.setVec4('texSize', this._fxaaParams);\n        pass.addQueue(rendering.QueueHint.OPAQUE)\n            .addFullscreenQuad(fxaaMaterial, 0);\n        return pass;\n    }\n    // FXAA\n    private readonly _fxaaParams = new Vec4(0, 0, 0, 0);\n}\n\nexport interface FSRPassConfigs {\n    enableFSR: boolean;\n}\n\nexport class BuiltinFsrPassBuilder implements rendering.PipelinePassBuilder {\n    getConfigOrder(): number {\n        return 0;\n    }\n    getRenderOrder(): number {\n        return 500;\n    }\n    configCamera(\n        camera: Readonly<renderer.scene.Camera>,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & FSRPassConfigs): void {\n        // FSR (Depend on Shading scale)\n        cameraConfigs.enableFSR = cameraConfigs.settings.fsr.enabled\n            && !!cameraConfigs.settings.fsr.material\n            && cameraConfigs.enableShadingScale\n            && cameraConfigs.shadingScale < 1.0;\n\n        if (cameraConfigs.enableFSR) {\n            ++cameraConfigs.remainingPasses;\n        }\n    }\n    setup(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & FSRPassConfigs,\n        camera: renderer.scene.Camera,\n        context: PipelineContext,\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\n        : rendering.BasicRenderPassBuilder | undefined {\n        if (!cameraConfigs.enableFSR) {\n            return prevRenderPass;\n        }\n        --cameraConfigs.remainingPasses;\n\n        const inputColorName = context.colorName;\n        const outputColorName\n            = cameraConfigs.remainingPasses === 0\n                ? cameraConfigs.colorName\n                : getPingPongRenderTarget(context.colorName, 'UiColor', cameraConfigs.renderWindowId);\n        context.colorName = outputColorName;\n\n        assert(!!cameraConfigs.settings.fsr.material);\n        return this._addFsrPass(ppl, pplConfigs, cameraConfigs,\n            cameraConfigs.settings,\n            cameraConfigs.settings.fsr.material,\n            cameraConfigs.renderWindowId,\n            cameraConfigs.width,\n            cameraConfigs.height,\n            inputColorName,\n            cameraConfigs.nativeWidth,\n            cameraConfigs.nativeHeight,\n            outputColorName);\n    }\n    private _addFsrPass(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & FSRPassConfigs,\n        settings: PipelineSettings,\n        fsrMaterial: Material,\n        id: number,\n        width: number,\n        height: number,\n        inputColorName: string,\n        nativeWidth: number,\n        nativeHeight: number,\n        outputColorName: string,\n    ): rendering.BasicRenderPassBuilder {\n        this._fsrTexSize.x = width;\n        this._fsrTexSize.y = height;\n        this._fsrTexSize.z = nativeWidth;\n        this._fsrTexSize.w = nativeHeight;\n        this._fsrParams.x = clamp(1.0 - settings.fsr.sharpness, 0.02, 0.98);\n\n        const uiColorPrefix = 'UiColor';\n\n        const fsrColorName = getPingPongRenderTarget(outputColorName, uiColorPrefix, id);\n\n        const easuPass = ppl.addRenderPass(nativeWidth, nativeHeight, 'cc-fsr-easu');\n        easuPass.addRenderTarget(fsrColorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\n        easuPass.addTexture(inputColorName, 'outputResultMap');\n        easuPass.setVec4('g_platform', pplConfigs.platform);\n        easuPass.setVec4('fsrTexSize', this._fsrTexSize);\n        easuPass\n            .addQueue(rendering.QueueHint.OPAQUE)\n            .addFullscreenQuad(fsrMaterial, 0);\n\n        const rcasPass = ppl.addRenderPass(nativeWidth, nativeHeight, 'cc-fsr-rcas');\n        rcasPass.addRenderTarget(outputColorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\n        rcasPass.addTexture(fsrColorName, 'outputResultMap');\n        rcasPass.setVec4('g_platform', pplConfigs.platform);\n        rcasPass.setVec4('fsrTexSize', this._fsrTexSize);\n        rcasPass.setVec4('fsrParams', this._fsrParams);\n        rcasPass\n            .addQueue(rendering.QueueHint.OPAQUE)\n            .addFullscreenQuad(fsrMaterial, 1);\n\n        return rcasPass;\n    }\n    // FSR\n    private readonly _fsrParams = new Vec4(0, 0, 0, 0);\n    private readonly _fsrTexSize = new Vec4(0, 0, 0, 0);\n}\n\nexport class BuiltinUiPassBuilder implements rendering.PipelinePassBuilder {\n    getConfigOrder(): number {\n        return 0;\n    }\n    getRenderOrder(): number {\n        return 1000;\n    }\n    setup(\n        ppl: rendering.BasicPipeline,\n        pplConfigs: Readonly<PipelineConfigs>,\n        cameraConfigs: CameraConfigs & FSRPassConfigs,\n        camera: renderer.scene.Camera,\n        context: PipelineContext,\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\n        : rendering.BasicRenderPassBuilder | undefined {\n        assert(!!prevRenderPass);\n\n        let flags = rendering.SceneFlags.UI;\n        if (cameraConfigs.enableProfiler) {\n            flags |= rendering.SceneFlags.PROFILER;\n            prevRenderPass.showStatistics = true;\n        }\n        prevRenderPass\n            .addQueue(rendering.QueueHint.BLEND, 'default', 'default')\n            .addScene(camera, flags);\n\n        return prevRenderPass;\n    }\n}\n\nif (rendering) {\n\n    const { QueueHint, SceneFlags } = rendering;\n\n    class BuiltinPipelineBuilder implements rendering.PipelineBuilder {\n        private readonly _pipelineEvent: PipelineEventProcessor = cclegacy.director.root.pipelineEvent as PipelineEventProcessor;\n        private readonly _forwardPass = new BuiltinForwardPassBuilder();\n        private readonly _bloomPass = new BuiltinBloomPassBuilder();\n        private readonly _toneMappingPass = new BuiltinToneMappingPassBuilder();\n        private readonly _fxaaPass = new BuiltinFXAAPassBuilder();\n        private readonly _fsrPass = new BuiltinFsrPassBuilder();\n        private readonly _uiPass = new BuiltinUiPassBuilder();\n        // Internal cached resources\n        private readonly _clearColor = new Color(0, 0, 0, 1);\n        private readonly _viewport = new Viewport();\n        private readonly _configs = new PipelineConfigs();\n        private readonly _cameraConfigs = new CameraConfigs();\n        // Materials\n        private readonly _copyAndTonemapMaterial = new Material();\n\n        // Internal States\n        private _initialized = false; // TODO(zhouzhenglong): Make default effect asset loading earlier and remove this flag\n        private _passBuilders: rendering.PipelinePassBuilder[] = [];\n\n        private _setupPipelinePreview(\n            camera: renderer.scene.Camera,\n            cameraConfigs: CameraConfigs) {\n            const isEditorView: boolean\n                = camera.cameraUsage === CameraUsage.SCENE_VIEW\n                || camera.cameraUsage === CameraUsage.PREVIEW;\n\n            if (isEditorView) {\n                const editorSettings = rendering.getEditorPipelineSettings() as PipelineSettings | null;\n                if (editorSettings) {\n                    cameraConfigs.settings = editorSettings;\n                } else {\n                    cameraConfigs.settings = defaultSettings;\n                }\n            } else {\n                if (camera.pipelineSettings) {\n                    cameraConfigs.settings = camera.pipelineSettings as PipelineSettings;\n                } else {\n                    cameraConfigs.settings = defaultSettings;\n                }\n            }\n        }\n\n        private _preparePipelinePasses(cameraConfigs: CameraConfigs): void {\n            const passBuilders = this._passBuilders;\n            passBuilders.length = 0;\n\n            const settings = cameraConfigs.settings as PipelineSettings2;\n            if (settings._passes) {\n                for (const pass of settings._passes) {\n                    passBuilders.push(pass);\n                }\n                assert(passBuilders.length === settings._passes.length);\n            }\n\n            passBuilders.push(this._forwardPass);\n\n            if (settings.bloom.enabled) {\n                passBuilders.push(this._bloomPass);\n            }\n\n            passBuilders.push(this._toneMappingPass);\n\n            if (settings.fxaa.enabled) {\n                passBuilders.push(this._fxaaPass);\n            }\n\n            if (settings.fsr.enabled) {\n                passBuilders.push(this._fsrPass);\n            }\n            passBuilders.push(this._uiPass);\n        }\n\n        private _setupBuiltinCameraConfigs(\n            camera: renderer.scene.Camera,\n            pipelineConfigs: PipelineConfigs,\n            cameraConfigs: CameraConfigs\n        ) {\n            const window = camera.window;\n            const isMainGameWindow: boolean = camera.cameraUsage === CameraUsage.GAME && !!window.swapchain;\n\n            // Window\n            cameraConfigs.isMainGameWindow = isMainGameWindow;\n            cameraConfigs.renderWindowId = window.renderWindowId;\n\n            // Camera\n            cameraConfigs.colorName = window.colorName;\n            cameraConfigs.depthStencilName = window.depthStencilName;\n\n            // Pipeline\n            cameraConfigs.enableFullPipeline = (camera.visibility & (Layers.Enum.DEFAULT)) !== 0;\n            cameraConfigs.enableProfiler = DEBUG && isMainGameWindow;\n            cameraConfigs.remainingPasses = 0;\n\n            // Shading scale\n            cameraConfigs.shadingScale = cameraConfigs.settings.shadingScale;\n            cameraConfigs.enableShadingScale = cameraConfigs.settings.enableShadingScale\n                && cameraConfigs.shadingScale !== 1.0;\n\n            cameraConfigs.nativeWidth = Math.max(Math.floor(window.width), 1);\n            cameraConfigs.nativeHeight = Math.max(Math.floor(window.height), 1);\n\n            cameraConfigs.width = cameraConfigs.enableShadingScale\n                ? Math.max(Math.floor(cameraConfigs.nativeWidth * cameraConfigs.shadingScale), 1)\n                : cameraConfigs.nativeWidth;\n            cameraConfigs.height = cameraConfigs.enableShadingScale\n                ? Math.max(Math.floor(cameraConfigs.nativeHeight * cameraConfigs.shadingScale), 1)\n                : cameraConfigs.nativeHeight;\n\n            // Radiance\n            cameraConfigs.enableHDR = cameraConfigs.enableFullPipeline\n                && pipelineConfigs.useFloatOutput;\n            cameraConfigs.radianceFormat = cameraConfigs.enableHDR\n                ? gfx.Format.RGBA16F : gfx.Format.RGBA8;\n\n            // Tone Mapping\n            cameraConfigs.copyAndTonemapMaterial = this._copyAndTonemapMaterial;\n\n            // Depth\n            cameraConfigs.enableStoreSceneDepth = false;\n        }\n\n        private _setupCameraConfigs(\n            camera: renderer.scene.Camera,\n            pipelineConfigs: PipelineConfigs,\n            cameraConfigs: CameraConfigs\n        ): void {\n            this._setupPipelinePreview(camera, cameraConfigs);\n\n            this._preparePipelinePasses(cameraConfigs);\n\n            sortPipelinePassBuildersByConfigOrder(this._passBuilders);\n\n            this._setupBuiltinCameraConfigs(camera, pipelineConfigs, cameraConfigs);\n\n            for (const builder of this._passBuilders) {\n                if (builder.configCamera) {\n                    builder.configCamera(camera, pipelineConfigs, cameraConfigs);\n                }\n            }\n        }\n\n        // ----------------------------------------------------------------\n        // Interface\n        // ----------------------------------------------------------------\n        windowResize(\n            ppl: rendering.BasicPipeline,\n            window: renderer.RenderWindow,\n            camera: renderer.scene.Camera,\n            nativeWidth: number,\n            nativeHeight: number,\n        ): void {\n            setupPipelineConfigs(ppl, this._configs);\n\n            this._setupCameraConfigs(camera, this._configs, this._cameraConfigs);\n\n            // Render Window (UI)\n            const id = window.renderWindowId;\n\n            ppl.addRenderWindow(this._cameraConfigs.colorName,\n                Format.RGBA8, nativeWidth, nativeHeight, window,\n                this._cameraConfigs.depthStencilName);\n\n            const width = this._cameraConfigs.width;\n            const height = this._cameraConfigs.height;\n\n            if (this._cameraConfigs.enableShadingScale) {\n                ppl.addDepthStencil(`ScaledSceneDepth_${id}`, Format.DEPTH_STENCIL, width, height);\n                ppl.addRenderTarget(`ScaledRadiance0_${id}`, this._cameraConfigs.radianceFormat, width, height);\n                ppl.addRenderTarget(`ScaledRadiance1_${id}`, this._cameraConfigs.radianceFormat, width, height);\n                ppl.addRenderTarget(`ScaledLdrColor0_${id}`, Format.RGBA8, width, height);\n                ppl.addRenderTarget(`ScaledLdrColor1_${id}`, Format.RGBA8, width, height);\n            } else {\n                ppl.addDepthStencil(`SceneDepth_${id}`, Format.DEPTH_STENCIL, width, height);\n                ppl.addRenderTarget(`Radiance0_${id}`, this._cameraConfigs.radianceFormat, width, height);\n                ppl.addRenderTarget(`Radiance1_${id}`, this._cameraConfigs.radianceFormat, width, height);\n                ppl.addRenderTarget(`LdrColor0_${id}`, Format.RGBA8, width, height);\n                ppl.addRenderTarget(`LdrColor1_${id}`, Format.RGBA8, width, height);\n            }\n            ppl.addRenderTarget(`UiColor0_${id}`, Format.RGBA8, nativeWidth, nativeHeight);\n            ppl.addRenderTarget(`UiColor1_${id}`, Format.RGBA8, nativeWidth, nativeHeight);\n\n            for (const builder of this._passBuilders) {\n                if (builder.windowResize) {\n                    builder.windowResize(ppl, this._configs, this._cameraConfigs, window, camera, nativeWidth, nativeHeight);\n                }\n            }\n        }\n        setup(cameras: renderer.scene.Camera[], ppl: rendering.BasicPipeline): void {\n            // TODO(zhouzhenglong): Make default effect asset loading earlier and remove _initMaterials\n            if (this._initMaterials(ppl)) {\n                return;\n            }\n            // Render cameras\n            // log(`==================== One Frame ====================`);\n            for (const camera of cameras) {\n                // Skip invalid camera\n                if (!camera.scene || !camera.window) {\n                    continue;\n                }\n                // Setup camera configs\n                this._setupCameraConfigs(camera, this._configs, this._cameraConfigs);\n                // log(`Setup camera: ${camera.node!.name}, window: ${camera.window.renderWindowId}, isFull: ${this._cameraConfigs.enableFullPipeline}, `\n                //     + `size: ${camera.window.width}x${camera.window.height}`);\n\n                this._pipelineEvent.emit(PipelineEventType.RENDER_CAMERA_BEGIN, camera);\n\n                // Build pipeline\n                if (this._cameraConfigs.enableFullPipeline) {\n                    this._buildForwardPipeline(ppl, camera, camera.scene, this._passBuilders);\n                } else {\n                    this._buildSimplePipeline(ppl, camera);\n                }\n\n                this._pipelineEvent.emit(PipelineEventType.RENDER_CAMERA_END, camera);\n            }\n        }\n        // ----------------------------------------------------------------\n        // Pipelines\n        // ----------------------------------------------------------------\n        private _buildSimplePipeline(\n            ppl: rendering.BasicPipeline,\n            camera: renderer.scene.Camera,\n        ): void {\n            const width = Math.max(Math.floor(camera.window.width), 1);\n            const height = Math.max(Math.floor(camera.window.height), 1);\n            const colorName = this._cameraConfigs.colorName;\n            const depthStencilName = this._cameraConfigs.depthStencilName;\n\n            const viewport = camera.viewport;  // Reduce C++/TS interop\n            this._viewport.left = Math.round(viewport.x * width);\n            this._viewport.top = Math.round(viewport.y * height);\n            // Here we must use camera.viewport.width instead of camera.viewport.z, which\n            // is undefined on native platform. The same as camera.viewport.height.\n            this._viewport.width = Math.max(Math.round(viewport.width * width), 1);\n            this._viewport.height = Math.max(Math.round(viewport.height * height), 1);\n\n            const clearColor = camera.clearColor;  // Reduce C++/TS interop\n            this._clearColor.x = clearColor.x;\n            this._clearColor.y = clearColor.y;\n            this._clearColor.z = clearColor.z;\n            this._clearColor.w = clearColor.w;\n\n            const pass = ppl.addRenderPass(width, height, 'default');\n\n            // bind output render target\n            if (forwardNeedClearColor(camera)) {\n                pass.addRenderTarget(colorName, LoadOp.CLEAR, StoreOp.STORE, this._clearColor);\n            } else {\n                pass.addRenderTarget(colorName, LoadOp.LOAD, StoreOp.STORE);\n            }\n\n            // bind depth stencil buffer\n            if (camera.clearFlag & ClearFlagBit.DEPTH_STENCIL) {\n                pass.addDepthStencil(\n                    depthStencilName,\n                    LoadOp.CLEAR,\n                    StoreOp.DISCARD,\n                    camera.clearDepth,\n                    camera.clearStencil,\n                    camera.clearFlag & ClearFlagBit.DEPTH_STENCIL,\n                );\n            } else {\n                pass.addDepthStencil(depthStencilName, LoadOp.LOAD, StoreOp.DISCARD);\n            }\n\n            pass.setViewport(this._viewport);\n\n            // The opaque queue is used for Reflection probe preview\n            pass.addQueue(QueueHint.OPAQUE)\n                .addScene(camera, SceneFlags.OPAQUE);\n\n            // The blend queue is used for UI and Gizmos\n            let flags = SceneFlags.BLEND | SceneFlags.UI;\n            if (this._cameraConfigs.enableProfiler) {\n                flags |= SceneFlags.PROFILER;\n                pass.showStatistics = true;\n            }\n            pass.addQueue(QueueHint.BLEND)\n                .addScene(camera, flags);\n        }\n\n        private _buildForwardPipeline(\n            ppl: rendering.BasicPipeline,\n            camera: renderer.scene.Camera,\n            scene: renderer.RenderScene,\n            passBuilders: rendering.PipelinePassBuilder[],\n        ): void {\n            sortPipelinePassBuildersByRenderOrder(passBuilders);\n\n            const context: PipelineContext = {\n                colorName: '',\n                depthStencilName: '',\n            };\n\n            let lastPass: rendering.BasicRenderPassBuilder | undefined = undefined;\n\n            for (const builder of passBuilders) {\n                if (builder.setup) {\n                    lastPass = builder.setup(ppl, this._configs, this._cameraConfigs,\n                        camera, context, lastPass);\n                }\n            }\n\n            assert(this._cameraConfigs.remainingPasses === 0);\n        }\n\n        private _initMaterials(ppl: rendering.BasicPipeline): number {\n            if (this._initialized) {\n                return 0;\n            }\n\n            setupPipelineConfigs(ppl, this._configs);\n\n            // When add new effect asset, please add its uuid to the dependentAssets in cc.config.json.\n            this._copyAndTonemapMaterial._uuid = `builtin-pipeline-tone-mapping-material`;\n            this._copyAndTonemapMaterial.initialize({ effectName: 'pipeline/post-process/tone-mapping' });\n\n            if (this._copyAndTonemapMaterial.effectAsset) {\n                this._initialized = true;\n            }\n\n            return this._initialized ? 0 : 1;\n        }\n    }\n\n    rendering.setCustomPipeline('Builtin', new BuiltinPipelineBuilder());\n\n} // if (rendering)\n"]}