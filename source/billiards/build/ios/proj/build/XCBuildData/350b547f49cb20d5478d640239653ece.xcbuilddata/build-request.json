{"buildCommand": {"command": "prepareForIndexing", "enableIndexBuildArena": false, "targets": null}, "configuredTargets": [{"guid": "5f7ab2a507f300417eed3c32bfa6500258182e56a9f21d0d41511c6133cefa11"}], "containerPath": "/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/ball.xcodeproj", "continueBuildingAfterErrors": true, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphoneos", "sdk": "iphoneos17.5", "sdkVariant": "iphoneos", "supportedArchitectures": ["arm64v8", "arm64", "armv8"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "", "buildProductsPath": "", "derivedDataPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/ball-ejgevntfyiazubgcpryumlhrhxku/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/ball-ejgevntfyiazubgcpryumlhrhxku/Index.noindex/PrecompiledHeaders", "pchPath": ""}, "configurationName": "Debug", "overrides": {"synthesized": {"table": {"ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone11,6", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "13.6", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPhone11,6", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "NO", "TARGET_DEVICE_IDENTIFIER": "00008020-000624513CE9002E", "TARGET_DEVICE_MODEL": "iPhone11,6", "TARGET_DEVICE_OS_VERSION": "13.6", "TARGET_DEVICE_PLATFORM_NAME": "iphoneos"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}