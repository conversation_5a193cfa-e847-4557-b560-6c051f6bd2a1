#!/bin/sh
set -e
if test "$CONFIGURATION" = "Debug"; then :
  cd /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E echo Running\ swig\ with\ config\ file\ /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/tools/swig-config/scene.i\ ...
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E make_directory /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/mac/bin/swig/bin/swig -c++ -cocos -fvirtual -noexcept -cpperraswarn -D__clang__ -Dfinal= -DCC_PLATFORM=3 -Dconstexpr=const -DCC_PLATFORM_ANDROID=3 -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/mac/bin/swig/share/swig/4.1.0/javascript/cocos -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/mac/bin/swig/share/swig/4.1.0 -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/ -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos -o /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp/jsb_scene_auto.cpp /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/tools/swig-config/scene.i
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E copy_if_different /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp/jsb_scene_auto.cpp /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/jsb_scene_auto.cpp
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E copy_if_different /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp/jsb_scene_auto.h /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/jsb_scene_auto.h
fi
if test "$CONFIGURATION" = "Release"; then :
  cd /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E echo Running\ swig\ with\ config\ file\ /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/tools/swig-config/scene.i\ ...
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E make_directory /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/mac/bin/swig/bin/swig -c++ -cocos -fvirtual -noexcept -cpperraswarn -D__clang__ -Dfinal= -DCC_PLATFORM=3 -Dconstexpr=const -DCC_PLATFORM_ANDROID=3 -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/mac/bin/swig/share/swig/4.1.0/javascript/cocos -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/mac/bin/swig/share/swig/4.1.0 -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/ -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos -o /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp/jsb_scene_auto.cpp /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/tools/swig-config/scene.i
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E copy_if_different /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp/jsb_scene_auto.cpp /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/jsb_scene_auto.cpp
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E copy_if_different /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp/jsb_scene_auto.h /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/jsb_scene_auto.h
fi
if test "$CONFIGURATION" = "MinSizeRel"; then :
  cd /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E echo Running\ swig\ with\ config\ file\ /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/tools/swig-config/scene.i\ ...
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E make_directory /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/mac/bin/swig/bin/swig -c++ -cocos -fvirtual -noexcept -cpperraswarn -D__clang__ -Dfinal= -DCC_PLATFORM=3 -Dconstexpr=const -DCC_PLATFORM_ANDROID=3 -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/mac/bin/swig/share/swig/4.1.0/javascript/cocos -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/mac/bin/swig/share/swig/4.1.0 -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/ -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos -o /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp/jsb_scene_auto.cpp /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/tools/swig-config/scene.i
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E copy_if_different /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp/jsb_scene_auto.cpp /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/jsb_scene_auto.cpp
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E copy_if_different /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp/jsb_scene_auto.h /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/jsb_scene_auto.h
fi
if test "$CONFIGURATION" = "RelWithDebInfo"; then :
  cd /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E echo Running\ swig\ with\ config\ file\ /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/tools/swig-config/scene.i\ ...
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E make_directory /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/mac/bin/swig/bin/swig -c++ -cocos -fvirtual -noexcept -cpperraswarn -D__clang__ -Dfinal= -DCC_PLATFORM=3 -Dconstexpr=const -DCC_PLATFORM_ANDROID=3 -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/mac/bin/swig/share/swig/4.1.0/javascript/cocos -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/mac/bin/swig/share/swig/4.1.0 -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/ -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos -o /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp/jsb_scene_auto.cpp /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/tools/swig-config/scene.i
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E copy_if_different /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp/jsb_scene_auto.cpp /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/jsb_scene_auto.cpp
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E copy_if_different /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/temp/jsb_scene_auto.h /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos/bindings/auto/jsb_scene_auto.h
fi

