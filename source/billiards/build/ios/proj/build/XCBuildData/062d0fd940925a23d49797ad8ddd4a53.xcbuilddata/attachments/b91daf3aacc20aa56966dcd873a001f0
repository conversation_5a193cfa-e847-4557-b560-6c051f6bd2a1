#!/bin/sh
set -e
if test "$CONFIGURATION" = "Debug"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E echo "Generate builtin resources ..."
  /usr/local/bin/node /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cmake/scripts/gen_debugInfos.js /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/../EngineErrorMap.md /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp.in /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp
fi
if test "$CONFIGURATION" = "Release"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E echo "Generate builtin resources ..."
  /usr/local/bin/node /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cmake/scripts/gen_debugInfos.js /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/../EngineErrorMap.md /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp.in /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp
fi
if test "$CONFIGURATION" = "MinSizeRel"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E echo "Generate builtin resources ..."
  /usr/local/bin/node /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cmake/scripts/gen_debugInfos.js /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/../EngineErrorMap.md /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp.in /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp
fi
if test "$CONFIGURATION" = "RelWithDebInfo"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj
  /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -E echo "Generate builtin resources ..."
  /usr/local/bin/node /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cmake/scripts/gen_debugInfos.js /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/../EngineErrorMap.md /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp.in /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/core/builtin/DebugInfos.cpp
fi

