-target arm64-apple-ios12.0 -fpascal-strings -O0 '-DCMAKE_INTDIR="Debug-iphoneos"' '-DGAME_NAME="ball-mobile"' '-DCC_PLATFORM_WINDOWS=2' '-DCC_PLATFORM_MACOS=4' '-DCC_PLATFORM_IOS=1' '-DCC_PLATFORM_MAC_OSX=4' '-DCC_PLATFORM_MAC_IOS=1' '-DCC_PLATFORM_ANDROID=3' '-DCC_PLATFORM_OHOS=5' '-DCC_PLATFORM_LINUX=6' '-DCC_PLATFORM_QNX=7' '-DCC_PLATFORM_NX=8' '-DCC_PLATFORM_OPENHARMONY=10' '-DCC_PLATFORM_EMSCRIPTEN=9' '-DCC_PLATFORM=1' -DBOOST_NO_CXX98_FUNCTION_BASE '-DCC_NETMODE_CLIENT=0' '-DC<PERSON>_NETMODE_LISTEN_SERVER=1' '-DCC_NETMODE_HOST_SERVER=2' '-DCC_NETMODE=0' '-DBOOST_ALL_NO_LIB=1' '-DCC_USE_VIDEO=0' '-DCC_USE_WEBVIEW=0' '-DCC_USE_AUDIO=0' '-DCC_USE_XR=0' '-DCC_USE_XR_REMOTE_PREVIEW=0' '-DCC_USE_SOCKET=1' '-DCC_USE_WEBSOCKET_SERVER=0' '-DCC_USE_EDITBOX=1' '-DUSE_V8_DEBUGGER=1' '-DCC_USE_MIDDLEWARE=1' '-DCC_USE_SPINE=1' '-DCC_USE_DRAGONBONES=1' '-DCC_USE_JOB_SYSTEM_TBB=0' '-DCC_USE_JOB_SYSTEM_TASKFLOW=0' '-DCC_USE_PHYSICS_PHYSX=0' '-DCC_USE_AR_MODULE=0' '-DCC_USE_AR_AUTO=0' '-DCC_USE_AR_CORE=0' '-DCC_USE_AR_ENGINE=0' '-DCC_USE_OCCLUSION_QUERY=0' '-DCC_USE_DEBUG_RENDERER=0' '-DCC_USE_GEOMETRY_RENDERER=0' '-DCC_USE_WEBP=1' '-DCC_EDITOR=0' '-DENABLE_FLOAT_OUTPUT=0' '-DSCRIPT_ENGINE_TYPE=2' '-DCC_DEBUG=1' '-DCC_USE_ADPF=0' '-DCC_USE_GOOGLE_BILLING=0' -DCC_USE_METAL -DBOOST_CONTAINER_NO_LIB -DBOOST_CONTAINER_STATIC_LINK -DBOOST_UUID_FORCE_AUTO_LINK '-DV8_TYPED_ARRAY_MAX_SIZE_IN_HEAP=64' -DENABLE_MINOR_MC -DENABLE_HANDLE_ZAPPING -DV8_ATOMIC_OBJECT_FIELD_WRITES -DV8_ATOMIC_MARKING_STATE -DV8_ENABLE_LAZY_SOURCE_POSITIONS -DV8_SHARED_RO_HEAP -DV8_WIN64_UNWINDING_INFO -DV8_ENABLE_REGEXP_INTERPRETER_THREADED_DISPATCH -DV8_SNAPSHOT_COMPRESSION -DV8_ENABLE_SYSTEM_INSTRUMENTATION -DV8_ENABLE_WEBASSEMBLY -DV8_DEPRECATION_WARNINGS -DV8_IMMINENT_DEPRECATION_WARNINGS -DCPPGC_CAGED_HEAP -DV8_TARGET_ARCH_ARM64 -DV8_HAVE_TARGET_OS -DV8_TARGET_OS_IOS -DDISABLE_UNTRUSTED_CODE_MITIGATIONS '-DOBJC_OLD_DISPATCH_PROTOTYPES=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS17.5.sdk -g -I/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/Debug-iphoneos/include -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources -I/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/native/engine/common/Classes -I/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/native/engine/ios/service -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/SocketRocket -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/ios/include -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/platform -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/renderer/core -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/editor-support -I/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated -I/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/generated/cocos -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/bindings/jswrapper -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/khronos -I/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cocos/platform/ios -isystem /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/ios/include/v8 -isystem /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/ios/include/uv -I/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/ball.build/Debug-iphoneos/ball-mobile.build/DerivedSources-normal/arm64 -I/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/ball.build/Debug-iphoneos/ball-mobile.build/DerivedSources/arm64 -I/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/ball.build/Debug-iphoneos/ball-mobile.build/DerivedSources -F/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/Debug-iphoneos '-std=gnu99'