#!/bin/sh
set -e
if test "$CONFIGURATION" = "Debug"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/CMakeScripts/ReRunCMake.make
fi
if test "$CONFIGURATION" = "Release"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/CMakeScripts/ReRunCMake.make
fi
if test "$CONFIGURATION" = "MinSizeRel"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/CMakeScripts/ReRunCMake.make
fi
if test "$CONFIGURATION" = "RelWithDebInfo"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/CMakeScripts/ReRunCMake.make
fi

